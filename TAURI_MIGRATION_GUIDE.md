# NovaRay: Tauri 2 迁移与架构指南

## 1. 核心原则：键盘优先 (Keyboard-First) & 多窗口原生架构

为了将 NovaRay 打造成一个极致体验的桌面应用，我们将完全拥抱 Tauri 2 的**多窗口架构**，并始终遵循**键盘优先**的设计原则。

-   **主窗口 (Launcher)**: 始终存在的核心窗口，承载主输入框。它的职责是快速响应、搜索、激活插件。为了实现快速启动，这个窗口可以被设置为隐藏而不是关闭，通过全局快捷键快速唤醒。
-   **插件窗口 (Plugin Hosts)**: 每个 `view` 模式的插件都在一个**独立**的、按需创建的 Tauri 窗口中运行。这带来了几个关键优势：
    -   **隔离性**: 插件的崩溃不会影响主应用。
    -   **独立生命周期**: 插件窗口可以独立于主窗口存在、移动和调整大小。
    -   **性能**: 主窗口保持轻量，只在需要时才加载插件的资源。

## 2. 高级命令总线与直接动作 (Advanced Command Bus & Direct Actions)

Web版本中的交互模型将在Tauri中得到完全的原生实现，以获得极致的性能和能力。

-   **中央命令注册表 (Rust)**: 在Rust后端，我们将创建一个中央的、高性能的哈希图（`HashMap`）作为命令注册表。应用启动时，它会扫描所有插件的清单文件，将所有**动作**、**别名**和开发者定义的**快捷键**加载到内存中。
-   **命令发现**:
    -   **直接动作**: 用户的每次输入都会通过Tauri命令（`invoke`）发送到Rust后端进行高效的模式匹配。
    -   **插件浏览**: 用户输入 `/plugins` 命令来浏览和激活已安装的插件。
-   **全局快捷键 (Native)**: Tauri的全局快捷键管理器将直接与Rust后端的命令注册表集成。当用户按下注册的快捷键（例如 `Cmd+Shift+C`），Tauri会捕获该事件，在后端直接触发关联的动作（例如，打开计算器或复制颜色），在某些情况下甚至**完全无需显示任何UI窗口**，实现真正的“一键直达”工作流。

## 3. 窗口间通信：Tauri 事件总线 (Event Bus)

Tauri 的**事件总线**是连接主窗口和插件窗口的唯一官方推荐的神经系统。我们放弃 `postMessage`，全面转向事件驱动模型。

-   **插件视图 (JS) -> Rust 后端**: 当插件视图需要执行一个需要原生权限的操作时（如执行shell命令），它会使用 Tauri 的 JS API 发送一个事件：
    ```javascript
    // 在 /shell 插件的 view/index.html 的 <script> 中
    import { emit } from '@tauri-apps/api/event';

    function runCommand(command) {
      // 向 Rust 后端发送一个全局事件
      emit('plugin:shell:run-command', {
        command: command
      });
    }
    ```

-   **Rust 后端 -> 插件视图 (JS)**: 主应用的 Rust 后端在处理完请求后（例如，收到了 shell 命令的**一行输出**），会向特定的插件窗口发送一个响应事件：
    ```rust
    // 在 src-tauri/src/main.rs
    use tauri::{Manager, AppHandle};

    // 假设我们知道 shell 插件窗口的标签是 "window-shell"
    fn emit_shell_output(app_handle: &AppHandle, line: String) {
      app_handle.emit_to("window-shell", "plugin:shell:output", line).unwrap();
    }
    ```

## 4. `plugin://` 协议的演进

自定义协议依然是加载插件视图UI的核心，但它现在服务于独立的插件窗口，而不是 `iframe`。当用户激活一个 `view` 插件时，Rust后端会创建一个新的窗口，并将其URL设置为 `plugin://{plugin-id}/index.html`。

## 5. 旗舰范例：`/shell` 插件原生化实施

`Shell` 插件是展示此原生架构威力的最佳范例。它的执行流程完美地结合了**事件总线**和**异步Rust**：

1.  **激活**: 用户在主窗口输入 `/shell`，主窗口的Rust后端监听到该指令，创建一个新的、专门用于 `shell` 的窗口（label: `window-shell`），并加载 `plugin://view-shell/index.html`。

2.  **命令执行**:
    -   用户在 `shell` 插件窗口中输入 `ping novaray.dev` 并回车。
    -   插件的 JavaScript 调用 `emit('plugin:shell:run-command', { command: 'ping novaray.dev' })`。

3.  **Rust 后端处理**:
    -   主应用的 Rust 后端始终监听 `plugin:shell:run-command` 事件。
    -   收到事件后，Rust 使用 `tokio::process::Command` 启动一个**异步子进程** (`sh -c "..."`)。
    -   **关键**: Rust 异步地、**逐行地**读取子进程的 `stdout` 和 `stderr` 流。

4.  **流式输出**:
    -   对于读取到的**每一行**输出，Rust 后端立即调用 `app_handle.emit_to("window-shell", "plugin:shell:output", line)` 将该行文本发送回 `shell` 插件窗口。
    -   当命令执行完毕，Rust 后端发送一个 `plugin:shell:command-complete` 事件。

这个流程实现了真正的非阻塞、流式交互，完美地展示了 Tauri 2 架构的强大能力。

## 6. 性能优化策略

-   **Rust 核心**: 利用Rust的高性能和内存安全特性。
-   **原生 Webview**: 使用操作系统提供的原生WebView，减少打包体积和内存占用。
-   **异步化所有操作**: 在 Rust 后端，所有潜在的阻塞操作（文件I/O、网络请求、命令执行）都将使用 `tokio` 进行异步处理。
-   **后端执行繁重任务**: 对于计算密集型或I/O密集型的 `list` 插件，核心逻辑将在 Rust 中执行。
