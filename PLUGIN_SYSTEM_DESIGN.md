# NovaRay 插件系统设计文档

## 0. 核心原则：键盘优先指令 (Keyboard-First Mandate)
为了提供极致的效率，NovaRay 遵循“键盘优先”的设计原则。所有核心功能都必须可以通过键盘直接访问，最大限度地减少用户将手从键盘上移开的需求。

- **命令化交互**: 通过鼠标点击触发操作的UI元素（如按钮栏和上下文菜单）已被废弃。
- **`/plugins` 命令**: 插件的发现和激活通过 `/plugins` 元命令完成，它会在标准结果列表中显示所有可用插件，用户可以使用键盘进行选择。
- **系统级直接动作**: 像 `screenshot`（截图）或 `upload`（上传）这样的核心功能被实现为系统级的直接动作，允许用户通过输入命令直接调用。

---

## 1. 核心理念：直接动作优先 (Action-First)

**直接动作 (Direct Actions)** 和 **命令总线 (Command Bus)** 的概念，将交互模型从“插件优先”演进为“动作优先”。用户不再总是需要通过 `/keyword` 来激活插件，而是可以直接执行插件提供的动作。

### 1.1. 命令总线 (Command Bus)
取代了简单的关键字搜索，命令总线是新的核心。当用户输入时，它会：
1.  **匹配动作**: 优先将输入（如 `= (2+2)*4`）与所有插件注册的动作 `pattern` (正则表达式)进行匹配。
2.  **直接执行**: 如果匹配成功，搜索结果中会展示一个可直接执行的动作。用户回车后，系统将提取参数并立即执行，无需先进入插件。
3.  **别名与关键字回退**: 如果没有匹配到任何动作，命令总线会回退到搜索插件的 `keyword` 或 `aliases` (别名)。

### 1.2. 声明动作与别名
插件开发者在 `plugin.json` 中通过 `actions` 和 `aliases` 字段来声明其能力。

```json
{
  "id": "list-note-adder",
  "name": "Add Note",
  "aliases": ["addnote"],
  "actions": [
    { 
      "id": "action-add-note", 
      "name": "Add Note", 
      "pattern": "^(note|addnote)\\s+(.+)" 
    }
  ]
},
{
  "id": "list-calculator",
  "name": "Calculator",
  "actions": [
    { 
      "id": "action-calculate", 
      "name": "Calculate Expression", 
      "pattern": "^=(.*)" 
    }
  ]
}
```

---

## 2. 双模式架构 (`list` & `view`)

### 2.1. `list` (列表模式)
- **描述**: 插件的核心逻辑（通常在后端实现）接收输入并返回一个结构化的数据列表。主应用负责渲染和交互。现在，它更多地作为**动作的执行者**。
- **适用场景**: 所有“直接动作”的后台处理器。如：计算器、笔记、剪贴板历史。

### 2.2. `view` (视图模式)
- **描述**: 插件提供一个完整的Web UI（HTML/CSS/JS），在独立的、沙箱化的环境中运行。
- **适用场景**: 复杂的自定义界面，如：Shell终端、项目看板、画板。

---
## 3. 设置与插件市场 (Settings & Store)

设置系统和插件市场的设计保持不变，但它们也受益于新的架构，可以被视为具有特定关键字的普通插件。

- **`/settings`**: 一个 `list` 模式插件，其动作是打开 `SettingsView`。
- **`/store`**: 一个 `list` 模式插件，其动作是获取远程插件列表并展示。

---
## 4. `view` 模式旗舰范例：`/shell`
- **功能**: 提供一个功能完备的终端模拟器，是 `view` 模式插件能力的终极展示。
- **UI**: 自包含的HTML界面，拥有命令输入行、历史记录、滚动输出区域，完全模拟原生终端的外观和感觉。
- **通信**:
  - **视图到主应用 (模拟)**: 使用 `postMessage` 发送 `{ type: 'run-shell-command', payload: { command: 'ls -la' } }`。
  - **主应用到视图 (模拟)**: 使用 `iframe.contentWindow.postMessage` 流式返回 `{ type: 'shell-output', payload: '...' }` 和 `{ type: 'command_done' }`。
- **Tauri原生集成**:
  - **多窗口架构**: `/shell` 会在一个独立的 Tauri 窗口中打开。
  - **事件驱动**: 插件窗口通过 Tauri 事件总线 (`emit`) 发送执行命令的请求。
  - **Rust后端**: 主应用的 Rust 后端接收事件，使用 `std::process::Command` 执行命令，并通过事件将 `stdout` 和 `stderr` **流式**返回给插件窗口，实现真正的实时交互。


## 更新日志
- **V7.0**:
  - [x] **键盘优先指令**: 彻底移除了 `AgentBar` 和 `ActionMenu`，实现全键盘操作。
  - [x] **引入 `/plugins` 命令**: 为插件发现和激活提供了新的键盘驱动的工作流。
  - [x] **系统级直接动作**: 将 `screenshot` 等核心功能转化为直接动作。
  - [x] **可访问的设置视图**: 重构设置界面以支持完整的键盘导航。
- **V6.0**:
  - [x] **引入直接动作与命令总线**: 重构搜索和执行逻辑，实现从“插件优先”到“动作优先”的转变，极大提升了交互效率。
  - [x] **为核心插件添加动作**: 为计算器、笔记、主题切换等插件添加了直接动作和别名。
- **V5.0**:
  - [x] **新增Shell插件 (`/shell`)**: 实现了一个功能强大的 `view` 模式终端模拟器，作为Tauri原生能力和多窗口架构的旗舰范例。
  - [x] **扩展设置系统**: 新增了 `'select'` 设置类型，并应用到了Shell插件中，允许用户选择默认的shell。
... (旧日志省略) ...
