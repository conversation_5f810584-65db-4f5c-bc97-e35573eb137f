# 🚀 NovaRay 事件总线测试指南

## 概述

本指南将帮助您完整测试 NovaRay 的事件总线系统，验证前端与后端的事件通信功能。

## 测试环境

### 系统要求
- **Node.js**: 16+ 
- **Rust**: 1.70+
- **Tauri CLI**: 2.0+
- **包管理器**: pnpm (推荐)

### 已安装的依赖
✅ `@tauri-apps/api` - Tauri 前端API  
✅ `@types/react` - React 类型定义  
✅ `@types/react-dom` - React DOM 类型定义  
✅ `@vitejs/plugin-react` - Vite React 插件  

## 🔧 启动测试环境

### 方法 1: 完整 Tauri 环境测试 (推荐)

```bash
# 1. 启动 Tauri 开发服务器
cd /Users/<USER>/Projects/launcher
pnpm tauri dev

# 这将同时启动前端和后端，您可以完整测试事件总线功能
```

### 方法 2: 前端组件测试

```bash
# 1. 启动前端开发服务器
cd /Users/<USER>/Projects/launcher
pnpm dev

# 2. 访问测试页面
# 主应用: http://localhost:5173
# 事件总线测试: http://localhost:5173/test-event-bus.html
```

## 📋 测试步骤

### 1. 基础事件通信测试

#### 1.1 输入事件测试
- [ ] 在"输入事件"字段中输入文本
- [ ] 观察控制台输出事件日志
- [ ] 验证前端事件历史记录更新

**期望结果**: 
```
收到UI事件: {type: "input_changed", payload: {value: "测试文本"}}
```

#### 1.2 搜索结果事件测试
- [ ] 输入一些文本后点击"更新搜索结果"
- [ ] 观察搜索结果数量变化
- [ ] 检查事件历史记录

**期望结果**: 搜索结果事件被触发并记录

#### 1.3 主题切换事件测试
- [ ] 切换主题选项 (浅色/深色/系统)
- [ ] 验证主题变化事件被发送
- [ ] 检查事件历史记录

### 2. 自定义事件测试

#### 2.1 发送自定义事件
- [ ] 在"事件名称"字段输入: `test-custom-event`
- [ ] 在"事件数据"字段输入: `{"message": "Hello from frontend!", "timestamp": 1234567890}`
- [ ] 点击"发送自定义事件"按钮
- [ ] 观察控制台和事件历史

**期望结果**: 自定义事件成功发送到后端

#### 2.2 复杂数据事件测试
- [ ] 测试包含数组、对象的复杂JSON数据
- [ ] 验证数据完整性和序列化

### 3. 插件事件测试

#### 3.1 模拟插件执行
- [ ] 点击"模拟插件执行完成"按钮
- [ ] 观察插件执行完成事件
- [ ] 检查事件payload包含插件ID、动作ID、结果等

**期望结果**: 插件事件被正确处理和记录

### 4. 事件历史管理测试

#### 4.1 历史记录功能
- [ ] 执行多个不同类型的事件
- [ ] 点击"刷新历史"按钮
- [ ] 验证历史记录显示正确
- [ ] 点击"清空历史"按钮
- [ ] 验证历史记录被清空

#### 4.2 事件过滤和限制
- [ ] 触发超过20个事件
- [ ] 验证只显示最近20条记录
- [ ] 检查时间戳排序

### 5. 错误处理测试

#### 5.1 无效JSON数据
- [ ] 在自定义事件数据中输入无效JSON: `{invalid json`
- [ ] 尝试发送事件
- [ ] 验证错误被正确捕获和显示

#### 5.2 网络错误模拟
- [ ] 断开网络连接
- [ ] 尝试发送事件
- [ ] 验证错误处理机制

## 🔍 调试和诊断

### 开发者工具
1. **浏览器控制台**: 查看前端事件日志
2. **Network 标签**: 监控 Tauri 命令调用
3. **Rust 日志**: 检查后端事件处理

### 常见问题诊断

#### 问题: 事件未触发
**解决方案**:
```bash
# 检查 Tauri 后端是否正在运行
ps aux | grep tauri

# 检查端口占用
lsof -i :5173
lsof -i :1420
```

#### 问题: TypeScript 错误
**解决方案**:
```bash
# 重新安装依赖
pnpm install

# 检查类型定义
npx tsc --noEmit
```

#### 问题: 事件总线连接失败
**解决方案**:
```bash
# 检查 Tauri 配置
cat src-tauri/tauri.conf.json

# 重新构建后端
cd src-tauri
cargo build
```

## 📊 测试报告模板

### 测试结果记录
```
日期: ___________
测试人员: ___________
环境: ___________

✅ 基础事件通信测试
  - 输入事件: [ ] 通过 [ ] 失败
  - 搜索结果事件: [ ] 通过 [ ] 失败
  - 主题切换事件: [ ] 通过 [ ] 失败

✅ 自定义事件测试
  - 简单事件: [ ] 通过 [ ] 失败
  - 复杂数据事件: [ ] 通过 [ ] 失败

✅ 插件事件测试
  - 插件执行事件: [ ] 通过 [ ] 失败

✅ 事件历史管理
  - 历史记录: [ ] 通过 [ ] 失败
  - 清空功能: [ ] 通过 [ ] 失败

✅ 错误处理测试
  - 无效数据: [ ] 通过 [ ] 失败
  - 网络错误: [ ] 通过 [ ] 失败

问题和建议:
_________________________________
_________________________________
_________________________________
```

## 🎯 性能基准

### 预期性能指标
- **事件发送延迟**: < 50ms
- **事件历史加载**: < 200ms
- **内存使用**: < 100MB (前端)
- **CPU使用**: < 5% (空闲时)

### 性能测试
```bash
# 监控性能
# 使用浏览器开发者工具的 Performance 标签

# 压力测试: 快速发送100个事件
for i in {1..100}; do
  # 在控制台中快速触发事件
  echo "Event $i sent"
done
```

## 🚀 下一步

测试完成后，您可以：
1. 继续 **Phase 2** 的其他任务
2. 开始 **Phase 3: 窗口管理系统**
3. 集成事件总线到现有插件系统
4. 优化事件系统性能

## 📞 支持

如果遇到问题，请：
1. 检查控制台错误信息
2. 验证所有依赖已正确安装
3. 确认 Tauri 环境配置正确
4. 参考 [Tauri 官方文档](https://tauri.app/zh-cn/)

---

**祝您测试顺利！** 🎉