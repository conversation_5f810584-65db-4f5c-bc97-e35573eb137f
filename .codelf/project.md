## NovaRay Desktop Application

> NovaRay 是一个基于 Tauri 框架构建的现代化桌面应用程序，具有强大的插件系统、事件总线架构和窗口管理功能。

> 该项目旨在提供一个可扩展的桌面应用平台，支持多种插件模式，具备完整的窗口管理和布局系统。

> 项目状态：开发中 - 刚完成窗口管理集成功能

> 项目团队：NovaRay 开发团队

> 技术栈：Tauri + React + TypeScript + Rust，采用事件驱动架构和插件化设计

## Dependencies

### 前端依赖
* @tauri-apps/api: Tauri 前端 API 库
* react (18.x): React 框架
* typescript: TypeScript 支持
* vite: 构建工具
* @vitejs/plugin-react: React Vite 插件

### 后端依赖 (Rust)
* tauri (1.x): 桌面应用框架
* serde: 序列化/反序列化
* tokio: 异步运行时
* uuid: UUID 生成
* chrono: 日期时间处理

## Development Environment

> 开发环境要求：
> - Node.js 16+ with pnpm/npm/yarn
> - Rust 1.70+
> - <PERSON><PERSON> CLI

> 运行项目：
> ```bash
> # 安装依赖
> npm install
> # 启动开发服务器
> npm run tauri dev
> # 构建应用
> npm run tauri build
> ```

## Structure

```
launcher/
├── src/                          # 前端源代码
│   ├── components/              # React 组件
│   │   ├── WindowManagerPanel.tsx    # 窗口管理面板组件 - 提供完整的窗口管理UI
│   │   ├── WindowManagerPanel.css    # 窗口管理面板样式
│   │   ├── EventBusDemo.tsx          # 事件总线演示组件
│   │   ├── ChatInputArea.tsx         # 聊天输入区域
│   │   ├── ResultsList.tsx           # 结果列表组件
│   │   └── ...                       # 其他UI组件
│   ├── core/                    # 核心功能模块
│   │   ├── eventBus.ts              # 事件总线核心实现 - 支持事件订阅发布
│   │   ├── types.ts                 # 类型定义
│   │   ├── pluginRegistry.ts        # 插件注册管理
│   │   └── constants.ts             # 常量定义
│   ├── hooks/                   # React Hooks
│   │   ├── useWindowManager.ts      # 窗口管理 Hook - 提供窗口操作的React接口
│   │   └── useWindowLayoutManager.ts # 窗口布局管理 Hook - 提供布局管理功能
│   ├── plugins/                 # 插件系统
│   │   ├── index.ts                 # 插件注册入口
│   │   ├── window-manager/          # 窗口管理器插件
│   │   │   └── manifest.ts          # 插件清单和实现
│   │   ├── event-bus-test/          # 事件总线测试插件
│   │   └── ...                      # 其他插件
│   ├── App.tsx                  # 主应用组件
│   ├── main.tsx                 # 应用入口点
│   └── vite-env.d.ts           # Vite 环境类型声明
├── src-tauri/                   # 后端 Rust 代码
│   ├── src/
│   │   ├── main.rs                  # 主程序入口
│   │   ├── lib.rs                   # 库入口 - 注册所有命令和模块
│   │   ├── window_manager.rs        # 窗口管理器 - 核心窗口操作功能
│   │   ├── window_layout_manager.rs # 窗口布局管理器 - 多种布局模式支持
│   │   ├── plugin_system.rs         # 插件系统核心
│   │   ├── settings.rs              # 设置管理
│   │   ├── commands/                # Tauri 命令处理器
│   │   │   ├── mod.rs               # 命令模块入口
│   │   │   ├── window_commands.rs   # 窗口操作命令 - 前端调用的窗口管理接口
│   │   │   └── window_layout_commands.rs # 布局操作命令 - 前端调用的布局管理接口
│   │   └── ...
│   ├── Cargo.toml              # Rust 项目配置
│   └── tauri.conf.json         # Tauri 配置文件
├── vite.config.ts              # Vite 配置
├── package.json                # 项目依赖配置
└── README.md                   # 项目说明文档
```

## 核心功能模块

### 1. 窗口管理系统
- **window_manager.rs**: 提供基础窗口操作（创建、关闭、聚焦、最小化、最大化、调整大小、移动、置顶）
- **window_layout_manager.rs**: 实现多种布局模式（Free、Tiled、Stacked、Grid、Tabbed）
- **WindowManagerPanel.tsx**: 完整的窗口管理用户界面

### 2. 事件总线架构
- **eventBus.ts**: 事件订阅发布机制，支持组件间通信
- **EventBusDemo.tsx**: 事件总线功能演示

### 3. 插件系统
- **pluginRegistry.ts**: 插件注册和管理
- **plugins/**: 可扩展的插件生态系统

### 4. 前后端通信
- **commands/**: Tauri 命令处理器，提供前端调用后端的接口
- **hooks/**: React hooks 封装，简化前端调用

## 重要特性

### 窗口管理集成 (最新完成)
- ✅ 完整的窗口生命周期管理
- ✅ 多种布局模式支持
- ✅ 窗口状态同步
- ✅ 焦点管理和导航
- ✅ 网格布局和标签组
- ✅ 布局保存和恢复

### 事件驱动架构
- ✅ 解耦的组件通信
- ✅ 实时状态同步
- ✅ 可扩展的事件系统

### 插件化设计
- ✅ 动态插件加载
- ✅ 插件生命周期管理
- ✅ 插件间通信机制