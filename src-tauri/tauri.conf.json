{"$schema": "https://schema.tauri.app/config/2", "productName": "NovaRay", "version": "0.1.0", "identifier": "com.novaray.launcher", "build": {"frontendDist": "dist", "devUrl": "http://localhost:5173", "beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build"}, "app": {"macOSPrivateApi": true, "windows": [{"label": "main", "title": "NovaRay Launcher", "width": 1000, "height": 800, "minWidth": 800, "minHeight": 80, "maxWidth": 1200, "resizable": true, "fullscreen": false, "decorations": false, "transparent": true, "alwaysOnTop": true, "center": true, "skipTaskbar": true, "titleBarStyle": "Overlay", "hiddenTitle": true, "tabbingIdentifier": "launcher", "visible": true, "maximized": false, "focus": true, "acceptFirstMouse": true, "contentProtected": false}], "security": {"csp": null}, "trayIcon": {"iconPath": "icons/32x32.png", "iconAsTemplate": true, "menuOnLeftClick": false, "tooltip": "NovaRay Launcher"}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "publisher": "NovaRay Team", "copyright": "Copyright © 2025 NovaRay Team. All rights reserved.", "category": "DeveloperTool", "shortDescription": "Modern plugin-based launcher for developers", "longDescription": "NovaRay is a modern, extensible launcher application designed for developers. It features a powerful plugin system, keyboard-first navigation, and seamless integration with development tools."}, "plugins": {"updater": {"active": false}, "globalShortcut": {"shortcuts": {"CommandOrControl+Space": "toggle_launcher", "CommandOrControl+Shift+L": "toggle_launcher"}}}}