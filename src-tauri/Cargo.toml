[package]
name = "novaray"
version = "0.1.0"
description = "Modern plugin-based launcher for developers"
authors = ["NovaRay Team"]
license = "MIT"
repository = "https://github.com/novaray/novaray"
edition = "2021"
rust-version = "1.77.2"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
name = "novaray_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2.3.0", features = ["codegen"] }

[dependencies]
# Core dependencies
serde_json = "1.0"
serde = { version = "1.0", features = ["derive"] }
log = "0.4"
anyhow = "1.0"
tokio = { version = "1.0", features = ["full"] }
uuid = { version = "1.0", features = ["v4", "serde"] }
regex = "1.10"
dirs = "5.0"
chrono = { version = "0.4", features = ["serde"] }
sysinfo = "0.31"

# Tauri core
tauri = { version = "2.6.2", features = [ "macos-private-api", "tray-icon"] }
tauri-plugin-log = "2"
tauri-plugin-fs = "2"
tauri-plugin-shell = "2"
tauri-plugin-window-state = "2"
tauri-plugin-global-shortcut = "2"
tauri-plugin-clipboard-manager = "2"
tauri-plugin-dialog = "2"
tauri-plugin-notification = "2"
tauri-plugin-process = "2"
tauri-plugin-store = "2"

# Database/Storage
rusqlite = { version = "0.32", features = ["bundled"] }

[dev-dependencies]
tempfile = "3.10"
