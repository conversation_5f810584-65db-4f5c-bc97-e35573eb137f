//! 性能监控模块
//!
//! 提供应用程序性能指标的实时收集、存储和分析功能

use anyhow::Result;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::VecDeque;
use std::sync::{Arc, RwLock};
use std::time::{Duration, Instant};
use sysinfo::{Pid, ProcessesToUpdate, System};
use tokio::time::interval;

/// 性能指标数据点
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetric {
    pub timestamp: DateTime<Utc>,
    pub metric_type: MetricType,
    pub value: f64,
    pub unit: String,
    pub metadata: Option<serde_json::Value>,
}

/// 指标类型枚举
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MetricType {
    /// CPU使用率 (%)
    CpuUsage,
    /// 内存使用量 (MB)
    MemoryUsage,
    /// 内存使用率 (%)
    MemoryUsagePercent,
    /// 应用启动时间 (ms)
    AppStartupTime,
    /// 插件加载时间 (ms)
    PluginLoadTime,
    /// 命令响应时间 (ms)
    CommandResponseTime,
    /// 窗口操作时间 (ms)
    WindowOperationTime,
    /// 事件总线延迟 (ms)
    EventBusLatency,
    /// 磁盘使用率 (%)
    DiskUsage,
    /// 网络IO (KB/s)
    NetworkIO,
}

/// 系统性能快照
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemSnapshot {
    pub timestamp: DateTime<Utc>,
    pub cpu_usage: f64,
    pub memory_used: u64,
    pub memory_total: u64,
    pub memory_usage_percent: f64,
    pub process_memory: u64,
    pub process_cpu: f64,
    pub uptime: Duration,
}

/// 应用性能统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppPerformanceStats {
    pub startup_time: Option<Duration>,
    pub total_commands_executed: u64,
    pub average_command_response_time: f64,
    pub total_events_processed: u64,
    pub average_event_latency: f64,
    pub plugins_loaded: u32,
    pub total_plugin_load_time: Duration,
}

/// 性能历史数据容器
#[derive(Debug)]
pub struct PerformanceHistory {
    metrics: VecDeque<PerformanceMetric>,
    max_size: usize,
}

impl PerformanceHistory {
    pub fn new(max_size: usize) -> Self {
        Self {
            metrics: VecDeque::with_capacity(max_size),
            max_size,
        }
    }

    pub fn add_metric(&mut self, metric: PerformanceMetric) {
        if self.metrics.len() >= self.max_size {
            self.metrics.pop_front();
        }
        self.metrics.push_back(metric);
    }

    pub fn get_metrics(&self) -> Vec<PerformanceMetric> {
        self.metrics.iter().cloned().collect()
    }

    pub fn get_metrics_by_type(&self, metric_type: &MetricType) -> Vec<PerformanceMetric> {
        self.metrics
            .iter()
            .filter(|m| {
                std::mem::discriminant(&m.metric_type) == std::mem::discriminant(metric_type)
            })
            .cloned()
            .collect()
    }

    pub fn clear(&mut self) {
        self.metrics.clear();
    }
}

/// 性能监控器
#[derive(Debug)]
pub struct PerformanceMonitor {
    system: Arc<RwLock<System>>,
    history: Arc<RwLock<PerformanceHistory>>,
    app_stats: Arc<RwLock<AppPerformanceStats>>,
    start_time: Instant,
    monitoring_enabled: Arc<RwLock<bool>>,
    process_pid: Option<Pid>,
}

impl PerformanceMonitor {
    /// 创建新的性能监控器实例
    pub fn new() -> Self {
        let mut system = System::new_all();
        system.refresh_all();

        let process_pid = sysinfo::get_current_pid().ok();

        Self {
            system: Arc::new(RwLock::new(system)),
            history: Arc::new(RwLock::new(PerformanceHistory::new(1000))), // 最多保存1000个数据点
            app_stats: Arc::new(RwLock::new(AppPerformanceStats {
                startup_time: None,
                total_commands_executed: 0,
                average_command_response_time: 0.0,
                total_events_processed: 0,
                average_event_latency: 0.0,
                plugins_loaded: 0,
                total_plugin_load_time: Duration::new(0, 0),
            })),
            start_time: Instant::now(),
            monitoring_enabled: Arc::new(RwLock::new(false)),
            process_pid,
        }
    }

    /// 启动性能监控
    pub async fn start_monitoring(&self) -> Result<()> {
        *self.monitoring_enabled.write().unwrap() = true;

        let system = Arc::clone(&self.system);
        let history = Arc::clone(&self.history);
        let enabled = Arc::clone(&self.monitoring_enabled);
        let process_pid = self.process_pid;

        // 启动系统指标收集任务
        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(5)); // 每5秒收集一次

            while *enabled.read().unwrap() {
                interval.tick().await;

                // 刷新系统信息
                if let Ok(mut sys) = system.write() {
                    sys.refresh_cpu_all();
                    sys.refresh_memory();
                    sys.refresh_processes(ProcessesToUpdate::All);

                    let now = Utc::now();

                    // CPU使用率
                    let cpu_usage = sys.global_cpu_usage();
                    if let Ok(mut hist) = history.write() {
                        hist.add_metric(PerformanceMetric {
                            timestamp: now,
                            metric_type: MetricType::CpuUsage,
                            value: cpu_usage as f64,
                            unit: "%".to_string(),
                            metadata: None,
                        });
                    }

                    // 内存使用率
                    let memory_used = sys.used_memory();
                    let memory_total = sys.total_memory();
                    let memory_percent = (memory_used as f64 / memory_total as f64) * 100.0;

                    if let Ok(mut hist) = history.write() {
                        hist.add_metric(PerformanceMetric {
                            timestamp: now,
                            metric_type: MetricType::MemoryUsage,
                            value: memory_used as f64 / 1024.0 / 1024.0, // 转换为MB
                            unit: "MB".to_string(),
                            metadata: None,
                        });

                        hist.add_metric(PerformanceMetric {
                            timestamp: now,
                            metric_type: MetricType::MemoryUsagePercent,
                            value: memory_percent,
                            unit: "%".to_string(),
                            metadata: None,
                        });
                    }

                    // 进程特定指标
                    if let Some(pid) = process_pid {
                        if let Some(process) = sys.process(pid) {
                            let process_memory = process.memory();
                            let process_cpu = process.cpu_usage();

                            if let Ok(mut hist) = history.write() {
                                hist.add_metric(PerformanceMetric {
                                    timestamp: now,
                                    metric_type: MetricType::MemoryUsage,
                                    value: process_memory as f64 / 1024.0 / 1024.0,
                                    unit: "MB".to_string(),
                                    metadata: Some(serde_json::json!({"process": true})),
                                });
                            }
                        }
                    }
                }
            }
        });

        log::info!("性能监控已启动");
        Ok(())
    }

    /// 停止性能监控
    pub fn stop_monitoring(&self) {
        *self.monitoring_enabled.write().unwrap() = false;
        log::info!("性能监控已停止");
    }

    /// 获取当前系统快照
    pub fn get_system_snapshot(&self) -> Result<SystemSnapshot> {
        let mut system = self.system.write().unwrap();
        system.refresh_all();

        let cpu_usage = system.global_cpu_usage();
        let memory_used = system.used_memory();
        let memory_total = system.total_memory();
        let memory_usage_percent = (memory_used as f64 / memory_total as f64) * 100.0;

        let (process_memory, process_cpu) = if let Some(pid) = self.process_pid {
            if let Some(process) = system.process(pid) {
                (process.memory(), process.cpu_usage())
            } else {
                (0, 0.0)
            }
        } else {
            (0, 0.0)
        };

        Ok(SystemSnapshot {
            timestamp: Utc::now(),
            cpu_usage: cpu_usage as f64,
            memory_used,
            memory_total,
            memory_usage_percent,
            process_memory,
            process_cpu: process_cpu as f64,
            uptime: self.start_time.elapsed(),
        })
    }

    /// 获取应用性能统计
    pub fn get_app_stats(&self) -> AppPerformanceStats {
        self.app_stats.read().unwrap().clone()
    }

    /// 获取性能历史数据
    pub fn get_metrics_history(&self) -> Vec<PerformanceMetric> {
        self.history.read().unwrap().get_metrics()
    }

    /// 根据类型获取性能指标
    pub fn get_metrics_by_type(&self, metric_type: MetricType) -> Vec<PerformanceMetric> {
        self.history
            .read()
            .unwrap()
            .get_metrics_by_type(&metric_type)
    }

    /// 记录命令执行时间
    pub fn record_command_execution(&self, duration: Duration, command_name: &str) {
        let mut stats = self.app_stats.write().unwrap();
        stats.total_commands_executed += 1;

        let execution_time = duration.as_millis() as f64;
        stats.average_command_response_time = (stats.average_command_response_time
            * (stats.total_commands_executed - 1) as f64
            + execution_time)
            / stats.total_commands_executed as f64;

        // 添加到历史记录
        let mut history = self.history.write().unwrap();
        history.add_metric(PerformanceMetric {
            timestamp: Utc::now(),
            metric_type: MetricType::CommandResponseTime,
            value: execution_time,
            unit: "ms".to_string(),
            metadata: Some(serde_json::json!({"command": command_name})),
        });
    }

    /// 记录事件处理时间
    pub fn record_event_processing(&self, duration: Duration, event_name: &str) {
        let mut stats = self.app_stats.write().unwrap();
        stats.total_events_processed += 1;

        let processing_time = duration.as_millis() as f64;
        stats.average_event_latency = (stats.average_event_latency
            * (stats.total_events_processed - 1) as f64
            + processing_time)
            / stats.total_events_processed as f64;

        // 添加到历史记录
        let mut history = self.history.write().unwrap();
        history.add_metric(PerformanceMetric {
            timestamp: Utc::now(),
            metric_type: MetricType::EventBusLatency,
            value: processing_time,
            unit: "ms".to_string(),
            metadata: Some(serde_json::json!({"event": event_name})),
        });
    }

    /// 记录插件加载时间
    pub fn record_plugin_load(&self, duration: Duration, plugin_id: &str) {
        let mut stats = self.app_stats.write().unwrap();
        stats.plugins_loaded += 1;
        stats.total_plugin_load_time += duration;

        // 添加到历史记录
        let mut history = self.history.write().unwrap();
        history.add_metric(PerformanceMetric {
            timestamp: Utc::now(),
            metric_type: MetricType::PluginLoadTime,
            value: duration.as_millis() as f64,
            unit: "ms".to_string(),
            metadata: Some(serde_json::json!({"plugin": plugin_id})),
        });
    }

    /// 记录应用启动时间
    pub fn record_startup_time(&self) {
        let startup_time = self.start_time.elapsed();
        self.app_stats.write().unwrap().startup_time = Some(startup_time);

        // 添加到历史记录
        let mut history = self.history.write().unwrap();
        history.add_metric(PerformanceMetric {
            timestamp: Utc::now(),
            metric_type: MetricType::AppStartupTime,
            value: startup_time.as_millis() as f64,
            unit: "ms".to_string(),
            metadata: None,
        });

        log::info!("应用启动时间: {}ms", startup_time.as_millis());
    }

    /// 记录窗口操作时间
    pub fn record_window_operation(&self, duration: Duration, operation: &str, window_id: &str) {
        let mut history = self.history.write().unwrap();
        history.add_metric(PerformanceMetric {
            timestamp: Utc::now(),
            metric_type: MetricType::WindowOperationTime,
            value: duration.as_millis() as f64,
            unit: "ms".to_string(),
            metadata: Some(serde_json::json!({
                "operation": operation,
                "window_id": window_id
            })),
        });
    }

    /// 清除历史数据
    pub fn clear_history(&self) {
        self.history.write().unwrap().clear();
        log::info!("性能监控历史数据已清除");
    }
}

impl Default for PerformanceMonitor {
    fn default() -> Self {
        Self::new()
    }
}
