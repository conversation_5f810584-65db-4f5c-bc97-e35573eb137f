use anyhow::Result;
use dirs;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;

/// 主题设置
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum Theme {
    #[serde(rename = "light")]
    Light,
    #[serde(rename = "dark")]
    Dark,
    #[serde(rename = "system")]
    System,
}

impl Default for Theme {
    fn default() -> Self {
        Theme::System
    }
}

/// 键盘快捷键设置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct KeyboardShortcuts {
    pub toggle_main_window: String,
    pub close_window: String,
    pub next_result: String,
    pub previous_result: String,
    pub select_result: String,
    pub clear_input: String,
    pub open_settings: String,
}

impl Default for KeyboardShortcuts {
    fn default() -> Self {
        Self {
            toggle_main_window: if cfg!(target_os = "macos") {
                "Cmd+Space".to_string()
            } else {
                "Ctrl+Space".to_string()
            },
            close_window: "Escape".to_string(),
            next_result: "ArrowDown".to_string(),
            previous_result: "ArrowUp".to_string(),
            select_result: "Enter".to_string(),
            clear_input: if cfg!(target_os = "macos") {
                "Cmd+K".to_string()
            } else {
                "Ctrl+K".to_string()
            },
            open_settings: if cfg!(target_os = "macos") {
                "Cmd+,".to_string()
            } else {
                "Ctrl+,".to_string()
            },
        }
    }
}

/// 窗口设置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WindowSettings {
    pub main_window_size: (u32, u32),
    pub main_window_position: Option<(i32, i32)>,
    pub always_on_top: bool,
    pub hide_on_blur: bool,
    pub show_in_dock: bool,
    pub transparency: f64,
    pub corner_radius: u32,
}

impl Default for WindowSettings {
    fn default() -> Self {
        Self {
            main_window_size: (900, 700),
            main_window_position: None,
            always_on_top: false,
            hide_on_blur: true,
            show_in_dock: true,
            transparency: 0.95,
            corner_radius: 28,
        }
    }
}

/// 搜索设置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchSettings {
    pub max_results: usize,
    pub show_icons: bool,
    pub search_delay_ms: u64,
    pub fuzzy_search: bool,
    pub case_sensitive: bool,
    pub include_descriptions: bool,
}

impl Default for SearchSettings {
    fn default() -> Self {
        Self {
            max_results: 10,
            show_icons: true,
            search_delay_ms: 100,
            fuzzy_search: true,
            case_sensitive: false,
            include_descriptions: true,
        }
    }
}

/// 插件设置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PluginSettings {
    pub enabled_plugins: Vec<String>,
    pub disabled_plugins: Vec<String>,
    pub plugin_directories: Vec<PathBuf>,
    pub auto_update_plugins: bool,
    pub plugin_timeout_ms: u64,
    pub plugin_specific_settings: HashMap<String, serde_json::Value>,
}

impl Default for PluginSettings {
    fn default() -> Self {
        Self {
            enabled_plugins: vec![
                "calculator".to_string(),
                "shell".to_string(),
                "settings".to_string(),
            ],
            disabled_plugins: Vec::new(),
            plugin_directories: Vec::new(),
            auto_update_plugins: true,
            plugin_timeout_ms: 5000,
            plugin_specific_settings: HashMap::new(),
        }
    }
}

/// 隐私设置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PrivacySettings {
    pub collect_analytics: bool,
    pub collect_crash_reports: bool,
    pub store_search_history: bool,
    pub max_history_entries: usize,
    pub auto_clear_history_days: Option<u32>,
}

impl Default for PrivacySettings {
    fn default() -> Self {
        Self {
            collect_analytics: false,
            collect_crash_reports: true,
            store_search_history: true,
            max_history_entries: 1000,
            auto_clear_history_days: Some(30),
        }
    }
}

/// 高级设置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AdvancedSettings {
    pub debug_mode: bool,
    pub log_level: String,
    pub max_log_file_size_mb: u32,
    pub performance_mode: bool,
    pub experimental_features: bool,
    pub custom_css: Option<String>,
    pub custom_js: Option<String>,
}

impl Default for AdvancedSettings {
    fn default() -> Self {
        Self {
            debug_mode: false,
            log_level: "info".to_string(),
            max_log_file_size_mb: 10,
            performance_mode: false,
            experimental_features: false,
            custom_css: None,
            custom_js: None,
        }
    }
}

/// 应用程序设置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppSettings {
    pub version: String,
    pub theme: Theme,
    pub keyboard_shortcuts: KeyboardShortcuts,
    pub window_settings: WindowSettings,
    pub search_settings: SearchSettings,
    pub plugin_settings: PluginSettings,
    pub privacy_settings: PrivacySettings,
    pub advanced_settings: AdvancedSettings,
    pub custom_settings: HashMap<String, serde_json::Value>,
}

impl Default for AppSettings {
    fn default() -> Self {
        Self {
            version: "0.1.0".to_string(),
            theme: Theme::default(),
            keyboard_shortcuts: KeyboardShortcuts::default(),
            window_settings: WindowSettings::default(),
            search_settings: SearchSettings::default(),
            plugin_settings: PluginSettings::default(),
            privacy_settings: PrivacySettings::default(),
            advanced_settings: AdvancedSettings::default(),
            custom_settings: HashMap::new(),
        }
    }
}

/// 设置管理器
pub struct SettingsManager {
    settings: AppSettings,
    settings_file_path: PathBuf,
    auto_save: bool,
}

impl SettingsManager {
    pub fn new() -> Self {
        let settings_file_path = Self::get_default_settings_file_path();

        let mut manager = Self {
            settings: AppSettings::default(),
            settings_file_path,
            auto_save: true,
        };

        // 尝试加载现有设置
        if let Err(e) = manager.load_settings() {
            log::warn!("无法加载设置文件，使用默认设置: {}", e);
        }

        manager
    }

    /// 获取设置文件路径
    pub fn get_default_settings_file_path() -> PathBuf {
        let mut path = dirs::config_dir().unwrap_or_else(|| PathBuf::from("."));
        path.push("NovaRay");

        // 确保目录存在
        std::fs::create_dir_all(&path).unwrap_or_else(|e| {
            log::error!("无法创建设置目录: {}", e);
        });

        path.push("settings.json");
        path
    }

    /// 加载设置
    pub fn load_settings(&mut self) -> Result<()> {
        if !self.settings_file_path.exists() {
            log::info!("设置文件不存在，使用默认设置");
            return self.save_settings();
        }

        let content = std::fs::read_to_string(&self.settings_file_path)?;
        self.settings = serde_json::from_str(&content)?;

        log::info!("设置已从文件加载: {:?}", self.settings_file_path);
        Ok(())
    }

    /// 保存设置
    pub fn save_settings(&self) -> Result<()> {
        let content = serde_json::to_string_pretty(&self.settings)?;
        std::fs::write(&self.settings_file_path, content)?;

        log::info!("设置已保存到文件: {:?}", self.settings_file_path);
        Ok(())
    }

    /// 获取当前设置
    pub fn get_settings(&self) -> &AppSettings {
        &self.settings
    }

    /// 更新设置
    pub fn update_settings(&mut self, new_settings: AppSettings) -> Result<()> {
        self.settings = new_settings;

        if self.auto_save {
            self.save_settings()?;
        }

        log::info!("设置已更新");
        Ok(())
    }

    /// 重置设置到默认值
    pub fn reset_settings(&mut self) -> Result<()> {
        self.settings = AppSettings::default();

        if self.auto_save {
            self.save_settings()?;
        }

        log::info!("设置已重置为默认值");
        Ok(())
    }

    /// 获取特定设置值
    pub fn get_setting<T>(&self, key: &str) -> Option<T>
    where
        T: for<'de> Deserialize<'de>,
    {
        match key {
            "theme" => serde_json::to_value(&self.settings.theme)
                .ok()
                .and_then(|v| serde_json::from_value(v).ok()),
            "keyboard_shortcuts" => serde_json::to_value(&self.settings.keyboard_shortcuts)
                .ok()
                .and_then(|v| serde_json::from_value(v).ok()),
            "window_settings" => serde_json::to_value(&self.settings.window_settings)
                .ok()
                .and_then(|v| serde_json::from_value(v).ok()),
            "search_settings" => serde_json::to_value(&self.settings.search_settings)
                .ok()
                .and_then(|v| serde_json::from_value(v).ok()),
            "plugin_settings" => serde_json::to_value(&self.settings.plugin_settings)
                .ok()
                .and_then(|v| serde_json::from_value(v).ok()),
            "privacy_settings" => serde_json::to_value(&self.settings.privacy_settings)
                .ok()
                .and_then(|v| serde_json::from_value(v).ok()),
            "advanced_settings" => serde_json::to_value(&self.settings.advanced_settings)
                .ok()
                .and_then(|v| serde_json::from_value(v).ok()),
            _ => {
                // 查找自定义设置
                self.settings
                    .custom_settings
                    .get(key)
                    .and_then(|v| serde_json::from_value(v.clone()).ok())
            }
        }
    }

    /// 设置特定设置值
    pub fn set_setting<T>(&mut self, key: &str, value: T) -> Result<()>
    where
        T: Serialize,
    {
        match key {
            "theme" => {
                let theme_value = serde_json::to_value(&value)?;
                self.settings.theme = serde_json::from_value(theme_value)?;
            }
            "keyboard_shortcuts" => {
                let shortcuts_value = serde_json::to_value(&value)?;
                self.settings.keyboard_shortcuts = serde_json::from_value(shortcuts_value)?;
            }
            "window_settings" => {
                let window_value = serde_json::to_value(&value)?;
                self.settings.window_settings = serde_json::from_value(window_value)?;
            }
            "search_settings" => {
                let search_value = serde_json::to_value(&value)?;
                self.settings.search_settings = serde_json::from_value(search_value)?;
            }
            "plugin_settings" => {
                let plugin_value = serde_json::to_value(&value)?;
                self.settings.plugin_settings = serde_json::from_value(plugin_value)?;
            }
            "privacy_settings" => {
                let privacy_value = serde_json::to_value(&value)?;
                self.settings.privacy_settings = serde_json::from_value(privacy_value)?;
            }
            "advanced_settings" => {
                let advanced_value = serde_json::to_value(&value)?;
                self.settings.advanced_settings = serde_json::from_value(advanced_value)?;
            }
            _ => {
                // 设置自定义设置
                let custom_value = serde_json::to_value(&value)?;
                self.settings
                    .custom_settings
                    .insert(key.to_string(), custom_value);
            }
        }

        if self.auto_save {
            self.save_settings()?;
        }

        log::info!("设置项已更新: {}", key);
        Ok(())
    }

    /// 删除自定义设置
    pub fn remove_custom_setting(&mut self, key: &str) -> Result<()> {
        if self.settings.custom_settings.remove(key).is_some() {
            if self.auto_save {
                self.save_settings()?;
            }
            log::info!("自定义设置已删除: {}", key);
        }

        Ok(())
    }

    /// 获取插件特定设置
    pub fn get_plugin_setting<T>(&self, plugin_id: &str, key: &str) -> Option<T>
    where
        T: for<'de> Deserialize<'de>,
    {
        self.settings
            .plugin_settings
            .plugin_specific_settings
            .get(plugin_id)
            .and_then(|plugin_settings| {
                if let serde_json::Value::Object(map) = plugin_settings {
                    map.get(key)
                        .and_then(|v| serde_json::from_value(v.clone()).ok())
                } else {
                    None
                }
            })
    }

    /// 设置插件特定设置
    pub fn set_plugin_setting<T>(&mut self, plugin_id: &str, key: &str, value: T) -> Result<()>
    where
        T: Serialize,
    {
        let plugin_settings = self
            .settings
            .plugin_settings
            .plugin_specific_settings
            .entry(plugin_id.to_string())
            .or_insert_with(|| serde_json::Value::Object(serde_json::Map::new()));

        if let serde_json::Value::Object(map) = plugin_settings {
            map.insert(key.to_string(), serde_json::to_value(&value)?);
        }

        if self.auto_save {
            self.save_settings()?;
        }

        log::info!("插件设置已更新: {} -> {}", plugin_id, key);
        Ok(())
    }

    /// 导出设置
    pub fn export_settings(&self) -> Result<String> {
        serde_json::to_string_pretty(&self.settings)
            .map_err(|e| anyhow::anyhow!("导出设置失败: {}", e))
    }

    /// 导入设置
    pub fn import_settings(&mut self, settings_json: &str) -> Result<()> {
        let new_settings: AppSettings = serde_json::from_str(settings_json)?;
        self.update_settings(new_settings)?;

        log::info!("设置已从 JSON 导入");
        Ok(())
    }

    /// 启用/禁用自动保存
    pub fn set_auto_save(&mut self, enabled: bool) {
        self.auto_save = enabled;
        log::info!("自动保存已{}", if enabled { "启用" } else { "禁用" });
    }

    /// 获取设置文件路径
    pub fn get_settings_file_path(&self) -> &PathBuf {
        &self.settings_file_path
    }

    /// 验证设置
    pub fn validate_settings(&self) -> Result<()> {
        // 验证主题设置
        match self.settings.theme {
            Theme::Light | Theme::Dark | Theme::System => {}
        }

        // 验证窗口设置
        if self.settings.window_settings.main_window_size.0 < 400
            || self.settings.window_settings.main_window_size.1 < 300
        {
            return Err(anyhow::anyhow!("窗口大小太小"));
        }

        if self.settings.window_settings.transparency < 0.1
            || self.settings.window_settings.transparency > 1.0
        {
            return Err(anyhow::anyhow!("透明度值无效"));
        }

        // 验证搜索设置
        if self.settings.search_settings.max_results == 0 {
            return Err(anyhow::anyhow!("最大结果数不能为零"));
        }

        log::info!("设置验证通过");
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::tempdir;

    #[test]
    fn test_app_settings_default() {
        let settings = AppSettings::default();
        assert_eq!(settings.version, "0.1.0");
        assert_eq!(settings.theme, Theme::System);
        assert_eq!(settings.search_settings.max_results, 10);
        assert!(!settings.advanced_settings.debug_mode);
    }

    #[test]
    fn test_settings_manager_basic() {
        let temp_dir = tempdir().unwrap();
        let settings_file_path = temp_dir.path().join("settings.json");

        let mut manager = SettingsManager {
            settings: AppSettings::default(),
            settings_file_path,
            auto_save: false, // 禁用自动保存以避免测试污染
        };

        // 测试获取设置
        let theme = manager.get_setting::<Theme>("theme").unwrap();
        assert_eq!(theme, Theme::System);

        // 测试设置更新
        manager.set_setting("theme", Theme::Dark).unwrap();
        let updated_theme = manager.get_setting::<Theme>("theme").unwrap();
        assert_eq!(updated_theme, Theme::Dark);
    }

    #[test]
    fn test_plugin_settings() {
        let mut manager = SettingsManager::new();

        // 测试插件设置
        manager
            .set_plugin_setting("test_plugin", "enabled", true)
            .unwrap();
        let enabled = manager
            .get_plugin_setting::<bool>("test_plugin", "enabled")
            .unwrap();
        assert!(enabled);

        manager
            .set_plugin_setting("test_plugin", "max_items", 20)
            .unwrap();
        let max_items = manager
            .get_plugin_setting::<i32>("test_plugin", "max_items")
            .unwrap();
        assert_eq!(max_items, 20);
    }
}
