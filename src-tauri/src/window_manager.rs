use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager, WebviewWindow, WebviewWindowBuilder};

/// 窗口类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum WindowType {
    /// 主启动器窗口
    Main,
    /// 插件视图窗口
    Plugin { plugin_id: String, view_url: String },
    /// 设置窗口
    Settings,
    /// 浮动窗口
    Floating { title: String, size: (u32, u32) },
}

/// 窗口配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WindowConfig {
    pub window_type: WindowType,
    pub title: String,
    pub width: u32,
    pub height: u32,
    pub resizable: bool,
    pub decorations: bool,
    pub transparent: bool,
    pub always_on_top: bool,
    pub center: bool,
    pub skip_taskbar: bool,
    pub url: Option<String>,
}

impl WindowConfig {
    /// 创建主窗口配置
    pub fn main() -> Self {
        Self {
            window_type: WindowType::Main,
            title: "NovaRay".to_string(),
            width: 900,
            height: 700,
            resizable: true,
            decorations: false,
            transparent: true,
            always_on_top: false,
            center: true,
            skip_taskbar: false,
            url: None,
        }
    }

    /// 创建插件窗口配置
    pub fn plugin(plugin_id: String, view_url: String, title: String) -> Self {
        Self {
            window_type: WindowType::Plugin {
                plugin_id,
                view_url: view_url.clone(),
            },
            title,
            width: 1200,
            height: 800,
            resizable: true,
            decorations: true,
            transparent: false,
            always_on_top: false,
            center: true,
            skip_taskbar: false,
            url: Some(view_url),
        }
    }

    /// 创建设置窗口配置
    pub fn settings() -> Self {
        Self {
            window_type: WindowType::Settings,
            title: "NovaRay Settings".to_string(),
            width: 1000,
            height: 700,
            resizable: true,
            decorations: true,
            transparent: false,
            always_on_top: false,
            center: true,
            skip_taskbar: false,
            url: None,
        }
    }

    /// 创建浮动窗口配置
    pub fn floating(title: String, size: (u32, u32)) -> Self {
        Self {
            window_type: WindowType::Floating {
                title: title.clone(),
                size,
            },
            title,
            width: size.0,
            height: size.1,
            resizable: true,
            decorations: true,
            transparent: false,
            always_on_top: true,
            center: true,
            skip_taskbar: true,
            url: None,
        }
    }
}

/// 窗口状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WindowState {
    pub id: String,
    pub config: WindowConfig,
    pub visible: bool,
    pub focused: bool,
    pub minimized: bool,
    pub maximized: bool,
    pub position: Option<(i32, i32)>,
    pub size: Option<(u32, u32)>,
    pub created_at: u64,
    pub last_activity: u64,
}

impl WindowState {
    pub fn new(id: String, config: WindowConfig) -> Self {
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_millis() as u64;

        Self {
            id,
            config,
            visible: false,
            focused: false,
            minimized: false,
            maximized: false,
            position: None,
            size: None,
            created_at: now,
            last_activity: now,
        }
    }

    pub fn update_activity(&mut self) {
        self.last_activity = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_millis() as u64;
    }
}

/// 窗口管理器
pub struct WindowManager {
    app_handle: Option<AppHandle>,
    windows: HashMap<String, WindowState>,
    plugin_windows: HashMap<String, String>, // plugin_id -> window_id
}

impl WindowManager {
    pub fn new() -> Self {
        Self {
            app_handle: None,
            windows: HashMap::new(),
            plugin_windows: HashMap::new(),
        }
    }

    /// 初始化窗口管理器
    pub async fn initialize(&mut self, app_handle: AppHandle) {
        self.app_handle = Some(app_handle);
        log::info!("窗口管理器已初始化");

        // 初始化主窗口状态
        let main_config = WindowConfig::main();
        let main_state = WindowState::new("main".to_string(), main_config);
        self.windows.insert("main".to_string(), main_state);
    }

    /// 创建窗口
    pub async fn create_window(&mut self, window_id: String, config: WindowConfig) -> Result<()> {
        let app_handle = self
            .app_handle
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("应用句柄未初始化"))?;

        // 检查窗口是否已存在
        if let Some(existing_window) = app_handle.get_webview_window(&window_id) {
            // 如果窗口已存在，显示并聚焦
            existing_window.show()?;
            existing_window.set_focus()?;

            // 更新窗口状态
            if let Some(state) = self.windows.get_mut(&window_id) {
                state.visible = true;
                state.focused = true;
                state.update_activity();
            }

            return Ok(());
        }

        // 创建窗口构建器
        let mut window_builder = WebviewWindowBuilder::new(
            app_handle,
            &window_id,
            tauri::WebviewUrl::App("index.html".into()),
        )
        .title(&config.title)
        .inner_size(config.width as f64, config.height as f64)
        .resizable(config.resizable)
        .decorations(config.decorations)
        .always_on_top(config.always_on_top)
        .center()
        .skip_taskbar(config.skip_taskbar);

        // 如果有自定义 URL，使用它
        if let Some(url) = &config.url {
            window_builder = WebviewWindowBuilder::new(
                app_handle,
                &window_id,
                tauri::WebviewUrl::External(url.parse()?),
            )
            .title(&config.title)
            .inner_size(config.width as f64, config.height as f64)
            .resizable(config.resizable)
            .decorations(config.decorations)
            .always_on_top(config.always_on_top)
            .center()
            .skip_taskbar(config.skip_taskbar);
        }

        // 构建窗口
        let window = window_builder.build()?;

        // 设置窗口事件监听器
        self.setup_window_events(&window_id, &window).await?;

        // 创建窗口状态
        let mut window_state = WindowState::new(window_id.clone(), config.clone());
        window_state.visible = true;
        window_state.focused = true;

        // 如果是插件窗口，记录映射关系
        if let WindowType::Plugin { plugin_id, .. } = &config.window_type {
            self.plugin_windows
                .insert(plugin_id.clone(), window_id.clone());
        }

        self.windows.insert(window_id.clone(), window_state);

        log::info!("窗口已创建: {} ({})", window_id, config.title);
        Ok(())
    }

    /// 设置窗口事件监听器
    async fn setup_window_events(&mut self, window_id: &str, window: &WebviewWindow) -> Result<()> {
        let window_id = window_id.to_string();

        // 监听窗口关闭事件
        let window_id_clone = window_id.clone();
        window.on_window_event(move |event| match event {
            tauri::WindowEvent::CloseRequested { .. } => {
                log::info!("窗口关闭请求: {}", window_id_clone);
            }
            tauri::WindowEvent::Focused(focused) => {
                log::debug!("窗口焦点变化: {} -> {}", window_id_clone, focused);
            }
            tauri::WindowEvent::Resized(size) => {
                log::debug!("窗口大小变化: {} -> {:?}", window_id_clone, size);
            }
            tauri::WindowEvent::Moved(position) => {
                log::debug!("窗口位置变化: {} -> {:?}", window_id_clone, position);
            }
            _ => {}
        });

        Ok(())
    }

    /// 关闭窗口
    pub async fn close_window(&mut self, window_id: &str) -> Result<()> {
        let app_handle = self
            .app_handle
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("应用句柄未初始化"))?;

        if let Some(window) = app_handle.get_webview_window(window_id) {
            window.close()?;
        }

        // 移除窗口状态
        self.windows.remove(window_id);

        // 如果是插件窗口，移除映射关系
        self.plugin_windows.retain(|_, v| v != window_id);

        log::info!("窗口已关闭: {}", window_id);
        Ok(())
    }

    /// 显示窗口
    pub async fn show_window(&mut self, window_id: &str) -> Result<()> {
        let app_handle = self
            .app_handle
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("应用句柄未初始化"))?;

        if let Some(window) = app_handle.get_webview_window(window_id) {
            window.show()?;
            window.set_focus()?;

            // 更新窗口状态
            if let Some(state) = self.windows.get_mut(window_id) {
                state.visible = true;
                state.focused = true;
                state.update_activity();
            }
        }

        Ok(())
    }

    /// 隐藏窗口
    pub async fn hide_window(&mut self, window_id: &str) -> Result<()> {
        let app_handle = self
            .app_handle
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("应用句柄未初始化"))?;

        if let Some(window) = app_handle.get_webview_window(window_id) {
            window.hide()?;

            // 更新窗口状态
            if let Some(state) = self.windows.get_mut(window_id) {
                state.visible = false;
                state.focused = false;
                state.update_activity();
            }
        }

        Ok(())
    }

    /// 切换窗口显示状态
    pub async fn toggle_window(&mut self, window_id: &str) -> Result<()> {
        let app_handle = self
            .app_handle
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("应用句柄未初始化"))?;

        if let Some(window) = app_handle.get_webview_window(window_id) {
            let is_visible = window.is_visible().unwrap_or(false);

            if is_visible {
                self.hide_window(window_id).await?;
            } else {
                self.show_window(window_id).await?;
            }
        }

        Ok(())
    }

    /// 创建插件窗口
    pub async fn create_plugin_window(
        &mut self,
        plugin_id: String,
        view_url: String,
        title: String,
    ) -> Result<String> {
        let window_id = format!("plugin_{}", plugin_id);
        let config = WindowConfig::plugin(plugin_id.clone(), view_url, title);

        self.create_window(window_id.clone(), config).await?;

        Ok(window_id)
    }

    /// 关闭插件窗口
    pub async fn close_plugin_window(&mut self, plugin_id: &str) -> Result<()> {
        if let Some(window_id) = self.plugin_windows.get(plugin_id) {
            let window_id = window_id.clone();
            self.close_window(&window_id).await?;
        }

        Ok(())
    }

    /// 获取窗口状态
    pub fn get_window_state(&self, window_id: &str) -> Option<&WindowState> {
        self.windows.get(window_id)
    }

    /// 获取所有窗口状态
    pub fn get_all_windows(&self) -> Vec<&WindowState> {
        self.windows.values().collect()
    }

    /// 获取活动窗口
    pub fn get_active_window(&self) -> Option<&WindowState> {
        self.windows
            .values()
            .filter(|state| state.visible && state.focused)
            .max_by_key(|state| state.last_activity)
    }

    /// 聚焦主窗口
    pub async fn focus_main_window(&mut self) -> Result<()> {
        self.show_window("main").await
    }

    /// 最小化所有窗口
    pub async fn minimize_all_windows(&mut self) -> Result<()> {
        let app_handle = self
            .app_handle
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("应用句柄未初始化"))?;

        for window_id in self.windows.keys() {
            if let Some(window) = app_handle.get_webview_window(window_id) {
                let _ = window.minimize();
            }
        }

        Ok(())
    }

    /// 关闭所有插件窗口
    pub async fn close_all_plugin_windows(&mut self) -> Result<()> {
        let plugin_windows: Vec<String> = self.plugin_windows.values().cloned().collect();

        for window_id in plugin_windows {
            self.close_window(&window_id).await?;
        }

        Ok(())
    }

    /// 获取窗口统计信息
    pub fn get_window_stats(&self) -> HashMap<String, serde_json::Value> {
        let mut stats = HashMap::new();

        let total_windows = self.windows.len();
        let visible_windows = self.windows.values().filter(|s| s.visible).count();
        let focused_windows = self.windows.values().filter(|s| s.focused).count();
        let plugin_windows = self.plugin_windows.len();

        stats.insert(
            "total_windows".to_string(),
            serde_json::Value::Number(total_windows.into()),
        );
        stats.insert(
            "visible_windows".to_string(),
            serde_json::Value::Number(visible_windows.into()),
        );
        stats.insert(
            "focused_windows".to_string(),
            serde_json::Value::Number(focused_windows.into()),
        );
        stats.insert(
            "plugin_windows".to_string(),
            serde_json::Value::Number(plugin_windows.into()),
        );

        stats
    }

    /// 最小化窗口
    pub async fn minimize_window(&mut self, window_id: &str) -> Result<()> {
        let app_handle = self
            .app_handle
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("应用句柄未初始化"))?;

        if let Some(window) = app_handle.get_webview_window(window_id) {
            window.minimize()?;

            // 更新窗口状态
            if let Some(state) = self.windows.get_mut(window_id) {
                state.minimized = true;
                state.visible = false;
                state.focused = false;
                state.update_activity();
            }
        }

        Ok(())
    }

    /// 最大化窗口
    pub async fn maximize_window(&mut self, window_id: &str) -> Result<()> {
        let app_handle = self
            .app_handle
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("应用句柄未初始化"))?;

        if let Some(window) = app_handle.get_webview_window(window_id) {
            window.maximize()?;

            // 更新窗口状态
            if let Some(state) = self.windows.get_mut(window_id) {
                state.maximized = true;
                state.minimized = false;
                state.visible = true;
                state.focused = true;
                state.update_activity();
            }
        }

        Ok(())
    }

    /// 取消最大化窗口
    pub async fn unmaximize_window(&mut self, window_id: &str) -> Result<()> {
        let app_handle = self
            .app_handle
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("应用句柄未初始化"))?;

        if let Some(window) = app_handle.get_webview_window(window_id) {
            window.unmaximize()?;

            // 更新窗口状态
            if let Some(state) = self.windows.get_mut(window_id) {
                state.maximized = false;
                state.minimized = false;
                state.visible = true;
                state.focused = true;
                state.update_activity();
            }
        }

        Ok(())
    }

    /// 设置窗口大小
    pub async fn resize_window(&mut self, window_id: &str, width: u32, height: u32) -> Result<()> {
        let app_handle = self
            .app_handle
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("应用句柄未初始化"))?;

        if let Some(window) = app_handle.get_webview_window(window_id) {
            window.set_size(tauri::Size::Physical(tauri::PhysicalSize::new(
                width, height,
            )))?;

            // 更新窗口状态
            if let Some(state) = self.windows.get_mut(window_id) {
                state.size = Some((width, height));
                state.update_activity();
            }
        }

        Ok(())
    }

    /// 设置窗口位置
    pub async fn move_window(&mut self, window_id: &str, x: i32, y: i32) -> Result<()> {
        let app_handle = self
            .app_handle
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("应用句柄未初始化"))?;

        if let Some(window) = app_handle.get_webview_window(window_id) {
            window.set_position(tauri::Position::Physical(tauri::PhysicalPosition::new(
                x, y,
            )))?;

            // 更新窗口状态
            if let Some(state) = self.windows.get_mut(window_id) {
                state.position = Some((x, y));
                state.update_activity();
            }
        }

        Ok(())
    }

    /// 设置窗口总在最前
    pub async fn set_always_on_top(&mut self, window_id: &str, always_on_top: bool) -> Result<()> {
        let app_handle = self
            .app_handle
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("应用句柄未初始化"))?;

        if let Some(window) = app_handle.get_webview_window(window_id) {
            window.set_always_on_top(always_on_top)?;

            // 更新窗口配置
            if let Some(state) = self.windows.get_mut(window_id) {
                state.config.always_on_top = always_on_top;
                state.update_activity();
            }
        }

        Ok(())
    }

    /// 获取窗口的物理信息
    pub async fn get_window_info(&self, window_id: &str) -> Result<Option<WindowInfo>> {
        let app_handle = self
            .app_handle
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("应用句柄未初始化"))?;

        if let Some(window) = app_handle.get_webview_window(window_id) {
            let position = window.outer_position().ok();
            let size = window.outer_size().ok();
            let is_maximized = window.is_maximized().unwrap_or(false);
            let is_minimized = window.is_minimized().unwrap_or(false);
            let is_visible = window.is_visible().unwrap_or(false);
            let is_focused = window.is_focused().unwrap_or(false);

            let window_info = WindowInfo {
                id: window_id.to_string(),
                position: position.map(|p| (p.x, p.y)),
                size: size.map(|s| (s.width, s.height)),
                is_maximized,
                is_minimized,
                is_visible,
                is_focused,
            };

            return Ok(Some(window_info));
        }

        Ok(None)
    }

    /// 同步窗口状态
    pub async fn sync_window_state(&mut self, window_id: &str) -> Result<()> {
        if let Some(window_info) = self.get_window_info(window_id).await? {
            if let Some(state) = self.windows.get_mut(window_id) {
                state.position = window_info.position;
                state.size = window_info.size;
                state.maximized = window_info.is_maximized;
                state.minimized = window_info.is_minimized;
                state.visible = window_info.is_visible;
                state.focused = window_info.is_focused;
                state.update_activity();
            }
        }

        Ok(())
    }

    /// 同步所有窗口状态
    pub async fn sync_all_window_states(&mut self) -> Result<()> {
        let window_ids: Vec<String> = self.windows.keys().cloned().collect();

        for window_id in window_ids {
            if let Err(e) = self.sync_window_state(&window_id).await {
                log::warn!("同步窗口状态失败: {} -> {}", window_id, e);
            }
        }

        Ok(())
    }
}

/// 窗口信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WindowInfo {
    pub id: String,
    pub position: Option<(i32, i32)>,
    pub size: Option<(u32, u32)>,
    pub is_maximized: bool,
    pub is_minimized: bool,
    pub is_visible: bool,
    pub is_focused: bool,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_window_manager_basic() {
        let mut manager = WindowManager::new();

        // 测试窗口配置创建
        let main_config = WindowConfig::main();
        assert_eq!(main_config.title, "NovaRay");
        assert_eq!(main_config.width, 900);
        assert_eq!(main_config.height, 700);

        let plugin_config = WindowConfig::plugin(
            "test_plugin".to_string(),
            "https://example.com".to_string(),
            "Test Plugin".to_string(),
        );
        assert_eq!(plugin_config.title, "Test Plugin");

        // 测试窗口状态创建
        let window_state = WindowState::new("test".to_string(), main_config);
        assert_eq!(window_state.id, "test");
        assert!(!window_state.visible);
        assert!(!window_state.focused);
    }
}
