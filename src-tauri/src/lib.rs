use std::collections::HashMap;
use std::sync::Arc;
use tauri::{Listener, Manager};
use tokio::sync::RwLock;

mod commands;
mod event_bus;
mod launcher_manager;
mod layout_engine;
mod performance_monitor;
mod plugin_manifest;
mod plugin_system;
mod settings;
#[cfg(test)]
mod test_plugin_system;
mod window_layout_manager;
mod window_manager;

use commands::layout_engine_commands::LayoutEngineState;
use event_bus::EventBus;
use launcher_manager::LauncherManager;
use performance_monitor::PerformanceMonitor;
use plugin_system::PluginManager;
use settings::SettingsManager;
use window_layout_manager::WindowLayoutManager;
use window_manager::WindowManager;

/// 应用程序的核心状态
#[derive(Clone)]
pub struct AppState {
    pub plugin_manager: Arc<RwLock<PluginManager>>,
    pub event_bus: Arc<EventBus>,
    pub window_manager: Arc<RwLock<WindowManager>>,
    pub window_layout_manager: Arc<RwLock<WindowLayoutManager>>,
    pub settings_manager: Arc<RwLock<SettingsManager>>,
    pub layout_engine_state: LayoutEngineState,
    pub performance_monitor: Arc<RwLock<PerformanceMonitor>>,
    pub launcher_manager: LauncherManager,
}

impl AppState {
    pub fn new() -> Self {
        Self {
            plugin_manager: Arc::new(RwLock::new(PluginManager::new())),
            event_bus: Arc::new(EventBus::new()),
            window_manager: Arc::new(RwLock::new(WindowManager::new())),
            window_layout_manager: Arc::new(RwLock::new(WindowLayoutManager::new())),
            settings_manager: Arc::new(RwLock::new(SettingsManager::new())),
            layout_engine_state: LayoutEngineState::new(),
            performance_monitor: Arc::new(RwLock::new(PerformanceMonitor::new())),
            launcher_manager: LauncherManager::new(),
        }
    }
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(
            tauri_plugin_log::Builder::default()
                .level(log::LevelFilter::Info)
                .build(),
        )
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_shell::init())
        .plugin(tauri_plugin_window_state::Builder::default().build())
        .plugin(tauri_plugin_global_shortcut::Builder::new().build())
        .plugin(tauri_plugin_clipboard_manager::init())
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_notification::init())
        .plugin(tauri_plugin_process::init())
        .plugin(tauri_plugin_store::Builder::default().build())
        .setup(|app| {
            let app_state = AppState::new();

            // 注册全局应用状态
            app.manage(app_state.clone());
            app.manage(app_state.layout_engine_state.clone());
            app.manage(app_state.launcher_manager.clone());

            // 初始化窗口管理器
            let window_manager = app_state.window_manager.clone();
            let window_layout_manager = app_state.window_layout_manager.clone();
            let app_handle = app.handle().clone();

            tauri::async_runtime::spawn(async move {
                let mut wm = window_manager.write().await;
                wm.initialize(app_handle.clone()).await;

                let mut wlm = window_layout_manager.write().await;
                wlm.initialize(app_handle.clone()).await;

                // 启动性能监控
                let performance_monitor = app_state.performance_monitor.clone();
                let monitor = performance_monitor.read().await;
                if let Err(e) = monitor.start_monitoring().await {
                    log::error!("启动性能监控失败: {}", e);
                } else {
                    // 记录应用启动完成
                    monitor.record_startup_time();
                }
            });

            // 注册全局快捷键
            setup_global_shortcuts(app.handle())?;

            log::info!("NovaRay 应用程序初始化完成");
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            commands::launcher_commands::toggle_launcher,
            commands::launcher_commands::show_launcher,
            commands::launcher_commands::hide_launcher,
            commands::launcher_commands::get_launcher_state,
            commands::launcher_commands::set_launcher_size,
            commands::launcher_commands::handle_launcher_blur,
            commands::launcher_commands::create_plugin_window,
            commands::launcher_commands::close_plugin_window,
            commands::launcher_commands::get_plugin_windows,
            commands::plugin_commands::search_plugins,
            commands::plugin_commands::execute_plugin_action,
            commands::plugin_commands::get_plugin_list,
            commands::plugin_commands::install_plugin,
            commands::plugin_commands::uninstall_plugin,
            commands::window_commands::create_window,
            commands::window_commands::focus_main_window,
            commands::window_commands::show_window,
            commands::window_commands::hide_window,
            commands::window_commands::toggle_window,
            commands::window_commands::minimize_window,
            commands::window_commands::maximize_window,
            commands::window_commands::unmaximize_window,
            commands::window_commands::resize_window,
            commands::window_commands::move_window,
            commands::window_commands::set_always_on_top,
            commands::window_commands::get_window_state,
            commands::window_commands::get_all_windows,
            commands::window_commands::get_window_info,
            commands::window_commands::sync_window_state,
            commands::window_commands::sync_all_window_states,
            commands::window_commands::close_all_plugin_windows,
            commands::window_commands::get_window_stats,
            commands::window_layout_commands::set_window_layout_config,
            commands::window_layout_commands::get_window_layout_config,
            commands::window_layout_commands::add_window_to_layout,
            commands::window_layout_commands::remove_window_from_layout,
            commands::window_layout_commands::switch_layout_type,
            commands::window_layout_commands::focus_window_in_layout,
            commands::window_layout_commands::get_focused_window,
            commands::window_layout_commands::cycle_focus_windows,
            commands::window_layout_commands::get_current_layout,
            commands::window_layout_commands::get_all_layouts,
            commands::window_layout_commands::create_layout,
            commands::window_layout_commands::delete_layout,
            commands::window_layout_commands::switch_layout,
            commands::window_layout_commands::get_layout_stats,
            commands::window_layout_commands::apply_current_layout,
            commands::window_layout_commands::get_window_layouts,
            commands::window_layout_commands::arrange_windows,
            commands::window_layout_commands::focus_next_window,
            commands::window_layout_commands::focus_previous_window,
            commands::settings_commands::get_settings,
            commands::settings_commands::update_settings,
            commands::settings_commands::reset_settings,
            commands::event_commands::emit_event,
            commands::event_commands::subscribe_to_events,
            commands::event_commands::get_event_history,
            commands::event_commands::clear_event_history,
            commands::event_commands::emit_system_event,
            commands::event_commands::emit_ui_event,
            commands::event_commands::emit_plugin_event,
            commands::layout_engine_commands::update_screen_info,
            commands::layout_engine_commands::apply_auto_tile_layout,
            commands::layout_engine_commands::apply_layout_template,
            commands::layout_engine_commands::create_layout_template,
            commands::layout_engine_commands::get_layout_templates,
            commands::layout_engine_commands::get_popular_templates,
            commands::layout_engine_commands::set_layout_constraints,
            commands::layout_engine_commands::get_layout_constraints,
            commands::layout_engine_commands::set_layout_animation,
            commands::layout_engine_commands::get_layout_animation,
            commands::layout_engine_commands::calculate_animation_frame,
            commands::layout_engine_commands::get_layout_performance_stats,
            commands::layout_engine_commands::cleanup_performance_cache,
            commands::layout_engine_commands::get_screen_info,
            commands::layout_engine_commands::preview_layout,
            commands::layout_engine_commands::recommend_layout,
            commands::layout_engine_commands::optimize_current_layout,
            commands::performance_commands::start_performance_monitoring,
            commands::performance_commands::stop_performance_monitoring,
            commands::performance_commands::get_system_snapshot,
            commands::performance_commands::get_app_performance_stats,
            commands::performance_commands::get_performance_history,
            commands::performance_commands::get_metrics_by_type,
            commands::performance_commands::clear_performance_history,
            commands::performance_commands::record_startup_complete,
            commands::performance_commands::get_monitoring_status,
            commands::performance_commands::export_performance_data,
            commands::performance_commands::run_performance_benchmark,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

/// 设置全局快捷键
fn setup_global_shortcuts(app_handle: &tauri::AppHandle) -> Result<(), Box<dyn std::error::Error>> {
    use tauri_plugin_global_shortcut::GlobalShortcutExt;

    // 监听全局快捷键事件
    let app_handle_clone = app_handle.clone();
    app_handle.listen("toggle_launcher", move |_| {
        let app_handle = app_handle_clone.clone();
        tauri::async_runtime::spawn(async move {
            if let Some(launcher_manager) = app_handle.try_state::<LauncherManager>() {
                if let Err(e) = launcher_manager.toggle_launcher(&app_handle).await {
                    log::error!("切换启动器失败: {}", e);
                }
            }
        });
    });

    // 定义快捷键
    let primary_shortcut = "CommandOrControl+Space";
    let secondary_shortcut = "CommandOrControl+Shift+L";

    // 注册快捷键（通过配置文件中的plugin配置，快捷键会触发"toggle_launcher"事件）
    if let Err(e) = app_handle.global_shortcut().register(primary_shortcut) {
        log::warn!(
            "无法注册主快捷键 {}: {}。这可能是因为系统快捷键冲突。",
            primary_shortcut,
            e
        );
    } else {
        log::info!("主快捷键 {} 注册成功", primary_shortcut);
    }

    // 注册备用快捷键
    if let Err(e) = app_handle.global_shortcut().register(secondary_shortcut) {
        log::warn!("无法注册备用快捷键 {}: {}。", secondary_shortcut, e);
    } else {
        log::info!("备用快捷键 {} 注册成功", secondary_shortcut);
    }

    let shortcut_names = if cfg!(target_os = "macos") {
        "Cmd+Space (或 Cmd+Shift+L)"
    } else {
        "Ctrl+Space (或 Ctrl+Shift+L)"
    };

    log::info!("全局快捷键已配置: {}", shortcut_names);
    Ok(())
}
