use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{broadcast, RwLock};
use uuid::Uuid;

/// 事件类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EventType {
    /// 插件相关事件
    Plugin(PluginEvent),
    /// 窗口相关事件
    Window(WindowEvent),
    /// 系统相关事件
    System(SystemEvent),
    /// 用户界面事件
    UI(UIEvent),
    /// 自定义事件
    Custom(CustomEvent),
}

/// 插件事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PluginEvent {
    /// 插件执行完成
    ExecutionComplete {
        plugin_id: String,
        action_id: String,
        result: String,
        success: bool,
    },
    /// 插件安装
    Installed {
        plugin_id: String,
        version: String,
    },
    /// 插件卸载
    Uninstalled {
        plugin_id: String,
    },
    /// 插件启用/禁用
    ToggleEnabled {
        plugin_id: String,
        enabled: bool,
    },
}

/// 窗口事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum WindowEvent {
    /// 窗口创建
    Created {
        window_id: String,
        window_type: String,
    },
    /// 窗口关闭
    Closed {
        window_id: String,
    },
    /// 窗口显示/隐藏
    VisibilityChanged {
        window_id: String,
        visible: bool,
    },
    /// 窗口聚焦
    Focused {
        window_id: String,
    },
    /// 窗口失去焦点
    Blurred {
        window_id: String,
    },
}

/// 系统事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SystemEvent {
    /// 全局快捷键触发
    GlobalShortcut {
        shortcut: String,
    },
    /// 剪贴板变化
    ClipboardChanged {
        content: String,
    },
    /// 文件变化
    FileChanged {
        path: String,
        event_type: String,
    },
    /// 应用程序启动
    AppStarted,
    /// 应用程序退出
    AppExit,
}

/// 用户界面事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum UIEvent {
    /// 输入值变化
    InputChanged {
        value: String,
    },
    /// 搜索结果变化
    SearchResultsChanged {
        results: Vec<String>,
    },
    /// 主题变化
    ThemeChanged {
        theme: String,
    },
    /// 设置变化
    SettingsChanged {
        setting_key: String,
        new_value: String,
    },
}

/// 自定义事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CustomEvent {
    pub event_name: String,
    pub payload: serde_json::Value,
    pub source: String,
    pub target: Option<String>,
}

/// 事件消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EventMessage {
    pub id: String,
    pub timestamp: u64,
    pub event_type: EventType,
    pub source: String,
    pub target: Option<String>,
    pub metadata: HashMap<String, String>,
}

impl EventMessage {
    pub fn new(event_type: EventType, source: String) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_millis() as u64,
            event_type,
            source,
            target: None,
            metadata: HashMap::new(),
        }
    }
    
    pub fn with_target(mut self, target: String) -> Self {
        self.target = Some(target);
        self
    }
    
    pub fn with_metadata(mut self, key: String, value: String) -> Self {
        self.metadata.insert(key, value);
        self
    }
}

/// 事件监听器
pub type EventListener = Arc<dyn Fn(EventMessage) -> Result<(), Box<dyn std::error::Error + Send + Sync>> + Send + Sync>;

/// 事件总线
pub struct EventBus {
    /// 广播通道发送器
    sender: broadcast::Sender<EventMessage>,
    /// 事件监听器
    listeners: Arc<RwLock<HashMap<String, Vec<EventListener>>>>,
    /// 事件历史记录
    history: Arc<RwLock<Vec<EventMessage>>>,
}

impl EventBus {
    /// 创建新的事件总线
    pub fn new() -> Self {
        let (sender, _) = broadcast::channel(1000);
        
        Self {
            sender,
            listeners: Arc::new(RwLock::new(HashMap::new())),
            history: Arc::new(RwLock::new(Vec::new())),
        }
    }
    
    /// 发送事件
    pub async fn emit(&self, event: EventMessage) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // 添加到历史记录
        {
            let mut history = self.history.write().await;
            history.push(event.clone());
            
            // 限制历史记录数量
            if history.len() > 1000 {
                history.remove(0);
            }
        }
        
        // 触发监听器
        let listeners = self.listeners.read().await;
        let event_type_key = self.get_event_type_key(&event.event_type);
        
        if let Some(event_listeners) = listeners.get(&event_type_key) {
            for listener in event_listeners {
                if let Err(e) = listener(event.clone()) {
                    log::error!("事件监听器执行失败: {}", e);
                }
            }
        }
        
        // 广播事件
        if let Err(e) = self.sender.send(event.clone()) {
            log::error!("事件广播失败: {}", e);
        }
        
        log::debug!("事件已发送: {} ({})", event.id, event_type_key);
        Ok(())
    }
    
    /// 订阅事件
    pub async fn subscribe(&self, event_pattern: &str, listener: EventListener) -> String {
        let mut listeners = self.listeners.write().await;
        let listener_id = Uuid::new_v4().to_string();
        
        listeners.entry(event_pattern.to_string())
            .or_insert_with(Vec::new)
            .push(listener);
        
        log::debug!("已订阅事件: {} (监听器 ID: {})", event_pattern, listener_id);
        listener_id
    }
    
    /// 取消订阅事件
    pub async fn unsubscribe(&self, event_pattern: &str, listener_id: &str) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut listeners = self.listeners.write().await;
        
        if let Some(event_listeners) = listeners.get_mut(event_pattern) {
            // 这里简化处理，实际应用中需要更复杂的监听器管理
            log::debug!("取消订阅事件: {} (监听器 ID: {})", event_pattern, listener_id);
        }
        
        Ok(())
    }
    
    /// 获取事件订阅器
    pub fn get_subscriber(&self) -> broadcast::Receiver<EventMessage> {
        self.sender.subscribe()
    }
    
    /// 获取事件历史记录
    pub async fn get_history(&self, limit: Option<usize>) -> Vec<EventMessage> {
        let history = self.history.read().await;
        let limit = limit.unwrap_or(100);
        
        if history.len() > limit {
            history[history.len() - limit..].to_vec()
        } else {
            history.clone()
        }
    }
    
    /// 清空事件历史记录
    pub async fn clear_history(&self) {
        let mut history = self.history.write().await;
        history.clear();
        log::info!("事件历史记录已清空");
    }
    
    /// 获取事件类型键
    fn get_event_type_key(&self, event_type: &EventType) -> String {
        match event_type {
            EventType::Plugin(_) => "plugin".to_string(),
            EventType::Window(_) => "window".to_string(),
            EventType::System(_) => "system".to_string(),
            EventType::UI(_) => "ui".to_string(),
            EventType::Custom(custom) => format!("custom.{}", custom.event_name),
        }
    }
    
    /// 发送插件事件
    pub async fn emit_plugin_event(&self, plugin_event: PluginEvent, source: String) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let event = EventMessage::new(EventType::Plugin(plugin_event), source);
        self.emit(event).await
    }
    
    /// 发送窗口事件
    pub async fn emit_window_event(&self, window_event: WindowEvent, source: String) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let event = EventMessage::new(EventType::Window(window_event), source);
        self.emit(event).await
    }
    
    /// 发送系统事件
    pub async fn emit_system_event(&self, system_event: SystemEvent, source: String) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let event = EventMessage::new(EventType::System(system_event), source);
        self.emit(event).await
    }
    
    /// 发送用户界面事件
    pub async fn emit_ui_event(&self, ui_event: UIEvent, source: String) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let event = EventMessage::new(EventType::UI(ui_event), source);
        self.emit(event).await
    }
    
    /// 发送自定义事件
    pub async fn emit_custom_event(&self, custom_event: CustomEvent, source: String) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let event = EventMessage::new(EventType::Custom(custom_event), source);
        self.emit(event).await
    }
}

/// 事件总线扩展 trait
pub trait EventBusExt {
    /// 快速发送简单事件
    fn emit_simple(&self, event_name: &str, payload: serde_json::Value, source: String) -> impl std::future::Future<Output = Result<(), Box<dyn std::error::Error + Send + Sync>>> + Send;
    
    /// 发送带目标的事件
    fn emit_targeted(&self, event_name: &str, payload: serde_json::Value, source: String, target: String) -> impl std::future::Future<Output = Result<(), Box<dyn std::error::Error + Send + Sync>>> + Send;
}

impl EventBusExt for EventBus {
    async fn emit_simple(&self, event_name: &str, payload: serde_json::Value, source: String) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let custom_event = CustomEvent {
            event_name: event_name.to_string(),
            payload,
            source: source.clone(),
            target: None,
        };
        
        self.emit_custom_event(custom_event, source).await
    }
    
    async fn emit_targeted(&self, event_name: &str, payload: serde_json::Value, source: String, target: String) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let custom_event = CustomEvent {
            event_name: event_name.to_string(),
            payload,
            source: source.clone(),
            target: Some(target.clone()),
        };
        
        let event = EventMessage::new(EventType::Custom(custom_event), source)
            .with_target(target);
        
        self.emit(event).await
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::time::{sleep, Duration};
    
    #[tokio::test]
    async fn test_event_bus_basic() {
        let event_bus = EventBus::new();
        
        // 测试发送事件
        let event = EventMessage::new(
            EventType::System(SystemEvent::AppStarted),
            "test".to_string()
        );
        
        let result = event_bus.emit(event).await;
        assert!(result.is_ok());
        
        // 测试历史记录
        let history = event_bus.get_history(Some(10)).await;
        assert_eq!(history.len(), 1);
    }
    
    #[tokio::test]
    async fn test_event_subscription() {
        let event_bus = EventBus::new();
        let received_events = Arc::new(RwLock::new(Vec::new()));
        let received_events_clone = received_events.clone();
        
        // 订阅事件
        let listener: EventListener = Arc::new(move |event| {
            let received_events = received_events_clone.clone();
            tokio::spawn(async move {
                let mut events = received_events.write().await;
                events.push(event);
            });
            Ok(())
        });
        
        event_bus.subscribe("system", listener).await;
        
        // 发送事件
        event_bus.emit_system_event(
            SystemEvent::AppStarted,
            "test".to_string()
        ).await.unwrap();
        
        // 等待异步处理
        sleep(Duration::from_millis(100)).await;
        
        let events = received_events.read().await;
        assert_eq!(events.len(), 1);
    }
}