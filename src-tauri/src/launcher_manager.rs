//! 启动器管理器
//! 负责管理主启动器窗口的显示/隐藏逻辑，以及全局快捷键处理

use std::sync::Arc;
use tauri::{App<PERSON><PERSON><PERSON>, Emitter, Manager, PhysicalPosition, PhysicalSize, WebviewWindow};
use tokio::sync::RwLock;

/// 启动器管理器状态
#[derive(Debug, Clone)]
pub struct LauncherState {
    pub is_visible: bool,
    pub position: Option<PhysicalPosition<i32>>,
    pub size: PhysicalSize<u32>,
}

impl Default for LauncherState {
    fn default() -> Self {
        Self {
            is_visible: false,
            position: None,
            size: PhysicalSize::new(1000, 800),
        }
    }
}

/// 启动器管理器
#[derive(Clone)]
pub struct LauncherManager {
    state: Arc<RwLock<LauncherState>>,
}

impl LauncherManager {
    /// 创建新的启动器管理器
    pub fn new() -> Self {
        Self {
            state: Arc::new(RwLock::new(LauncherState::default())),
        }
    }

    /// 切换启动器可见性
    pub async fn toggle_launcher(&self, app_handle: &AppHandle) -> Result<(), String> {
        let mut state = self.state.write().await;

        if let Some(window) = app_handle.get_webview_window("main") {
            if state.is_visible {
                // 隐藏启动器
                self.hide_launcher_internal(&window, &mut state).await?;
            } else {
                // 显示启动器
                self.show_launcher_internal(&window, &mut state).await?;
            }
        } else {
            return Err("无法找到主窗口".to_string());
        }

        Ok(())
    }

    /// 显示启动器
    pub async fn show_launcher(&self, app_handle: &AppHandle) -> Result<(), String> {
        let mut state = self.state.write().await;

        if let Some(window) = app_handle.get_webview_window("main") {
            if !state.is_visible {
                self.show_launcher_internal(&window, &mut state).await?;
            }
        } else {
            return Err("无法找到主窗口".to_string());
        }

        Ok(())
    }

    /// 隐藏启动器
    pub async fn hide_launcher(&self, app_handle: &AppHandle) -> Result<(), String> {
        let mut state = self.state.write().await;

        if let Some(window) = app_handle.get_webview_window("main") {
            if state.is_visible {
                self.hide_launcher_internal(&window, &mut state).await?;
            }
        } else {
            return Err("无法找到主窗口".to_string());
        }

        Ok(())
    }

    /// 获取启动器状态
    pub async fn get_state(&self) -> LauncherState {
        self.state.read().await.clone()
    }

    /// 设置启动器大小（动态调整）
    pub async fn set_launcher_size(
        &self,
        app_handle: &AppHandle,
        height: u32,
    ) -> Result<(), String> {
        let mut state = self.state.write().await;

        if let Some(window) = app_handle.get_webview_window("main") {
            let new_size = PhysicalSize::new(1000, height);
            window.set_size(new_size).map_err(|e| e.to_string())?;
            state.size = new_size;

            // 重新居中窗口
            if state.is_visible {
                self.center_launcher(&window).await?;
            }
        } else {
            return Err("无法找到主窗口".to_string());
        }

        Ok(())
    }

    /// 内部方法：显示启动器
    async fn show_launcher_internal(
        &self,
        window: &WebviewWindow,
        state: &mut LauncherState,
    ) -> Result<(), String> {
        // 居中窗口
        self.center_launcher(window).await?;

        // 设置窗口属性
        window.set_always_on_top(true).map_err(|e| e.to_string())?;
        window.set_skip_taskbar(true).map_err(|e| e.to_string())?;

        // 先显示窗口再设置焦点
        window.show().map_err(|e| e.to_string())?;

        // 延迟一点设置焦点，确保窗口完全显示
        tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;
        window.set_focus().map_err(|e| e.to_string())?;

        state.is_visible = true;

        // 发送显示事件
        window
            .emit("launcher:shown", ())
            .map_err(|e| e.to_string())?;

        Ok(())
    }

    /// 内部方法：隐藏启动器
    async fn hide_launcher_internal(
        &self,
        window: &WebviewWindow,
        state: &mut LauncherState,
    ) -> Result<(), String> {
        // 保存当前位置
        if let Ok(position) = window.outer_position() {
            state.position = Some(position);
        }

        window.hide().map_err(|e| e.to_string())?;
        state.is_visible = false;

        // 发送隐藏事件
        window
            .emit("launcher:hidden", ())
            .map_err(|e| e.to_string())?;

        Ok(())
    }

    /// 居中启动器窗口
    async fn center_launcher(&self, window: &WebviewWindow) -> Result<(), String> {
        if let Ok(monitor) = window.current_monitor() {
            if let Some(monitor) = monitor {
                let monitor_size = monitor.size();
                let window_size = window.outer_size().map_err(|e| e.to_string())?;

                let x = (monitor_size.width as i32 - window_size.width as i32) / 2;
                let y = (monitor_size.height as i32 - window_size.height as i32) / 2; // 居中显示

                let position = PhysicalPosition::new(x, y);
                window.set_position(position).map_err(|e| e.to_string())?;

                log::info!(
                    "窗口已居中: {}x{} at ({}, {})",
                    window_size.width,
                    window_size.height,
                    x,
                    y
                );
            }
        }

        Ok(())
    }

    /// 处理窗口失焦事件（自动隐藏）
    pub async fn handle_blur(&self, app_handle: &AppHandle) -> Result<(), String> {
        // 延迟检查焦点，避免误触发（增加延迟时间）
        tokio::time::sleep(tokio::time::Duration::from_millis(300)).await;

        if let Some(window) = app_handle.get_webview_window("main") {
            // 检查窗口是否仍然可见
            let state = self.state.read().await;
            if !state.is_visible {
                return Ok(()); // 窗口已经隐藏，不需要处理
            }

            if window.is_focused().unwrap_or(false) {
                return Ok(()); // 窗口仍然有焦点，不隐藏
            }

            // 检查是否有插件窗口活动
            let windows = app_handle.webview_windows();
            for (label, win) in windows {
                if label != "main" && win.is_focused().unwrap_or(false) {
                    return Ok(()); // 有插件窗口活动，不隐藏主窗口
                }
            }

            // 只有在确实失去焦点且没有其他活动窗口时才隐藏
            drop(state); // 释放读锁
            self.hide_launcher(app_handle).await?;
        }

        Ok(())
    }
}

impl Default for LauncherManager {
    fn default() -> Self {
        Self::new()
    }
}
