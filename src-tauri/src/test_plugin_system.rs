//! 插件系统测试模块
use crate::plugin_system::PluginManager;
use tokio;

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_plugin_manager_creation() {
        let manager = PluginManager::new();
        let plugins = manager.get_plugin_list();

        // 验证内置插件是否正确加载
        assert_eq!(plugins.len(), 3); // calculator, shell, settings

        // 验证插件名称
        let plugin_names: Vec<String> = plugins.iter().map(|p| p.name.clone()).collect();
        assert!(plugin_names.contains(&"Calculator".to_string()));
        assert!(plugin_names.contains(&"Shell Terminal".to_string()));
        assert!(plugin_names.contains(&"Settings".to_string()));

        println!("✅ 插件管理器创建测试通过");
    }

    #[tokio::test]
    async fn test_plugin_search() {
        let manager = PluginManager::new();

        // 测试搜索功能
        let results = manager.search("calc");
        assert!(!results.is_empty());

        let first_result = &results[0];
        assert!(
            first_result.name.contains("Calculator") || first_result.name.contains("Calculate")
        );

        println!("✅ 插件搜索测试通过");
    }

    #[tokio::test]
    async fn test_plugin_execution() {
        let manager = PluginManager::new();

        // 测试计算器插件执行
        let result = manager
            .execute_plugin_action("calculator", "calculate", "2 + 3")
            .await;
        assert!(result.is_ok());

        let execution_result = result.unwrap();
        assert!(execution_result.success);
        assert_eq!(execution_result.result, "5");

        println!("✅ 插件执行测试通过");
    }

    #[tokio::test]
    async fn test_typescript_plugin_scanning() {
        let mut manager = PluginManager::new();

        // 测试 TypeScript 插件扫描
        let result = manager.scan_typescript_plugins().await;

        // 即使没有 TypeScript 插件，扫描也应该成功
        match result {
            Ok(_) => println!("✅ TypeScript 插件扫描测试通过"),
            Err(e) => println!("⚠️ TypeScript 插件扫描失败: {} (可能是因为没有插件目录)", e),
        }
    }

    #[tokio::test]
    async fn test_plugin_toggle() {
        let mut manager = PluginManager::new();

        // 测试禁用插件
        let result = manager.toggle_plugin("calculator", false);
        assert!(result.is_ok());

        // 验证插件被禁用
        let plugin = manager.get_plugin("calculator").unwrap();
        assert!(!plugin.enabled);

        // 重新启用
        let result = manager.toggle_plugin("calculator", true);
        assert!(result.is_ok());

        let plugin = manager.get_plugin("calculator").unwrap();
        assert!(plugin.enabled);

        println!("✅ 插件启用/禁用测试通过");
    }
}

/// 运行所有插件系统测试
pub async fn run_plugin_tests() {
    println!("🚀 开始运行插件系统测试...");

    // 创建插件管理器
    let mut manager = PluginManager::new();

    // 测试基本功能
    println!("📋 测试插件列表:");
    let plugins = manager.get_plugin_list();
    for plugin in &plugins {
        println!(
            "  - {} ({}): {}",
            plugin.name, plugin.id, plugin.description
        );
    }

    // 测试搜索
    println!("\n🔍 测试搜索功能:");
    let search_results = manager.search("calc");
    for result in &search_results {
        println!("  - {} (分数: {:.2})", result.name, result.score);
    }

    // 测试执行
    println!("\n⚡ 测试插件执行:");
    match manager
        .execute_plugin_action("calculator", "calculate", "10 + 5")
        .await
    {
        Ok(result) => println!("  计算结果: {}", result.result),
        Err(e) => println!("  执行失败: {}", e),
    }

    // 测试 TypeScript 插件扫描
    println!("\n📁 测试 TypeScript 插件扫描:");
    match manager.scan_typescript_plugins().await {
        Ok(_) => println!("  扫描完成"),
        Err(e) => println!("  扫描失败: {}", e),
    }

    println!("\n✅ 插件系统测试完成!");
}
