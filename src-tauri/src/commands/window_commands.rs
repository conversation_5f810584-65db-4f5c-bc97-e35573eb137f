use crate::window_manager::{WindowConfig, WindowState};
use crate::AppState;
use anyhow::Result;
use tauri::State;
use uuid;

/// 聚焦主窗口
#[tauri::command]
pub async fn focus_main_window(state: State<'_, AppState>) -> Result<bool, String> {
    let mut window_manager = state.window_manager.write().await;

    match window_manager.focus_main_window().await {
        Ok(_) => {
            log::info!("主窗口已聚焦");

            // 发送事件
            let event_bus = state.event_bus.clone();
            let window_event = crate::event_bus::WindowEvent::Focused {
                window_id: "main".to_string(),
            };

            tokio::spawn(async move {
                if let Err(e) = event_bus
                    .emit_window_event(window_event, "window_command".to_string())
                    .await
                {
                    log::error!("发送窗口聚焦事件失败: {}", e);
                }
            });

            Ok(true)
        }
        Err(e) => {
            log::error!("聚焦主窗口失败: {}", e);
            Err(e.to_string())
        }
    }
}

/// 显示窗口
#[tauri::command]
pub async fn show_window(window_id: String, state: State<'_, AppState>) -> Result<bool, String> {
    let mut window_manager = state.window_manager.write().await;

    match window_manager.show_window(&window_id).await {
        Ok(_) => {
            log::info!("窗口已显示: {}", window_id);

            // 发送事件
            let event_bus = state.event_bus.clone();
            let window_event = crate::event_bus::WindowEvent::VisibilityChanged {
                window_id: window_id.clone(),
                visible: true,
            };

            tokio::spawn(async move {
                if let Err(e) = event_bus
                    .emit_window_event(window_event, "window_command".to_string())
                    .await
                {
                    log::error!("发送窗口显示事件失败: {}", e);
                }
            });

            Ok(true)
        }
        Err(e) => {
            log::error!("显示窗口失败: {} -> {}", window_id, e);
            Err(e.to_string())
        }
    }
}

/// 隐藏窗口
#[tauri::command]
pub async fn hide_window(window_id: String, state: State<'_, AppState>) -> Result<bool, String> {
    let mut window_manager = state.window_manager.write().await;

    match window_manager.hide_window(&window_id).await {
        Ok(_) => {
            log::info!("窗口已隐藏: {}", window_id);

            // 发送事件
            let event_bus = state.event_bus.clone();
            let window_event = crate::event_bus::WindowEvent::VisibilityChanged {
                window_id: window_id.clone(),
                visible: false,
            };

            tokio::spawn(async move {
                if let Err(e) = event_bus
                    .emit_window_event(window_event, "window_command".to_string())
                    .await
                {
                    log::error!("发送窗口隐藏事件失败: {}", e);
                }
            });

            Ok(true)
        }
        Err(e) => {
            log::error!("隐藏窗口失败: {} -> {}", window_id, e);
            Err(e.to_string())
        }
    }
}

/// 切换窗口显示状态
#[tauri::command]
pub async fn toggle_window(window_id: String, state: State<'_, AppState>) -> Result<bool, String> {
    let mut window_manager = state.window_manager.write().await;

    match window_manager.toggle_window(&window_id).await {
        Ok(_) => {
            log::info!("窗口显示状态已切换: {}", window_id);
            Ok(true)
        }
        Err(e) => {
            log::error!("切换窗口显示状态失败: {} -> {}", window_id, e);
            Err(e.to_string())
        }
    }
}

/// 获取窗口状态
#[tauri::command]
pub async fn get_window_state(
    window_id: String,
    state: State<'_, AppState>,
) -> Result<Option<WindowState>, String> {
    let window_manager = state.window_manager.read().await;

    let window_state = window_manager.get_window_state(&window_id).cloned();

    log::debug!("获取窗口状态: {} -> {}", window_id, window_state.is_some());

    Ok(window_state)
}

/// 获取所有窗口状态
#[tauri::command]
pub async fn get_all_windows(state: State<'_, AppState>) -> Result<Vec<WindowState>, String> {
    let window_manager = state.window_manager.read().await;

    let windows: Vec<WindowState> = window_manager
        .get_all_windows()
        .into_iter()
        .cloned()
        .collect();

    log::debug!("获取所有窗口状态: {} 个窗口", windows.len());

    Ok(windows)
}

/// 关闭所有插件窗口
#[tauri::command]
pub async fn close_all_plugin_windows(state: State<'_, AppState>) -> Result<bool, String> {
    let mut window_manager = state.window_manager.write().await;

    match window_manager.close_all_plugin_windows().await {
        Ok(_) => {
            log::info!("所有插件窗口已关闭");
            Ok(true)
        }
        Err(e) => {
            log::error!("关闭所有插件窗口失败: {}", e);
            Err(e.to_string())
        }
    }
}

/// 获取窗口统计信息
#[tauri::command]
pub async fn get_window_stats(
    state: State<'_, AppState>,
) -> Result<std::collections::HashMap<String, serde_json::Value>, String> {
    let window_manager = state.window_manager.read().await;

    let stats = window_manager.get_window_stats();

    log::debug!("获取窗口统计信息: {:?}", stats);

    Ok(stats)
}

/// 最小化窗口
#[tauri::command]
pub async fn minimize_window(
    window_id: String,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let mut window_manager = state.window_manager.write().await;

    match window_manager.minimize_window(&window_id).await {
        Ok(_) => {
            log::info!("窗口已最小化: {}", window_id);

            // 发送事件
            let event_bus = state.event_bus.clone();
            let window_event = crate::event_bus::WindowEvent::VisibilityChanged {
                window_id: window_id.clone(),
                visible: false,
            };

            tokio::spawn(async move {
                if let Err(e) = event_bus
                    .emit_window_event(window_event, "window_command".to_string())
                    .await
                {
                    log::error!("发送窗口最小化事件失败: {}", e);
                }
            });

            Ok(true)
        }
        Err(e) => {
            log::error!("最小化窗口失败: {} -> {}", window_id, e);
            Err(e.to_string())
        }
    }
}

/// 最大化窗口
#[tauri::command]
pub async fn maximize_window(
    window_id: String,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let mut window_manager = state.window_manager.write().await;

    match window_manager.maximize_window(&window_id).await {
        Ok(_) => {
            log::info!("窗口已最大化: {}", window_id);

            // 发送事件
            let event_bus = state.event_bus.clone();
            let window_event = crate::event_bus::WindowEvent::VisibilityChanged {
                window_id: window_id.clone(),
                visible: true,
            };

            tokio::spawn(async move {
                if let Err(e) = event_bus
                    .emit_window_event(window_event, "window_command".to_string())
                    .await
                {
                    log::error!("发送窗口最大化事件失败: {}", e);
                }
            });

            Ok(true)
        }
        Err(e) => {
            log::error!("最大化窗口失败: {} -> {}", window_id, e);
            Err(e.to_string())
        }
    }
}

/// 取消最大化窗口
#[tauri::command]
pub async fn unmaximize_window(
    window_id: String,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let mut window_manager = state.window_manager.write().await;

    match window_manager.unmaximize_window(&window_id).await {
        Ok(_) => {
            log::info!("窗口已取消最大化: {}", window_id);
            Ok(true)
        }
        Err(e) => {
            log::error!("取消最大化窗口失败: {} -> {}", window_id, e);
            Err(e.to_string())
        }
    }
}

/// 调整窗口大小
#[tauri::command]
pub async fn resize_window(
    window_id: String,
    width: u32,
    height: u32,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let mut window_manager = state.window_manager.write().await;

    match window_manager
        .resize_window(&window_id, width, height)
        .await
    {
        Ok(_) => {
            log::info!("窗口大小已调整: {} -> {}x{}", window_id, width, height);
            Ok(true)
        }
        Err(e) => {
            log::error!("调整窗口大小失败: {} -> {}", window_id, e);
            Err(e.to_string())
        }
    }
}

/// 移动窗口
#[tauri::command]
pub async fn move_window(
    window_id: String,
    x: i32,
    y: i32,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let mut window_manager = state.window_manager.write().await;

    match window_manager.move_window(&window_id, x, y).await {
        Ok(_) => {
            log::info!("窗口已移动: {} -> ({}, {})", window_id, x, y);
            Ok(true)
        }
        Err(e) => {
            log::error!("移动窗口失败: {} -> {}", window_id, e);
            Err(e.to_string())
        }
    }
}

/// 设置窗口总在最前
#[tauri::command]
pub async fn set_always_on_top(
    window_id: String,
    always_on_top: bool,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let mut window_manager = state.window_manager.write().await;

    match window_manager
        .set_always_on_top(&window_id, always_on_top)
        .await
    {
        Ok(_) => {
            log::info!("窗口置顶状态已设置: {} -> {}", window_id, always_on_top);
            Ok(true)
        }
        Err(e) => {
            log::error!("设置窗口置顶状态失败: {} -> {}", window_id, e);
            Err(e.to_string())
        }
    }
}

/// 获取窗口信息
#[tauri::command]
pub async fn get_window_info(
    window_id: String,
    state: State<'_, AppState>,
) -> Result<Option<crate::window_manager::WindowInfo>, String> {
    let window_manager = state.window_manager.read().await;

    match window_manager.get_window_info(&window_id).await {
        Ok(window_info) => {
            log::debug!("获取窗口信息: {} -> {:?}", window_id, window_info);
            Ok(window_info)
        }
        Err(e) => {
            log::error!("获取窗口信息失败: {} -> {}", window_id, e);
            Err(e.to_string())
        }
    }
}

/// 同步窗口状态
#[tauri::command]
pub async fn sync_window_state(
    window_id: String,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let mut window_manager = state.window_manager.write().await;

    match window_manager.sync_window_state(&window_id).await {
        Ok(_) => {
            log::debug!("窗口状态已同步: {}", window_id);
            Ok(true)
        }
        Err(e) => {
            log::error!("同步窗口状态失败: {} -> {}", window_id, e);
            Err(e.to_string())
        }
    }
}

/// 同步所有窗口状态
#[tauri::command]
pub async fn sync_all_window_states(state: State<'_, AppState>) -> Result<bool, String> {
    let mut window_manager = state.window_manager.write().await;

    match window_manager.sync_all_window_states().await {
        Ok(_) => {
            log::info!("所有窗口状态已同步");
            Ok(true)
        }
        Err(e) => {
            log::error!("同步所有窗口状态失败: {}", e);
            Err(e.to_string())
        }
    }
}

/// 创建通用窗口（用于窗口管理面板）
#[tauri::command]
pub async fn create_window(
    url: String,
    title: String,
    width: u32,
    height: u32,
    state: State<'_, AppState>,
) -> Result<String, String> {
    let mut window_manager = state.window_manager.write().await;

    // 为通用窗口生成一个唯一的插件ID
    let plugin_id = format!("generic_window_{}", uuid::Uuid::new_v4());

    match window_manager
        .create_plugin_window(plugin_id.clone(), url.clone(), title.clone())
        .await
    {
        Ok(window_id) => {
            log::info!(
                "通用窗口已创建: {} ({}x{}) - {}",
                window_id,
                width,
                height,
                url
            );

            // 尝试调整窗口大小
            if let Err(e) = window_manager
                .resize_window(&window_id, width, height)
                .await
            {
                log::warn!("调整新窗口大小失败: {}", e);
            }

            // 发送事件
            let event_bus = state.event_bus.clone();
            let window_event = crate::event_bus::WindowEvent::Created {
                window_id: window_id.clone(),
                window_type: "generic".to_string(),
            };

            tokio::spawn(async move {
                if let Err(e) = event_bus
                    .emit_window_event(window_event, "window_command".to_string())
                    .await
                {
                    log::error!("发送窗口创建事件失败: {}", e);
                }
            });

            Ok(window_id)
        }
        Err(e) => {
            log::error!("创建通用窗口失败: {} -> {}", url, e);
            Err(e.to_string())
        }
    }
}
