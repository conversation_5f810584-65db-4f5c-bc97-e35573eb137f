//! # 布局引擎命令
//!
//! 提供高级布局算法和动画功能的Tauri命令接口

use crate::layout_engine::{
    EasingFunction, LayoutAnimation, LayoutConstraints, LayoutEngine, LayoutTemplate,
    LayoutTemplateConfig, ScreenInfo, SplitDirection, WindowBounds,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;
use tauri::{A<PERSON><PERSON><PERSON><PERSON>, Manager, State};
use tokio::sync::RwLock;

/// 布局引擎状态管理
#[derive(Clone)]
pub struct LayoutEngineState {
    pub engine: Arc<RwLock<LayoutEngine>>,
}

impl LayoutEngineState {
    pub fn new() -> Self {
        // 默认屏幕信息，实际使用时应该从系统获取
        let default_screen = ScreenInfo {
            width: 1920.0,
            height: 1080.0,
            scale_factor: 1.0,
            workarea: WindowBounds {
                x: 0.0,
                y: 0.0,
                width: 1920.0,
                height: 1080.0,
            },
        };

        Self {
            engine: Arc::new(RwLock::new(LayoutEngine::new(default_screen))),
        }
    }
}

/// 屏幕信息请求
#[derive(Debug, Serialize, Deserialize)]
pub struct ScreenInfoRequest {
    pub width: f64,
    pub height: f64,
    pub scale_factor: f64,
    pub workarea: WindowBounds,
}

/// 布局模板请求
#[derive(Debug, Serialize, Deserialize)]
pub struct LayoutTemplateRequest {
    pub id: String,
    pub name: String,
    pub description: String,
    pub layout_type: String,
    pub config: LayoutTemplateConfig,
}

/// 布局约束请求
#[derive(Debug, Serialize, Deserialize)]
pub struct LayoutConstraintsRequest {
    pub min_window_width: f64,
    pub min_window_height: f64,
    pub max_window_width: Option<f64>,
    pub max_window_height: Option<f64>,
    pub window_gap: f64,
    pub screen_margin: f64,
}

/// 布局动画请求
#[derive(Debug, Serialize, Deserialize)]
pub struct LayoutAnimationRequest {
    pub duration_ms: u64,
    pub easing: EasingFunction,
    pub enabled: bool,
}

/// 布局结果
#[derive(Debug, Serialize, Deserialize)]
pub struct LayoutResult {
    pub success: bool,
    pub message: String,
    pub layout: HashMap<String, WindowBounds>,
    pub stats: Option<LayoutStatsResponse>,
}

/// 布局统计响应
#[derive(Debug, Serialize, Deserialize)]
pub struct LayoutStatsResponse {
    pub calculation_time_ms: u64,
    pub windows_processed: usize,
    pub layout_efficiency: f64,
    pub balance_score: f64,
}

/// 模板列表响应
#[derive(Debug, Serialize, Deserialize)]
pub struct TemplateListResponse {
    pub templates: Vec<LayoutTemplate>,
    pub total_count: usize,
}

/// 性能统计响应
#[derive(Debug, Serialize, Deserialize)]
pub struct PerformanceStatsResponse {
    pub stats: HashMap<String, LayoutStatsResponse>,
}

/// 动画帧响应
#[derive(Debug, Serialize, Deserialize)]
pub struct AnimationFrameResponse {
    pub frame: HashMap<String, WindowBounds>,
    pub progress: f64,
}

/// 更新屏幕信息
#[tauri::command]
pub async fn update_screen_info(
    screen_info: ScreenInfoRequest,
    state: State<'_, LayoutEngineState>,
) -> Result<String, String> {
    let mut engine = state.engine.write().await;

    let screen = ScreenInfo {
        width: screen_info.width,
        height: screen_info.height,
        scale_factor: screen_info.scale_factor,
        workarea: screen_info.workarea,
    };

    engine.update_screen_info(screen);
    Ok("屏幕信息已更新".to_string())
}

/// 智能平铺布局
#[tauri::command]
pub async fn apply_auto_tile_layout(
    window_ids: Vec<String>,
    state: State<'_, LayoutEngineState>,
) -> Result<LayoutResult, String> {
    let start_time = std::time::Instant::now();
    let mut engine = state.engine.write().await;

    match engine.auto_tile_layout(&window_ids) {
        Ok(layout) => {
            let stats =
                engine
                    .get_performance_stats()
                    .get("auto_tile")
                    .map(|s| LayoutStatsResponse {
                        calculation_time_ms: s.calculation_time.as_millis() as u64,
                        windows_processed: s.windows_processed,
                        layout_efficiency: s.layout_efficiency,
                        balance_score: s.balance_score,
                    });

            Ok(LayoutResult {
                success: true,
                message: format!("成功应用智能平铺布局，处理了 {} 个窗口", window_ids.len()),
                layout,
                stats,
            })
        }
        Err(e) => Err(format!("应用智能平铺布局失败: {}", e)),
    }
}

/// 应用布局模板
#[tauri::command]
pub async fn apply_layout_template(
    template_id: String,
    window_ids: Vec<String>,
    state: State<'_, LayoutEngineState>,
) -> Result<LayoutResult, String> {
    let mut engine = state.engine.write().await;

    match engine.apply_template(&template_id, &window_ids) {
        Ok(layout) => Ok(LayoutResult {
            success: true,
            message: format!(
                "成功应用布局模板 '{}', 处理了 {} 个窗口",
                template_id,
                window_ids.len()
            ),
            layout,
            stats: None,
        }),
        Err(e) => Err(format!("应用布局模板失败: {}", e)),
    }
}

/// 创建自定义布局模板
#[tauri::command]
pub async fn create_layout_template(
    template_request: LayoutTemplateRequest,
    state: State<'_, LayoutEngineState>,
) -> Result<String, String> {
    let mut engine = state.engine.write().await;

    let template = LayoutTemplate {
        id: template_request.id.clone(),
        name: template_request.name,
        description: template_request.description,
        layout_type: template_request.layout_type,
        config: template_request.config,
        created_at: chrono::Utc::now(),
        usage_count: 0,
    };

    engine.add_template(template);
    Ok(format!("布局模板 '{}' 创建成功", template_request.id))
}

/// 获取所有布局模板
#[tauri::command]
pub async fn get_layout_templates(
    state: State<'_, LayoutEngineState>,
) -> Result<TemplateListResponse, String> {
    let engine = state.engine.read().await;
    let templates: Vec<_> = engine.get_templates().into_iter().cloned().collect();
    let total_count = templates.len();

    Ok(TemplateListResponse {
        templates,
        total_count,
    })
}

/// 获取热门布局模板
#[tauri::command]
pub async fn get_popular_templates(
    limit: usize,
    state: State<'_, LayoutEngineState>,
) -> Result<TemplateListResponse, String> {
    let engine = state.engine.read().await;
    let templates: Vec<_> = engine
        .get_popular_templates(limit)
        .into_iter()
        .cloned()
        .collect();
    let total_count = templates.len();

    Ok(TemplateListResponse {
        templates,
        total_count,
    })
}

/// 设置布局约束
#[tauri::command]
pub async fn set_layout_constraints(
    constraints_request: LayoutConstraintsRequest,
    state: State<'_, LayoutEngineState>,
) -> Result<String, String> {
    let mut engine = state.engine.write().await;

    engine.constraints = LayoutConstraints {
        min_window_width: constraints_request.min_window_width,
        min_window_height: constraints_request.min_window_height,
        max_window_width: constraints_request.max_window_width,
        max_window_height: constraints_request.max_window_height,
        window_gap: constraints_request.window_gap,
        screen_margin: constraints_request.screen_margin,
    };

    Ok("布局约束已更新".to_string())
}

/// 获取布局约束
#[tauri::command]
pub async fn get_layout_constraints(
    state: State<'_, LayoutEngineState>,
) -> Result<LayoutConstraints, String> {
    let engine = state.engine.read().await;
    Ok(engine.constraints.clone())
}

/// 设置布局动画
#[tauri::command]
pub async fn set_layout_animation(
    animation_request: LayoutAnimationRequest,
    state: State<'_, LayoutEngineState>,
) -> Result<String, String> {
    let mut engine = state.engine.write().await;

    engine.animation = LayoutAnimation {
        duration: Duration::from_millis(animation_request.duration_ms),
        easing: animation_request.easing,
        enabled: animation_request.enabled,
    };

    Ok("布局动画配置已更新".to_string())
}

/// 获取布局动画配置
#[tauri::command]
pub async fn get_layout_animation(
    state: State<'_, LayoutEngineState>,
) -> Result<LayoutAnimationRequest, String> {
    let engine = state.engine.read().await;

    Ok(LayoutAnimationRequest {
        duration_ms: engine.animation.duration.as_millis() as u64,
        easing: engine.animation.easing.clone(),
        enabled: engine.animation.enabled,
    })
}

/// 计算布局动画帧
#[tauri::command]
pub async fn calculate_animation_frame(
    from_layout: HashMap<String, WindowBounds>,
    to_layout: HashMap<String, WindowBounds>,
    progress: f64,
    state: State<'_, LayoutEngineState>,
) -> Result<AnimationFrameResponse, String> {
    let engine = state.engine.read().await;

    let frame = engine.calculate_animation_frame(&from_layout, &to_layout, progress);

    Ok(AnimationFrameResponse { frame, progress })
}

/// 获取性能统计
#[tauri::command]
pub async fn get_layout_performance_stats(
    state: State<'_, LayoutEngineState>,
) -> Result<PerformanceStatsResponse, String> {
    let engine = state.engine.read().await;

    let stats = engine
        .get_performance_stats()
        .into_iter()
        .map(|(k, v)| {
            let response_stats = LayoutStatsResponse {
                calculation_time_ms: v.calculation_time.as_millis() as u64,
                windows_processed: v.windows_processed,
                layout_efficiency: v.layout_efficiency,
                balance_score: v.balance_score,
            };
            (k, response_stats)
        })
        .collect();

    Ok(PerformanceStatsResponse { stats })
}

/// 清理性能缓存
#[tauri::command]
pub async fn cleanup_performance_cache(
    state: State<'_, LayoutEngineState>,
) -> Result<String, String> {
    let mut engine = state.engine.write().await;
    engine.cleanup_performance_cache();
    Ok("性能缓存已清理".to_string())
}

/// 获取屏幕信息
#[tauri::command]
pub async fn get_screen_info(state: State<'_, LayoutEngineState>) -> Result<ScreenInfo, String> {
    let engine = state.engine.read().await;
    Ok(engine.screen_info.clone())
}

/// 预览布局（不实际应用）
#[tauri::command]
pub async fn preview_layout(
    template_id: String,
    window_ids: Vec<String>,
    state: State<'_, LayoutEngineState>,
) -> Result<LayoutResult, String> {
    let engine = state.engine.read().await;

    // 创建临时引擎实例进行预览，避免影响当前状态
    let mut preview_engine = LayoutEngine::new(engine.screen_info.clone());
    preview_engine.constraints = engine.constraints.clone();
    preview_engine.animation = engine.animation.clone();
    preview_engine.templates = engine.templates.clone();

    match preview_engine.apply_template(&template_id, &window_ids) {
        Ok(layout) => Ok(LayoutResult {
            success: true,
            message: format!("布局预览生成成功，模板: '{}'", template_id),
            layout,
            stats: None,
        }),
        Err(e) => Err(format!("生成布局预览失败: {}", e)),
    }
}

/// 智能推荐布局
#[tauri::command]
pub async fn recommend_layout(
    window_ids: Vec<String>,
    state: State<'_, LayoutEngineState>,
) -> Result<String, String> {
    let engine = state.engine.read().await;
    let window_count = window_ids.len();
    let aspect_ratio = engine.screen_info.workarea.width / engine.screen_info.workarea.height;

    // 基于窗口数量和屏幕比例推荐最佳布局
    let recommended_template = match window_count {
        1 => "single-window",
        2 => {
            if aspect_ratio > 1.5 {
                "main-side" // 宽屏适合主副窗口
            } else {
                "vertical-split" // 竖屏适合垂直分割
            }
        }
        3..=4 => "golden-ratio",
        5..=8 => "three-column",
        _ => "auto-grid",
    };

    Ok(recommended_template.to_string())
}

/// 优化当前布局
#[tauri::command]
pub async fn optimize_current_layout(
    current_layout: HashMap<String, WindowBounds>,
    state: State<'_, LayoutEngineState>,
) -> Result<LayoutResult, String> {
    let engine = state.engine.read().await;

    // 分析当前布局的问题并优化
    let window_ids: Vec<String> = current_layout.keys().cloned().collect();

    // 使用智能平铺算法重新计算最优布局
    drop(engine); // 释放读锁
    let mut engine = state.engine.write().await;

    match engine.auto_tile_layout(&window_ids) {
        Ok(optimized_layout) => {
            let current_efficiency = engine.calculate_efficiency(&current_layout);
            let optimized_efficiency = engine.calculate_efficiency(&optimized_layout);

            if optimized_efficiency > current_efficiency {
                Ok(LayoutResult {
                    success: true,
                    message: format!(
                        "布局已优化，空间利用率从 {:.1}% 提升到 {:.1}%",
                        current_efficiency * 100.0,
                        optimized_efficiency * 100.0
                    ),
                    layout: optimized_layout,
                    stats: None,
                })
            } else {
                Ok(LayoutResult {
                    success: true,
                    message: "当前布局已经是最优状态".to_string(),
                    layout: current_layout,
                    stats: None,
                })
            }
        }
        Err(e) => Err(format!("布局优化失败: {}", e)),
    }
}
