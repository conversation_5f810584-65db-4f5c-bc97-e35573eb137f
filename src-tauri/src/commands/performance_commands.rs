//! 性能监控相关的 Tauri 命令

use crate::performance_monitor::{
    AppPerformanceStats, MetricType, PerformanceMetric, SystemSnapshot,
};
use crate::AppState;
use std::time::Duration;
use tauri::State;

/// 启动性能监控
#[tauri::command]
pub async fn start_performance_monitoring(state: State<'_, AppState>) -> Result<(), String> {
    let monitor = state.performance_monitor.read().await;
    monitor
        .start_monitoring()
        .await
        .map_err(|e| e.to_string())?;
    log::info!("性能监控已启动");
    Ok(())
}

/// 停止性能监控
#[tauri::command]
pub async fn stop_performance_monitoring(state: State<'_, AppState>) -> Result<(), String> {
    let monitor = state.performance_monitor.read().await;
    monitor.stop_monitoring();
    log::info!("性能监控已停止");
    Ok(())
}

/// 获取当前系统性能快照
#[tauri::command]
pub async fn get_system_snapshot(state: State<'_, AppState>) -> Result<SystemSnapshot, String> {
    let monitor = state.performance_monitor.read().await;
    monitor.get_system_snapshot().map_err(|e| e.to_string())
}

/// 获取应用性能统计
#[tauri::command]
pub async fn get_app_performance_stats(
    state: State<'_, AppState>,
) -> Result<AppPerformanceStats, String> {
    let monitor = state.performance_monitor.read().await;
    Ok(monitor.get_app_stats())
}

/// 获取性能历史数据
#[tauri::command]
pub async fn get_performance_history(
    state: State<'_, AppState>,
) -> Result<Vec<PerformanceMetric>, String> {
    let monitor = state.performance_monitor.read().await;
    Ok(monitor.get_metrics_history())
}

/// 根据类型获取性能指标
#[tauri::command]
pub async fn get_metrics_by_type(
    state: State<'_, AppState>,
    metric_type: String,
) -> Result<Vec<PerformanceMetric>, String> {
    let monitor = state.performance_monitor.read().await;

    let metric_type = match metric_type.as_str() {
        "cpu_usage" => MetricType::CpuUsage,
        "memory_usage" => MetricType::MemoryUsage,
        "memory_usage_percent" => MetricType::MemoryUsagePercent,
        "app_startup_time" => MetricType::AppStartupTime,
        "plugin_load_time" => MetricType::PluginLoadTime,
        "command_response_time" => MetricType::CommandResponseTime,
        "window_operation_time" => MetricType::WindowOperationTime,
        "event_bus_latency" => MetricType::EventBusLatency,
        "disk_usage" => MetricType::DiskUsage,
        "network_io" => MetricType::NetworkIO,
        _ => return Err("Unknown metric type".to_string()),
    };

    Ok(monitor.get_metrics_by_type(metric_type))
}

/// 清除性能历史数据
#[tauri::command]
pub async fn clear_performance_history(state: State<'_, AppState>) -> Result<(), String> {
    let monitor = state.performance_monitor.read().await;
    monitor.clear_history();
    log::info!("性能历史数据已清除");
    Ok(())
}

/// 手动记录启动时间
#[tauri::command]
pub async fn record_startup_complete(state: State<'_, AppState>) -> Result<(), String> {
    let monitor = state.performance_monitor.read().await;
    monitor.record_startup_time();
    Ok(())
}

/// 获取性能监控状态
#[tauri::command]
pub async fn get_monitoring_status(state: State<'_, AppState>) -> Result<bool, String> {
    // 这里可以通过检查监控线程是否活跃来确定状态
    // 暂时返回true，实际实现中可以添加状态跟踪
    Ok(true)
}

/// 导出性能数据
#[tauri::command]
pub async fn export_performance_data(
    state: State<'_, AppState>,
    format: String,
) -> Result<String, String> {
    let monitor = state.performance_monitor.read().await;
    let metrics = monitor.get_metrics_history();
    let stats = monitor.get_app_stats();

    match format.as_str() {
        "json" => {
            let export_data = serde_json::json!({
                "timestamp": chrono::Utc::now(),
                "app_stats": stats,
                "metrics": metrics
            });
            serde_json::to_string_pretty(&export_data).map_err(|e| e.to_string())
        }
        "csv" => {
            let mut csv_data = "timestamp,metric_type,value,unit,metadata\n".to_string();
            for metric in metrics {
                csv_data.push_str(&format!(
                    "{},{:?},{},{},{}\n",
                    metric.timestamp.format("%Y-%m-%d %H:%M:%S"),
                    metric.metric_type,
                    metric.value,
                    metric.unit,
                    metric.metadata.map_or("".to_string(), |m| m.to_string())
                ));
            }
            Ok(csv_data)
        }
        _ => Err("Unsupported export format".to_string()),
    }
}

/// 性能基准测试
#[tauri::command]
pub async fn run_performance_benchmark(
    state: State<'_, AppState>,
) -> Result<serde_json::Value, String> {
    let monitor = state.performance_monitor.read().await;

    // 执行一系列基准测试
    let start_time = std::time::Instant::now();

    // 模拟一些操作来测试性能
    let mut results = Vec::new();

    // 内存分配测试
    let memory_test_start = std::time::Instant::now();
    let _test_vec: Vec<u8> = vec![0; 1024 * 1024]; // 分配1MB内存
    let memory_test_duration = memory_test_start.elapsed();
    results.push(("memory_allocation", memory_test_duration.as_millis()));

    // CPU计算测试
    let cpu_test_start = std::time::Instant::now();
    let mut sum = 0u64;
    for i in 0..1000000 {
        sum += i;
    }
    let cpu_test_duration = cpu_test_start.elapsed();
    results.push(("cpu_computation", cpu_test_duration.as_millis()));

    // 文件系统测试
    let fs_test_start = std::time::Instant::now();
    if let Ok(temp_dir) = std::env::temp_dir().canonicalize() {
        let _ = std::fs::read_dir(temp_dir);
    }
    let fs_test_duration = fs_test_start.elapsed();
    results.push(("filesystem_access", fs_test_duration.as_millis()));

    let total_duration = start_time.elapsed();

    Ok(serde_json::json!({
        "total_duration_ms": total_duration.as_millis(),
        "tests": results.into_iter().map(|(name, duration)| {
            serde_json::json!({
                "test_name": name,
                "duration_ms": duration
            })
        }).collect::<Vec<_>>(),
        "timestamp": chrono::Utc::now()
    }))
}
