use crate::event_bus::{CustomEvent, EventMessage};
use crate::AppState;
use anyhow::Result;
use tauri::State;

/// 发送自定义事件
#[tauri::command]
pub async fn emit_event(
    event_name: String,
    payload: serde_json::Value,
    target: Option<String>,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let event_bus = state.event_bus.clone();

    let custom_event = CustomEvent {
        event_name: event_name.clone(),
        payload: payload.clone(),
        source: "frontend".to_string(),
        target: target.clone(),
    };

    match event_bus
        .emit_custom_event(custom_event, "event_command".to_string())
        .await
    {
        Ok(_) => {
            log::info!("事件已发送: {} -> {:?}", event_name, target);
            Ok(true)
        }
        Err(e) => {
            log::error!("发送事件失败: {} -> {}", event_name, e);
            Err(e.to_string())
        }
    }
}

/// 订阅事件 (简化版本，实际实现需要更复杂的订阅管理)
#[tauri::command]
pub async fn subscribe_to_events(
    event_pattern: String,
    state: State<'_, AppState>,
) -> Result<String, String> {
    let event_bus = state.event_bus.clone();

    // 创建一个简单的监听器
    let listener_id = uuid::Uuid::new_v4().to_string();
    let listener = std::sync::Arc::new(
        move |event: EventMessage| -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
            log::debug!(
                "接收到事件: {} ({})",
                event.id,
                serde_json::to_string(&event.event_type).unwrap_or_default()
            );
            Ok(())
        },
    );

    match event_bus.subscribe(&event_pattern, listener).await {
        listener_id => {
            log::info!(
                "已订阅事件模式: {} (监听器 ID: {})",
                event_pattern,
                listener_id
            );
            Ok(listener_id)
        }
    }
}

/// 获取事件历史记录
#[tauri::command]
pub async fn get_event_history(
    limit: Option<usize>,
    state: State<'_, AppState>,
) -> Result<Vec<EventMessage>, String> {
    let event_bus = state.event_bus.clone();

    let history = event_bus.get_history(limit).await;

    log::debug!("获取事件历史记录: {} 条", history.len());

    Ok(history)
}

/// 清空事件历史记录
#[tauri::command]
pub async fn clear_event_history(state: State<'_, AppState>) -> Result<bool, String> {
    let event_bus = state.event_bus.clone();

    event_bus.clear_history().await;

    log::info!("事件历史记录已清空");

    Ok(true)
}

/// 发送系统事件
#[tauri::command]
pub async fn emit_system_event(
    event_type: String,
    payload: serde_json::Value,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let event_bus = state.event_bus.clone();

    // 根据事件类型创建对应的系统事件
    let system_event = match event_type.as_str() {
        "global_shortcut" => {
            let shortcut = payload
                .get("shortcut")
                .and_then(|v| v.as_str())
                .unwrap_or("unknown")
                .to_string();

            crate::event_bus::SystemEvent::GlobalShortcut { shortcut }
        }
        "clipboard_changed" => {
            let content = payload
                .get("content")
                .and_then(|v| v.as_str())
                .unwrap_or("")
                .to_string();

            crate::event_bus::SystemEvent::ClipboardChanged { content }
        }
        "file_changed" => {
            let path = payload
                .get("path")
                .and_then(|v| v.as_str())
                .unwrap_or("")
                .to_string();
            let event_type = payload
                .get("event_type")
                .and_then(|v| v.as_str())
                .unwrap_or("unknown")
                .to_string();

            crate::event_bus::SystemEvent::FileChanged { path, event_type }
        }
        "app_started" => crate::event_bus::SystemEvent::AppStarted,
        "app_exit" => crate::event_bus::SystemEvent::AppExit,
        _ => {
            return Err(format!("未知的系统事件类型: {}", event_type));
        }
    };

    match event_bus
        .emit_system_event(system_event, "event_command".to_string())
        .await
    {
        Ok(_) => {
            log::info!("系统事件已发送: {}", event_type);
            Ok(true)
        }
        Err(e) => {
            log::error!("发送系统事件失败: {} -> {}", event_type, e);
            Err(e.to_string())
        }
    }
}

/// 发送UI事件
#[tauri::command]
pub async fn emit_ui_event(
    event_type: String,
    payload: serde_json::Value,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let event_bus = state.event_bus.clone();

    // 根据事件类型创建对应的UI事件
    let ui_event = match event_type.as_str() {
        "input_changed" => {
            let value = payload
                .get("value")
                .and_then(|v| v.as_str())
                .unwrap_or("")
                .to_string();

            crate::event_bus::UIEvent::InputChanged { value }
        }
        "search_results_changed" => {
            let results = payload
                .get("results")
                .and_then(|v| v.as_array())
                .map(|arr| {
                    arr.iter()
                        .filter_map(|v| v.as_str().map(|s| s.to_string()))
                        .collect()
                })
                .unwrap_or_default();

            crate::event_bus::UIEvent::SearchResultsChanged { results }
        }
        "theme_changed" => {
            let theme = payload
                .get("theme")
                .and_then(|v| v.as_str())
                .unwrap_or("system")
                .to_string();

            crate::event_bus::UIEvent::ThemeChanged { theme }
        }
        "settings_changed" => {
            let setting_key = payload
                .get("setting_key")
                .and_then(|v| v.as_str())
                .unwrap_or("")
                .to_string();
            let new_value = payload
                .get("new_value")
                .and_then(|v| v.as_str())
                .unwrap_or("")
                .to_string();

            crate::event_bus::UIEvent::SettingsChanged {
                setting_key,
                new_value,
            }
        }
        _ => {
            return Err(format!("未知的UI事件类型: {}", event_type));
        }
    };

    match event_bus
        .emit_ui_event(ui_event, "event_command".to_string())
        .await
    {
        Ok(_) => {
            log::info!("UI事件已发送: {}", event_type);
            Ok(true)
        }
        Err(e) => {
            log::error!("发送UI事件失败: {} -> {}", event_type, e);
            Err(e.to_string())
        }
    }
}

/// 发送插件事件
#[tauri::command]
pub async fn emit_plugin_event(
    event_type: String,
    payload: serde_json::Value,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let event_bus = state.event_bus.clone();

    // 根据事件类型创建对应的插件事件
    let plugin_event = match event_type.as_str() {
        "execution_complete" => {
            let plugin_id = payload
                .get("plugin_id")
                .and_then(|v| v.as_str())
                .unwrap_or("")
                .to_string();
            let action_id = payload
                .get("action_id")
                .and_then(|v| v.as_str())
                .unwrap_or("")
                .to_string();
            let result = payload
                .get("result")
                .and_then(|v| v.as_str())
                .unwrap_or("")
                .to_string();
            let success = payload
                .get("success")
                .and_then(|v| v.as_bool())
                .unwrap_or(false);

            crate::event_bus::PluginEvent::ExecutionComplete {
                plugin_id,
                action_id,
                result,
                success,
            }
        }
        "installed" => {
            let plugin_id = payload
                .get("plugin_id")
                .and_then(|v| v.as_str())
                .unwrap_or("")
                .to_string();
            let version = payload
                .get("version")
                .and_then(|v| v.as_str())
                .unwrap_or("1.0.0")
                .to_string();

            crate::event_bus::PluginEvent::Installed { plugin_id, version }
        }
        "uninstalled" => {
            let plugin_id = payload
                .get("plugin_id")
                .and_then(|v| v.as_str())
                .unwrap_or("")
                .to_string();

            crate::event_bus::PluginEvent::Uninstalled { plugin_id }
        }
        "toggle_enabled" => {
            let plugin_id = payload
                .get("plugin_id")
                .and_then(|v| v.as_str())
                .unwrap_or("")
                .to_string();
            let enabled = payload
                .get("enabled")
                .and_then(|v| v.as_bool())
                .unwrap_or(false);

            crate::event_bus::PluginEvent::ToggleEnabled { plugin_id, enabled }
        }
        _ => {
            return Err(format!("未知的插件事件类型: {}", event_type));
        }
    };

    match event_bus
        .emit_plugin_event(plugin_event, "event_command".to_string())
        .await
    {
        Ok(_) => {
            log::info!("插件事件已发送: {}", event_type);
            Ok(true)
        }
        Err(e) => {
            log::error!("发送插件事件失败: {} -> {}", event_type, e);
            Err(e.to_string())
        }
    }
}
