//! 启动器相关的 Tauri 命令处理器

use crate::launcher_manager::LauncherManager;
use serde::{Deserialize, Serialize};
use tauri::{AppHandle, State};

#[derive(Debug, Serialize, Deserialize)]
pub struct LauncherStateResponse {
    pub is_visible: bool,
    pub position: Option<(i32, i32)>,
    pub size: (u32, u32),
}

/// 切换启动器可见性
#[tauri::command]
pub async fn toggle_launcher(
    app_handle: AppHandle,
    launcher_manager: State<'_, LauncherManager>,
) -> Result<LauncherStateResponse, String> {
    launcher_manager.toggle_launcher(&app_handle).await?;
    let state = launcher_manager.get_state().await;

    Ok(LauncherStateResponse {
        is_visible: state.is_visible,
        position: state.position.map(|p| (p.x, p.y)),
        size: (state.size.width, state.size.height),
    })
}

/// 显示启动器
#[tauri::command]
pub async fn show_launcher(
    app_handle: AppHandle,
    launcher_manager: State<'_, LauncherManager>,
) -> Result<LauncherStateResponse, String> {
    launcher_manager.show_launcher(&app_handle).await?;
    let state = launcher_manager.get_state().await;

    Ok(LauncherStateResponse {
        is_visible: state.is_visible,
        position: state.position.map(|p| (p.x, p.y)),
        size: (state.size.width, state.size.height),
    })
}

/// 隐藏启动器
#[tauri::command]
pub async fn hide_launcher(
    app_handle: AppHandle,
    launcher_manager: State<'_, LauncherManager>,
) -> Result<LauncherStateResponse, String> {
    launcher_manager.hide_launcher(&app_handle).await?;
    let state = launcher_manager.get_state().await;

    Ok(LauncherStateResponse {
        is_visible: state.is_visible,
        position: state.position.map(|p| (p.x, p.y)),
        size: (state.size.width, state.size.height),
    })
}

/// 获取启动器状态
#[tauri::command]
pub async fn get_launcher_state(
    launcher_manager: State<'_, LauncherManager>,
) -> Result<LauncherStateResponse, String> {
    let state = launcher_manager.get_state().await;

    Ok(LauncherStateResponse {
        is_visible: state.is_visible,
        position: state.position.map(|p| (p.x, p.y)),
        size: (state.size.width, state.size.height),
    })
}

/// 设置启动器大小（动态调整）
#[tauri::command]
pub async fn set_launcher_size(
    app_handle: AppHandle,
    launcher_manager: State<'_, LauncherManager>,
    height: u32,
) -> Result<LauncherStateResponse, String> {
    launcher_manager
        .set_launcher_size(&app_handle, height)
        .await?;
    let state = launcher_manager.get_state().await;

    Ok(LauncherStateResponse {
        is_visible: state.is_visible,
        position: state.position.map(|p| (p.x, p.y)),
        size: (state.size.width, state.size.height),
    })
}

/// 处理窗口失焦事件
#[tauri::command]
pub async fn handle_launcher_blur(
    app_handle: AppHandle,
    launcher_manager: State<'_, LauncherManager>,
) -> Result<(), String> {
    launcher_manager.handle_blur(&app_handle).await
}

/// 创建插件窗口
#[tauri::command]
pub async fn create_plugin_window(
    app_handle: AppHandle,
    plugin_id: String,
    title: String,
    url: String,
    width: Option<u32>,
    height: Option<u32>,
) -> Result<String, String> {
    use tauri::{Manager, WebviewWindowBuilder};

    let window_label = format!("plugin-{}", plugin_id);
    let webview_url =
        tauri::WebviewUrl::External(url.parse().map_err(|e| format!("Invalid URL: {}", e))?);

    let mut builder = WebviewWindowBuilder::new(&app_handle, &window_label, webview_url)
        .title(&title)
        .inner_size(width.unwrap_or(800) as f64, height.unwrap_or(600) as f64)
        .center()
        .resizable(true)
        .fullscreen(false)
        .decorations(true)
        .always_on_top(false);

    let window = builder.build().map_err(|e| e.to_string())?;
    window.show().map_err(|e| e.to_string())?;

    Ok(window_label)
}

/// 关闭插件窗口
#[tauri::command]
pub async fn close_plugin_window(app_handle: AppHandle, plugin_id: String) -> Result<(), String> {
    use tauri::Manager;

    let window_label = format!("plugin-{}", plugin_id);

    if let Some(window) = app_handle.get_webview_window(&window_label) {
        window.close().map_err(|e| e.to_string())?;
    }

    Ok(())
}

/// 获取插件窗口列表
#[tauri::command]
pub async fn get_plugin_windows(app_handle: AppHandle) -> Result<Vec<(String, bool)>, String> {
    use tauri::Manager;

    let mut plugin_windows = Vec::new();

    for (label, window) in app_handle.webview_windows() {
        if label.starts_with("plugin-") {
            let plugin_id = label.strip_prefix("plugin-").unwrap_or(&label).to_string();
            let is_visible = window.is_visible().unwrap_or(false);
            plugin_windows.push((plugin_id, is_visible));
        }
    }

    Ok(plugin_windows)
}
