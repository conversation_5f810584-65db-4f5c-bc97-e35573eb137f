use crate::settings::{AppSettings, SettingsManager};
use crate::AppState;
use anyhow::Result;
use tauri::State;

/// 获取应用设置
#[tauri::command]
pub async fn get_settings(state: State<'_, AppState>) -> Result<AppSettings, String> {
    let settings_manager = state.settings_manager.read().await;

    let settings = settings_manager.get_settings().clone();

    log::debug!("获取应用设置");

    Ok(settings)
}

/// 更新应用设置
#[tauri::command]
pub async fn update_settings(
    new_settings: AppSettings,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let mut settings_manager = state.settings_manager.write().await;

    match settings_manager.update_settings(new_settings) {
        Ok(_) => {
            log::info!("应用设置已更新");

            // 发送事件
            let event_bus = state.event_bus.clone();
            let ui_event = crate::event_bus::UIEvent::SettingsChanged {
                setting_key: "all".to_string(),
                new_value: "updated".to_string(),
            };

            tokio::spawn(async move {
                if let Err(e) = event_bus
                    .emit_ui_event(ui_event, "settings_command".to_string())
                    .await
                {
                    log::error!("发送设置更新事件失败: {}", e);
                }
            });

            Ok(true)
        }
        Err(e) => {
            log::error!("更新应用设置失败: {}", e);
            Err(e.to_string())
        }
    }
}

/// 重置应用设置
#[tauri::command]
pub async fn reset_settings(state: State<'_, AppState>) -> Result<bool, String> {
    let mut settings_manager = state.settings_manager.write().await;

    match settings_manager.reset_settings() {
        Ok(_) => {
            log::info!("应用设置已重置");

            // 发送事件
            let event_bus = state.event_bus.clone();
            let ui_event = crate::event_bus::UIEvent::SettingsChanged {
                setting_key: "all".to_string(),
                new_value: "reset".to_string(),
            };

            tokio::spawn(async move {
                if let Err(e) = event_bus
                    .emit_ui_event(ui_event, "settings_command".to_string())
                    .await
                {
                    log::error!("发送设置重置事件失败: {}", e);
                }
            });

            Ok(true)
        }
        Err(e) => {
            log::error!("重置应用设置失败: {}", e);
            Err(e.to_string())
        }
    }
}

/// 获取特定设置值
#[tauri::command]
pub async fn get_setting_value(
    key: String,
    state: State<'_, AppState>,
) -> Result<Option<serde_json::Value>, String> {
    let settings_manager = state.settings_manager.read().await;

    let value = settings_manager.get_setting::<serde_json::Value>(&key);

    log::debug!("获取设置值: {} -> {}", key, value.is_some());

    Ok(value)
}

/// 设置特定设置值
#[tauri::command]
pub async fn set_setting_value(
    key: String,
    value: serde_json::Value,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let mut settings_manager = state.settings_manager.write().await;

    match settings_manager.set_setting(&key, &value) {
        Ok(_) => {
            log::info!("设置值已更新: {}", key);

            // 发送事件
            let event_bus = state.event_bus.clone();
            let ui_event = crate::event_bus::UIEvent::SettingsChanged {
                setting_key: key.clone(),
                new_value: value.to_string(),
            };

            tokio::spawn(async move {
                if let Err(e) = event_bus
                    .emit_ui_event(ui_event, "settings_command".to_string())
                    .await
                {
                    log::error!("发送设置更新事件失败: {}", e);
                }
            });

            Ok(true)
        }
        Err(e) => {
            log::error!("设置值更新失败: {} -> {}", key, e);
            Err(e.to_string())
        }
    }
}

/// 获取插件设置
#[tauri::command]
pub async fn get_plugin_setting(
    plugin_id: String,
    key: String,
    state: State<'_, AppState>,
) -> Result<Option<serde_json::Value>, String> {
    let settings_manager = state.settings_manager.read().await;

    let value = settings_manager.get_plugin_setting::<serde_json::Value>(&plugin_id, &key);

    log::debug!("获取插件设置: {}:{} -> {}", plugin_id, key, value.is_some());

    Ok(value)
}

/// 设置插件设置
#[tauri::command]
pub async fn set_plugin_setting(
    plugin_id: String,
    key: String,
    value: serde_json::Value,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let mut settings_manager = state.settings_manager.write().await;

    match settings_manager.set_plugin_setting(&plugin_id, &key, &value) {
        Ok(_) => {
            log::info!("插件设置已更新: {}:{}", plugin_id, key);

            // 发送事件
            let event_bus = state.event_bus.clone();
            let ui_event = crate::event_bus::UIEvent::SettingsChanged {
                setting_key: format!("plugin.{}.{}", plugin_id, key),
                new_value: value.to_string(),
            };

            tokio::spawn(async move {
                if let Err(e) = event_bus
                    .emit_ui_event(ui_event, "settings_command".to_string())
                    .await
                {
                    log::error!("发送插件设置更新事件失败: {}", e);
                }
            });

            Ok(true)
        }
        Err(e) => {
            log::error!("插件设置更新失败: {}:{} -> {}", plugin_id, key, e);
            Err(e.to_string())
        }
    }
}

/// 导出设置
#[tauri::command]
pub async fn export_settings(state: State<'_, AppState>) -> Result<String, String> {
    let settings_manager = state.settings_manager.read().await;

    match settings_manager.export_settings() {
        Ok(settings_json) => {
            log::info!("设置已导出");
            Ok(settings_json)
        }
        Err(e) => {
            log::error!("导出设置失败: {}", e);
            Err(e.to_string())
        }
    }
}

/// 导入设置
#[tauri::command]
pub async fn import_settings(
    settings_json: String,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let mut settings_manager = state.settings_manager.write().await;

    match settings_manager.import_settings(&settings_json) {
        Ok(_) => {
            log::info!("设置已导入");

            // 发送事件
            let event_bus = state.event_bus.clone();
            let ui_event = crate::event_bus::UIEvent::SettingsChanged {
                setting_key: "all".to_string(),
                new_value: "imported".to_string(),
            };

            tokio::spawn(async move {
                if let Err(e) = event_bus
                    .emit_ui_event(ui_event, "settings_command".to_string())
                    .await
                {
                    log::error!("发送设置导入事件失败: {}", e);
                }
            });

            Ok(true)
        }
        Err(e) => {
            log::error!("导入设置失败: {}", e);
            Err(e.to_string())
        }
    }
}

/// 获取设置文件路径
#[tauri::command]
pub async fn get_settings_file_path(state: State<'_, AppState>) -> Result<String, String> {
    let settings_manager = state.settings_manager.read().await;

    let path = SettingsManager::get_default_settings_file_path()
        .to_string_lossy()
        .to_string();

    log::debug!("获取设置文件路径: {}", path);

    Ok(path)
}

/// 验证设置
#[tauri::command]
pub async fn validate_settings(
    settings: AppSettings,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    // 创建临时设置管理器进行验证
    let mut temp_manager = crate::settings::SettingsManager::new();
    temp_manager.set_auto_save(false);

    match temp_manager.update_settings(settings) {
        Ok(_) => match temp_manager.validate_settings() {
            Ok(_) => {
                log::info!("设置验证通过");
                Ok(true)
            }
            Err(e) => {
                log::warn!("设置验证失败: {}", e);
                Err(e.to_string())
            }
        },
        Err(e) => {
            log::error!("设置更新失败: {}", e);
            Err(e.to_string())
        }
    }
}
