use crate::plugin_system::{PluginExecutionResult, PluginMetadata, SearchResultItem};
use crate::AppState;
use anyhow::Result;
use tauri::State;

/// 搜索插件
#[tauri::command]
pub async fn search_plugins(
    query: String,
    state: State<'_, AppState>,
) -> Result<Vec<SearchResultItem>, String> {
    let plugin_manager = state.plugin_manager.read().await;

    if query.trim().is_empty() {
        return Ok(Vec::new());
    }

    let results = plugin_manager.search(&query);

    log::debug!("搜索查询: '{}' -> {} 个结果", query, results.len());

    Ok(results)
}

/// 执行插件动作
#[tauri::command]
pub async fn execute_plugin_action(
    plugin_id: String,
    action_id: String,
    input: String,
    state: State<'_, AppState>,
) -> Result<PluginExecutionResult, String> {
    let plugin_manager = state.plugin_manager.read().await;

    match plugin_manager
        .execute_plugin_action(&plugin_id, &action_id, &input)
        .await
    {
        Ok(result) => {
            log::info!(
                "插件动作执行完成: {}::{} -> {}",
                plugin_id,
                action_id,
                result.success
            );

            // 发送事件
            let event_bus = state.event_bus.clone();
            let plugin_event = crate::event_bus::PluginEvent::ExecutionComplete {
                plugin_id: plugin_id.clone(),
                action_id: action_id.clone(),
                result: result.result.clone(),
                success: result.success,
            };

            tokio::spawn(async move {
                if let Err(e) = event_bus
                    .emit_plugin_event(plugin_event, "plugin_command".to_string())
                    .await
                {
                    log::error!("发送插件事件失败: {}", e);
                }
            });

            Ok(result)
        }
        Err(e) => {
            log::error!("插件动作执行失败: {}::{} -> {}", plugin_id, action_id, e);
            Err(e.to_string())
        }
    }
}

/// 获取插件列表
#[tauri::command]
pub async fn get_plugin_list(state: State<'_, AppState>) -> Result<Vec<PluginMetadata>, String> {
    let plugin_manager = state.plugin_manager.read().await;
    let plugins = plugin_manager.get_plugin_list();

    log::debug!("获取插件列表: {} 个插件", plugins.len());

    Ok(plugins)
}

/// 安装插件
#[tauri::command]
pub async fn install_plugin(
    plugin_data: String,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    // 这里简化实现，实际应用中需要更复杂的插件安装逻辑
    log::info!("安装插件请求: {}", plugin_data);

    // 发送事件
    let event_bus = state.event_bus.clone();
    let plugin_event = crate::event_bus::PluginEvent::Installed {
        plugin_id: "new_plugin".to_string(),
        version: "1.0.0".to_string(),
    };

    tokio::spawn(async move {
        if let Err(e) = event_bus
            .emit_plugin_event(plugin_event, "plugin_command".to_string())
            .await
        {
            log::error!("发送插件安装事件失败: {}", e);
        }
    });

    Ok(true)
}

/// 卸载插件
#[tauri::command]
pub async fn uninstall_plugin(
    plugin_id: String,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let mut plugin_manager = state.plugin_manager.write().await;

    match plugin_manager.uninstall_plugin(&plugin_id) {
        Ok(_) => {
            log::info!("插件已卸载: {}", plugin_id);

            // 发送事件
            let event_bus = state.event_bus.clone();
            let plugin_event = crate::event_bus::PluginEvent::Uninstalled {
                plugin_id: plugin_id.clone(),
            };

            tokio::spawn(async move {
                if let Err(e) = event_bus
                    .emit_plugin_event(plugin_event, "plugin_command".to_string())
                    .await
                {
                    log::error!("发送插件卸载事件失败: {}", e);
                }
            });

            Ok(true)
        }
        Err(e) => {
            log::error!("插件卸载失败: {} -> {}", plugin_id, e);
            Err(e.to_string())
        }
    }
}

/// 启用/禁用插件
#[tauri::command]
pub async fn toggle_plugin_enabled(
    plugin_id: String,
    enabled: bool,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let mut plugin_manager = state.plugin_manager.write().await;

    match plugin_manager.toggle_plugin(&plugin_id, enabled) {
        Ok(_) => {
            log::info!("插件状态已更改: {} -> {}", plugin_id, enabled);

            // 发送事件
            let event_bus = state.event_bus.clone();
            let plugin_event = crate::event_bus::PluginEvent::ToggleEnabled {
                plugin_id: plugin_id.clone(),
                enabled,
            };

            tokio::spawn(async move {
                if let Err(e) = event_bus
                    .emit_plugin_event(plugin_event, "plugin_command".to_string())
                    .await
                {
                    log::error!("发送插件状态变更事件失败: {}", e);
                }
            });

            Ok(true)
        }
        Err(e) => {
            log::error!("插件状态变更失败: {} -> {}", plugin_id, e);
            Err(e.to_string())
        }
    }
}

/// 获取插件详情
#[tauri::command]
pub async fn get_plugin_details(
    plugin_id: String,
    state: State<'_, AppState>,
) -> Result<Option<PluginMetadata>, String> {
    let plugin_manager = state.plugin_manager.read().await;

    let plugin = plugin_manager.get_plugin(&plugin_id).cloned();

    log::debug!("获取插件详情: {} -> {}", plugin_id, plugin.is_some());

    Ok(plugin)
}
