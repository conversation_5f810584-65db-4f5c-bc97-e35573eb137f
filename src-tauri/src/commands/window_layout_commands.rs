use crate::window_layout_manager::{WindowLayoutConfig, WindowLayoutInfo, WindowLayoutType};
use crate::AppState;
use anyhow::Result;
use tauri::State;

/// 设置窗口布局配置
#[tauri::command]
pub async fn set_window_layout_config(
    config: WindowLayoutConfig,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let mut layout_manager = state.window_layout_manager.write().await;

    layout_manager.set_layout_config(config.clone());

    // 应用新的布局配置
    match layout_manager.apply_layout().await {
        Ok(_) => {
            log::info!("窗口布局配置已更新: {:?}", config.layout_type);
            Ok(true)
        }
        Err(e) => {
            log::error!("更新窗口布局配置失败: {}", e);
            Err(e.to_string())
        }
    }
}

/// 获取窗口布局配置
#[tauri::command]
pub async fn get_window_layout_config(
    state: State<'_, AppState>,
) -> Result<WindowLayoutConfig, String> {
    let layout_manager = state.window_layout_manager.read().await;

    let config = layout_manager.get_layout_config().clone();

    log::debug!("获取窗口布局配置: {:?}", config.layout_type);

    Ok(config)
}

/// 添加窗口到布局
#[tauri::command]
pub async fn add_window_to_layout(
    window_id: String,
    width: u32,
    height: u32,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let mut layout_manager = state.window_layout_manager.write().await;

    match layout_manager
        .add_window_to_layout(window_id.clone(), (width, height))
        .await
    {
        Ok(_) => {
            log::info!("窗口已添加到布局: {} ({}x{})", window_id, width, height);
            Ok(true)
        }
        Err(e) => {
            log::error!("添加窗口到布局失败: {} -> {}", window_id, e);
            Err(e.to_string())
        }
    }
}

/// 从布局中移除窗口
#[tauri::command]
pub async fn remove_window_from_layout(
    window_id: String,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let mut layout_manager = state.window_layout_manager.write().await;

    match layout_manager.remove_window_from_layout(&window_id).await {
        Ok(_) => {
            log::info!("窗口已从布局中移除: {}", window_id);
            Ok(true)
        }
        Err(e) => {
            log::error!("从布局中移除窗口失败: {} -> {}", window_id, e);
            Err(e.to_string())
        }
    }
}

/// 切换布局类型
#[tauri::command]
pub async fn switch_layout_type(
    layout_type: WindowLayoutType,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let mut layout_manager = state.window_layout_manager.write().await;

    match layout_manager.switch_layout_type(layout_type.clone()).await {
        Ok(_) => {
            log::info!("布局类型已切换: {:?}", layout_type);
            Ok(true)
        }
        Err(e) => {
            log::error!("切换布局类型失败: {:?} -> {}", layout_type, e);
            Err(e.to_string())
        }
    }
}

/// 聚焦窗口
#[tauri::command]
pub async fn focus_window_in_layout(
    window_id: String,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let mut layout_manager = state.window_layout_manager.write().await;

    match layout_manager.focus_window(&window_id).await {
        Ok(_) => {
            log::info!("窗口已在布局中聚焦: {}", window_id);

            // 发送窗口聚焦事件
            let event_bus = state.event_bus.clone();
            let window_event = crate::event_bus::WindowEvent::Focused {
                window_id: window_id.clone(),
            };

            tokio::spawn(async move {
                if let Err(e) = event_bus
                    .emit_window_event(window_event, "window_layout_command".to_string())
                    .await
                {
                    log::error!("发送窗口聚焦事件失败: {}", e);
                }
            });

            Ok(true)
        }
        Err(e) => {
            log::error!("在布局中聚焦窗口失败: {} -> {}", window_id, e);
            Err(e.to_string())
        }
    }
}

/// 获取焦点窗口
#[tauri::command]
pub async fn get_focused_window(state: State<'_, AppState>) -> Result<Option<String>, String> {
    let layout_manager = state.window_layout_manager.read().await;

    let focused_window = layout_manager.get_focused_window().map(|s| s.to_string());

    log::debug!("获取焦点窗口: {:?}", focused_window);

    Ok(focused_window)
}

/// 循环聚焦窗口
#[tauri::command]
pub async fn cycle_focus_windows(state: State<'_, AppState>) -> Result<Option<String>, String> {
    let mut layout_manager = state.window_layout_manager.write().await;

    match layout_manager.cycle_focus().await {
        Ok(focused_window) => {
            log::info!("循环聚焦窗口: {:?}", focused_window);

            // 发送窗口聚焦事件
            if let Some(window_id) = &focused_window {
                let event_bus = state.event_bus.clone();
                let window_event = crate::event_bus::WindowEvent::Focused {
                    window_id: window_id.clone(),
                };

                tokio::spawn(async move {
                    if let Err(e) = event_bus
                        .emit_window_event(window_event, "window_layout_command".to_string())
                        .await
                    {
                        log::error!("发送窗口聚焦事件失败: {}", e);
                    }
                });
            }

            Ok(focused_window)
        }
        Err(e) => {
            log::error!("循环聚焦窗口失败: {}", e);
            Err(e.to_string())
        }
    }
}

/// 获取当前布局信息
#[tauri::command]
pub async fn get_current_layout(
    state: State<'_, AppState>,
) -> Result<Vec<WindowLayoutInfo>, String> {
    let layout_manager = state.window_layout_manager.read().await;

    let current_layout = layout_manager
        .get_current_layout()
        .map(|layout| layout.clone())
        .unwrap_or_default();

    log::debug!("获取当前布局信息: {} 个窗口", current_layout.len());

    Ok(current_layout)
}

/// 获取所有布局
#[tauri::command]
pub async fn get_all_layouts(
    state: State<'_, AppState>,
) -> Result<std::collections::HashMap<String, Vec<WindowLayoutInfo>>, String> {
    let layout_manager = state.window_layout_manager.read().await;

    let all_layouts = layout_manager.get_all_layouts().clone();

    log::debug!("获取所有布局: {} 个布局", all_layouts.len());

    Ok(all_layouts)
}

/// 创建新布局
#[tauri::command]
pub async fn create_layout(
    layout_name: String,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let mut layout_manager = state.window_layout_manager.write().await;

    match layout_manager.create_layout(layout_name.clone()) {
        Ok(_) => {
            log::info!("新布局已创建: {}", layout_name);
            Ok(true)
        }
        Err(e) => {
            log::error!("创建布局失败: {} -> {}", layout_name, e);
            Err(e.to_string())
        }
    }
}

/// 删除布局
#[tauri::command]
pub async fn delete_layout(
    layout_name: String,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let mut layout_manager = state.window_layout_manager.write().await;

    match layout_manager.delete_layout(&layout_name) {
        Ok(_) => {
            log::info!("布局已删除: {}", layout_name);
            Ok(true)
        }
        Err(e) => {
            log::error!("删除布局失败: {} -> {}", layout_name, e);
            Err(e.to_string())
        }
    }
}

/// 切换布局
#[tauri::command]
pub async fn switch_layout(
    layout_name: String,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let mut layout_manager = state.window_layout_manager.write().await;

    match layout_manager.switch_layout(&layout_name).await {
        Ok(_) => {
            log::info!("已切换到布局: {}", layout_name);
            Ok(true)
        }
        Err(e) => {
            log::error!("切换布局失败: {} -> {}", layout_name, e);
            Err(e.to_string())
        }
    }
}

/// 获取布局统计信息
#[tauri::command]
pub async fn get_layout_stats(
    state: State<'_, AppState>,
) -> Result<std::collections::HashMap<String, serde_json::Value>, String> {
    let layout_manager = state.window_layout_manager.read().await;

    let stats = layout_manager.get_layout_stats();

    log::debug!("获取布局统计信息: {:?}", stats);

    Ok(stats)
}

/// 应用当前布局
#[tauri::command]
pub async fn apply_current_layout(state: State<'_, AppState>) -> Result<bool, String> {
    let mut layout_manager = state.window_layout_manager.write().await;

    match layout_manager.apply_layout().await {
        Ok(_) => {
            log::info!("当前布局已应用");
            Ok(true)
        }
        Err(e) => {
            log::error!("应用当前布局失败: {}", e);
            Err(e.to_string())
        }
    }
}

/// 获取所有窗口的布局信息
#[tauri::command]
pub async fn get_window_layouts(
    state: State<'_, AppState>,
) -> Result<Vec<WindowLayoutInfo>, String> {
    let layout_manager = state.window_layout_manager.read().await;

    let current_layout = layout_manager
        .get_current_layout()
        .map(|layout| layout.clone())
        .unwrap_or_default();

    log::debug!("获取窗口布局信息: {} 个窗口", current_layout.len());

    Ok(current_layout)
}

/// 重新排列窗口
#[tauri::command]
pub async fn arrange_windows(
    layout_type: WindowLayoutType,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let mut layout_manager = state.window_layout_manager.write().await;

    match layout_manager.switch_layout_type(layout_type.clone()).await {
        Ok(_) => {
            log::info!("窗口重新排列完成: {:?}", layout_type);
            Ok(true)
        }
        Err(e) => {
            log::error!("重新排列窗口失败: {:?} -> {}", layout_type, e);
            Err(e.to_string())
        }
    }
}

/// 聚焦下一个窗口
#[tauri::command]
pub async fn focus_next_window(state: State<'_, AppState>) -> Result<bool, String> {
    let mut layout_manager = state.window_layout_manager.write().await;

    match layout_manager.cycle_focus().await {
        Ok(focused_window) => {
            log::info!("聚焦下一个窗口: {:?}", focused_window);
            Ok(true)
        }
        Err(e) => {
            log::error!("聚焦下一个窗口失败: {}", e);
            Err(e.to_string())
        }
    }
}

/// 聚焦上一个窗口
#[tauri::command]
pub async fn focus_previous_window(state: State<'_, AppState>) -> Result<bool, String> {
    let mut layout_manager = state.window_layout_manager.write().await;

    // 这里我们可以实现反向循环，暂时使用相同的cycle_focus逻辑
    match layout_manager.cycle_focus().await {
        Ok(focused_window) => {
            log::info!("聚焦上一个窗口: {:?}", focused_window);
            Ok(true)
        }
        Err(e) => {
            log::error!("聚焦上一个窗口失败: {}", e);
            Err(e.to_string())
        }
    }
}
