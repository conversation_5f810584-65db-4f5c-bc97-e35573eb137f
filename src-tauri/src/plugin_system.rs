use crate::plugin_manifest::PluginManifestParser;
use anyhow::Result;
use regex::Regex;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;
use uuid::Uuid;

/// 插件类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum PluginMode {
    #[serde(rename = "list")]
    List,
    #[serde(rename = "view")]
    View,
}

/// 插件动作定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PluginAction {
    pub id: String,
    pub name: String,
    pub pattern: String,
    pub description: String,
    pub is_interactive: bool,
}

/// 插件设置定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PluginSetting {
    pub id: String,
    pub title: String,
    pub description: String,
    pub setting_type: String,
    pub default_value: serde_json::Value,
    pub options: Option<Vec<(String, String)>>, // (label, value) pairs
}

/// 插件元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PluginMetadata {
    pub id: String,
    pub name: String,
    pub icon: String,
    pub mode: PluginMode,
    pub keyword: String,
    pub description: String,
    pub aliases: Vec<String>,
    pub actions: Vec<PluginAction>,
    pub settings: Vec<PluginSetting>,
    pub enabled: bool,
    pub version: String,
}

/// 插件搜索结果项
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchResultItem {
    pub id: String,
    pub name: String,
    pub description: String,
    pub icon: Option<String>,
    pub result_type: String, // "plugin" or "action"
    pub plugin_id: Option<String>,
    pub action_id: Option<String>,
    pub score: f32,
}

/// 插件执行结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PluginExecutionResult {
    pub success: bool,
    pub result: String,
    pub error: Option<String>,
    pub result_type: String, // "text", "list", "ui_action", "error"
}

/// 插件管理器
pub struct PluginManager {
    plugins: HashMap<String, PluginMetadata>,
    search_index: HashMap<String, Vec<String>>, // 关键词到插件ID的映射
    manifest_parser: PluginManifestParser,
}

impl PluginManager {
    pub fn new() -> Self {
        let plugins_dir = std::env::current_dir()
            .unwrap_or_else(|_| PathBuf::from("."))
            .join("src")
            .join("plugins");

        let mut manager = Self {
            plugins: HashMap::new(),
            search_index: HashMap::new(),
            manifest_parser: PluginManifestParser::new(plugins_dir),
        };

        // 加载内置插件
        manager.load_builtin_plugins();
        manager
    }

    /// 加载内置插件定义
    fn load_builtin_plugins(&mut self) {
        // 计算器插件
        self.register_plugin(PluginMetadata {
            id: "calculator".to_string(),
            name: "Calculator".to_string(),
            icon: "calculator".to_string(),
            mode: PluginMode::List,
            keyword: "calc".to_string(),
            description: "Basic calculator with mathematical operations".to_string(),
            aliases: vec!["calc".to_string(), "math".to_string()],
            actions: vec![
                PluginAction {
                    id: "calculate".to_string(),
                    name: "Calculate".to_string(),
                    pattern: r"^calc\s+(.+)$".to_string(),
                    description: "Perform mathematical calculation".to_string(),
                    is_interactive: false,
                },
                PluginAction {
                    id: "calculator_interactive".to_string(),
                    name: "Calculator (Interactive)".to_string(),
                    pattern: r"^calculator$".to_string(),
                    description: "Open interactive calculator".to_string(),
                    is_interactive: true,
                },
            ],
            settings: vec![PluginSetting {
                id: "precision".to_string(),
                title: "Precision".to_string(),
                description: "Number of decimal places".to_string(),
                setting_type: "number".to_string(),
                default_value: serde_json::Value::Number(2.into()),
                options: None,
            }],
            enabled: true,
            version: "1.0.0".to_string(),
        });

        // 终端插件
        self.register_plugin(PluginMetadata {
            id: "shell".to_string(),
            name: "Shell Terminal".to_string(),
            icon: "terminal".to_string(),
            mode: PluginMode::View,
            keyword: "shell".to_string(),
            description: "Terminal emulator with command execution".to_string(),
            aliases: vec![
                "shell".to_string(),
                "terminal".to_string(),
                "term".to_string(),
            ],
            actions: vec![PluginAction {
                id: "open_terminal".to_string(),
                name: "Open Terminal".to_string(),
                pattern: r"^(shell|terminal|term)$".to_string(),
                description: "Open terminal window".to_string(),
                is_interactive: false,
            }],
            settings: vec![PluginSetting {
                id: "defaultShell".to_string(),
                title: "Default Shell".to_string(),
                description: "The shell to use for executing commands".to_string(),
                setting_type: "select".to_string(),
                default_value: serde_json::Value::String("bash".to_string()),
                options: Some(vec![
                    ("Bash".to_string(), "bash".to_string()),
                    ("Zsh".to_string(), "zsh".to_string()),
                    ("PowerShell".to_string(), "powershell".to_string()),
                    ("CMD".to_string(), "cmd".to_string()),
                ]),
            }],
            enabled: true,
            version: "1.0.0".to_string(),
        });

        // 设置插件
        self.register_plugin(PluginMetadata {
            id: "settings".to_string(),
            name: "Settings".to_string(),
            icon: "settings".to_string(),
            mode: PluginMode::View,
            keyword: "settings".to_string(),
            description: "Application settings and preferences".to_string(),
            aliases: vec![
                "settings".to_string(),
                "preferences".to_string(),
                "config".to_string(),
            ],
            actions: vec![PluginAction {
                id: "open_settings".to_string(),
                name: "Open Settings".to_string(),
                pattern: r"^(settings|preferences|config)$".to_string(),
                description: "Open application settings".to_string(),
                is_interactive: false,
            }],
            settings: vec![],
            enabled: true,
            version: "1.0.0".to_string(),
        });

        log::info!("已加载 {} 个内置插件", self.plugins.len());
    }

    /// 扫描并加载 TypeScript 插件
    pub async fn scan_typescript_plugins(&mut self) -> Result<()> {
        match self.manifest_parser.scan_plugins().await {
            Ok(manifests) => {
                for ts_manifest in manifests {
                    match self.manifest_parser.convert_to_rust_metadata(&ts_manifest) {
                        Ok(rust_metadata) => {
                            log::info!("加载 TypeScript 插件: {}", rust_metadata.name);
                            self.register_plugin(rust_metadata);
                        }
                        Err(e) => {
                            log::warn!("转换插件失败 {}: {}", ts_manifest.name, e);
                        }
                    }
                }
                log::info!("已扫描并加载 TypeScript 插件");
                Ok(())
            }
            Err(e) => {
                log::error!("扫描 TypeScript 插件失败: {}", e);
                Err(e)
            }
        }
    }

    /// 重新加载指定的 TypeScript 插件
    pub async fn reload_typescript_plugin(&mut self, plugin_path: &std::path::Path) -> Result<()> {
        match self
            .manifest_parser
            .parse_plugin_manifest(plugin_path)
            .await
        {
            Ok(ts_manifest) => match self.manifest_parser.convert_to_rust_metadata(&ts_manifest) {
                Ok(rust_metadata) => {
                    let plugin_id = rust_metadata.id.clone();
                    log::info!("重新加载插件: {}", rust_metadata.name);
                    self.register_plugin(rust_metadata);
                    Ok(())
                }
                Err(e) => {
                    log::error!("转换插件失败: {}", e);
                    Err(e)
                }
            },
            Err(e) => {
                log::error!("解析插件清单失败: {}", e);
                Err(e)
            }
        }
    }

    /// 注册插件
    pub fn register_plugin(&mut self, plugin: PluginMetadata) {
        let plugin_id = plugin.id.clone();

        // 更新搜索索引
        self.update_search_index(&plugin);

        // 注册插件
        self.plugins.insert(plugin_id.clone(), plugin);

        log::info!("已注册插件: {}", plugin_id);
    }

    /// 更新搜索索引
    fn update_search_index(&mut self, plugin: &PluginMetadata) {
        let mut keywords = Vec::new();

        // 添加插件名称和描述的关键词
        keywords.extend(
            plugin
                .name
                .to_lowercase()
                .split_whitespace()
                .map(|s| s.to_string()),
        );
        keywords.extend(
            plugin
                .description
                .to_lowercase()
                .split_whitespace()
                .map(|s| s.to_string()),
        );

        // 添加关键词
        keywords.push(plugin.keyword.to_lowercase());

        // 添加别名
        for alias in &plugin.aliases {
            keywords.push(alias.to_lowercase());
        }

        // 添加动作名称
        for action in &plugin.actions {
            keywords.extend(
                action
                    .name
                    .to_lowercase()
                    .split_whitespace()
                    .map(|s| s.to_string()),
            );
        }

        // 更新索引
        for keyword in keywords {
            self.search_index
                .entry(keyword)
                .or_insert_with(Vec::new)
                .push(plugin.id.clone());
        }
    }

    /// 搜索插件
    pub fn search(&self, query: &str) -> Vec<SearchResultItem> {
        let mut results = Vec::new();
        let query_lower = query.to_lowercase();

        // 遍历所有插件
        for plugin in self.plugins.values() {
            if !plugin.enabled {
                continue;
            }

            // 检查插件名称匹配
            let name_score = self.calculate_match_score(&plugin.name.to_lowercase(), &query_lower);
            if name_score > 0.0 {
                results.push(SearchResultItem {
                    id: plugin.id.clone(),
                    name: plugin.name.clone(),
                    description: plugin.description.clone(),
                    icon: Some(plugin.icon.clone()),
                    result_type: "plugin".to_string(),
                    plugin_id: Some(plugin.id.clone()),
                    action_id: None,
                    score: name_score,
                });
            }

            // 检查动作匹配
            for action in &plugin.actions {
                let action_score = self.calculate_action_match_score(action, &query_lower);
                if action_score > 0.0 {
                    results.push(SearchResultItem {
                        id: format!("{}::{}", plugin.id, action.id),
                        name: action.name.clone(),
                        description: action.description.clone(),
                        icon: Some(plugin.icon.clone()),
                        result_type: "action".to_string(),
                        plugin_id: Some(plugin.id.clone()),
                        action_id: Some(action.id.clone()),
                        score: action_score,
                    });
                }
            }
        }

        // 按分数排序
        results.sort_by(|a, b| {
            b.score
                .partial_cmp(&a.score)
                .unwrap_or(std::cmp::Ordering::Equal)
        });

        // 限制结果数量
        results.truncate(10);

        results
    }

    /// 计算匹配分数
    fn calculate_match_score(&self, text: &str, query: &str) -> f32 {
        if text.starts_with(query) {
            return 1.0;
        }

        if text.contains(query) {
            return 0.8;
        }

        // 模糊匹配
        let words: Vec<&str> = text.split_whitespace().collect();
        for word in words {
            if word.starts_with(query) {
                return 0.6;
            }
        }

        0.0
    }

    /// 计算动作匹配分数
    fn calculate_action_match_score(&self, action: &PluginAction, query: &str) -> f32 {
        // 检查正则表达式匹配
        if let Ok(regex) = Regex::new(&action.pattern) {
            if regex.is_match(query) {
                return 1.0;
            }
        }

        // 检查动作名称匹配
        self.calculate_match_score(&action.name.to_lowercase(), query)
    }

    /// 获取插件列表
    pub fn get_plugin_list(&self) -> Vec<PluginMetadata> {
        self.plugins.values().cloned().collect()
    }

    /// 获取插件
    pub fn get_plugin(&self, plugin_id: &str) -> Option<&PluginMetadata> {
        self.plugins.get(plugin_id)
    }

    /// 执行插件动作
    pub async fn execute_plugin_action(
        &self,
        plugin_id: &str,
        action_id: &str,
        input: &str,
    ) -> Result<PluginExecutionResult> {
        let plugin = self
            .plugins
            .get(plugin_id)
            .ok_or_else(|| anyhow::anyhow!("插件未找到: {}", plugin_id))?;

        if !plugin.enabled {
            return Ok(PluginExecutionResult {
                success: false,
                result: "插件已禁用".to_string(),
                error: Some("Plugin is disabled".to_string()),
                result_type: "error".to_string(),
            });
        }

        let action = plugin
            .actions
            .iter()
            .find(|a| a.id == action_id)
            .ok_or_else(|| anyhow::anyhow!("动作未找到: {}", action_id))?;

        // 模拟插件执行
        let result = match plugin_id {
            "calculator" => self.execute_calculator_action(action, input).await,
            "shell" => self.execute_shell_action(action, input).await,
            "settings" => self.execute_settings_action(action, input).await,
            _ => Ok(PluginExecutionResult {
                success: false,
                result: "未实现的插件".to_string(),
                error: Some("Plugin not implemented".to_string()),
                result_type: "error".to_string(),
            }),
        };

        result
    }

    /// 执行计算器动作
    async fn execute_calculator_action(
        &self,
        action: &PluginAction,
        input: &str,
    ) -> Result<PluginExecutionResult> {
        match action.id.as_str() {
            "calculate" => {
                // 简单的数学计算实现
                let result = self.calculate_expression(input)?;
                Ok(PluginExecutionResult {
                    success: true,
                    result: result.to_string(),
                    error: None,
                    result_type: "text".to_string(),
                })
            }
            "calculator_interactive" => Ok(PluginExecutionResult {
                success: true,
                result: "LIST_MODE_RESULTS::{}".to_string(),
                error: None,
                result_type: "list".to_string(),
            }),
            _ => Ok(PluginExecutionResult {
                success: false,
                result: "未知的计算器动作".to_string(),
                error: Some("Unknown calculator action".to_string()),
                result_type: "error".to_string(),
            }),
        }
    }

    /// 执行终端动作
    async fn execute_shell_action(
        &self,
        action: &PluginAction,
        input: &str,
    ) -> Result<PluginExecutionResult> {
        match action.id.as_str() {
            "open_terminal" => Ok(PluginExecutionResult {
                success: true,
                result: "UI_ACTION::open_terminal".to_string(),
                error: None,
                result_type: "ui_action".to_string(),
            }),
            _ => Ok(PluginExecutionResult {
                success: false,
                result: "未知的终端动作".to_string(),
                error: Some("Unknown shell action".to_string()),
                result_type: "error".to_string(),
            }),
        }
    }

    /// 执行设置动作
    async fn execute_settings_action(
        &self,
        action: &PluginAction,
        input: &str,
    ) -> Result<PluginExecutionResult> {
        match action.id.as_str() {
            "open_settings" => Ok(PluginExecutionResult {
                success: true,
                result: "UI_ACTION::open_settings".to_string(),
                error: None,
                result_type: "ui_action".to_string(),
            }),
            _ => Ok(PluginExecutionResult {
                success: false,
                result: "未知的设置动作".to_string(),
                error: Some("Unknown settings action".to_string()),
                result_type: "error".to_string(),
            }),
        }
    }

    /// 简单的数学表达式计算
    fn calculate_expression(&self, input: &str) -> Result<f64> {
        // 这里使用简单的实现，实际应该使用更强大的数学表达式解析器
        let trimmed = input.trim();

        // 处理基本的四则运算
        if let Some(captures) =
            Regex::new(r"^(\d+(?:\.\d+)?)\s*([+\-*/])\s*(\d+(?:\.\d+)?)$")?.captures(trimmed)
        {
            let left: f64 = captures[1].parse()?;
            let operator = &captures[2];
            let right: f64 = captures[3].parse()?;

            match operator {
                "+" => Ok(left + right),
                "-" => Ok(left - right),
                "*" => Ok(left * right),
                "/" => {
                    if right == 0.0 {
                        Err(anyhow::anyhow!("除零错误"))
                    } else {
                        Ok(left / right)
                    }
                }
                _ => Err(anyhow::anyhow!("不支持的运算符")),
            }
        } else {
            // 尝试解析单个数字
            trimmed
                .parse::<f64>()
                .map_err(|_| anyhow::anyhow!("无效的数学表达式"))
        }
    }

    /// 启用/禁用插件
    pub fn toggle_plugin(&mut self, plugin_id: &str, enabled: bool) -> Result<()> {
        let plugin = self
            .plugins
            .get_mut(plugin_id)
            .ok_or_else(|| anyhow::anyhow!("插件未找到: {}", plugin_id))?;

        plugin.enabled = enabled;
        log::info!(
            "插件 {} 已{}",
            plugin_id,
            if enabled { "启用" } else { "禁用" }
        );

        Ok(())
    }

    /// 卸载插件
    pub fn uninstall_plugin(&mut self, plugin_id: &str) -> Result<()> {
        if self.plugins.remove(plugin_id).is_some() {
            // 清理搜索索引
            self.search_index.retain(|_, plugin_ids| {
                plugin_ids.retain(|id| id != plugin_id);
                !plugin_ids.is_empty()
            });

            log::info!("插件 {} 已卸载", plugin_id);
            Ok(())
        } else {
            Err(anyhow::anyhow!("插件未找到: {}", plugin_id))
        }
    }
}
