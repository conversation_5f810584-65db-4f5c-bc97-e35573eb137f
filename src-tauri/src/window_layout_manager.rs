use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager};

/// 窗口布局类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum WindowLayoutType {
    /// 自由布局
    Free,
    /// 瓦片布局
    Tiled,
    /// 堆叠布局
    Stacked,
    /// 标签页布局
    Tabbed,
    /// 网格布局
    Grid { rows: u32, cols: u32 },
}

/// 窗口布局配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WindowLayoutConfig {
    pub layout_type: WindowLayoutType,
    pub screen_bounds: ScreenBounds,
    pub padding: u32,
    pub gap: u32,
    pub auto_arrange: bool,
    pub focus_follows_mouse: bool,
    pub snap_to_grid: bool,
    pub grid_size: u32,
}

impl Default for WindowLayoutConfig {
    fn default() -> Self {
        Self {
            layout_type: WindowLayoutType::Free,
            screen_bounds: ScreenBounds {
                x: 0,
                y: 0,
                width: 1920,
                height: 1080,
            },
            padding: 10,
            gap: 5,
            auto_arrange: false,
            focus_follows_mouse: false,
            snap_to_grid: false,
            grid_size: 20,
        }
    }
}

/// 屏幕边界
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScreenBounds {
    pub x: i32,
    pub y: i32,
    pub width: u32,
    pub height: u32,
}

/// 窗口布局信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WindowLayoutInfo {
    pub window_id: String,
    pub position: (i32, i32),
    pub size: (u32, u32),
    pub z_order: i32,
    pub is_focused: bool,
    pub layout_slot: Option<usize>,
}

/// 窗口布局管理器
pub struct WindowLayoutManager {
    app_handle: Option<AppHandle>,
    config: WindowLayoutConfig,
    layouts: HashMap<String, Vec<WindowLayoutInfo>>,
    current_layout: String,
    focus_stack: Vec<String>,
    z_order_counter: i32,
}

impl WindowLayoutManager {
    pub fn new() -> Self {
        Self {
            app_handle: None,
            config: WindowLayoutConfig::default(),
            layouts: HashMap::new(),
            current_layout: "default".to_string(),
            focus_stack: Vec::new(),
            z_order_counter: 0,
        }
    }

    /// 初始化布局管理器
    pub async fn initialize(&mut self, app_handle: AppHandle) {
        self.app_handle = Some(app_handle);

        // 创建默认布局
        self.layouts.insert("default".to_string(), Vec::new());

        log::info!("窗口布局管理器已初始化");
    }

    /// 设置布局配置
    pub fn set_layout_config(&mut self, config: WindowLayoutConfig) {
        self.config = config;
        log::info!("窗口布局配置已更新: {:?}", self.config.layout_type);
    }

    /// 获取布局配置
    pub fn get_layout_config(&self) -> &WindowLayoutConfig {
        &self.config
    }

    /// 添加窗口到布局
    pub async fn add_window_to_layout(
        &mut self,
        window_id: String,
        size: (u32, u32),
    ) -> Result<()> {
        let app_handle = self
            .app_handle
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("应用句柄未初始化"))?;

        // 计算窗口位置
        let position = self.calculate_window_position(&window_id, size).await?;

        // 创建窗口布局信息
        let layout_info = WindowLayoutInfo {
            window_id: window_id.clone(),
            position,
            size,
            z_order: self.next_z_order(),
            is_focused: false,
            layout_slot: None,
        };

        // 添加到当前布局
        if let Some(layout) = self.layouts.get_mut(&self.current_layout) {
            layout.push(layout_info);
        }

        // 应用布局
        self.apply_layout().await?;

        log::info!(
            "窗口已添加到布局: {} at ({}, {})",
            window_id,
            position.0,
            position.1
        );
        Ok(())
    }

    /// 从布局中移除窗口
    pub async fn remove_window_from_layout(&mut self, window_id: &str) -> Result<()> {
        // 从当前布局中移除
        if let Some(layout) = self.layouts.get_mut(&self.current_layout) {
            layout.retain(|info| info.window_id != window_id);
        }

        // 从焦点堆栈中移除
        self.focus_stack.retain(|id| id != window_id);

        // 重新应用布局
        self.apply_layout().await?;

        log::info!("窗口已从布局中移除: {}", window_id);
        Ok(())
    }

    /// 计算窗口位置
    async fn calculate_window_position(
        &self,
        window_id: &str,
        size: (u32, u32),
    ) -> Result<(i32, i32)> {
        match &self.config.layout_type {
            WindowLayoutType::Free => {
                // 自由布局 - 使用级联放置
                let current_layout = self.layouts.get(&self.current_layout).unwrap();
                let offset = (current_layout.len() as i32) * 30;
                Ok((
                    self.config.screen_bounds.x + self.config.padding as i32 + offset,
                    self.config.screen_bounds.y + self.config.padding as i32 + offset,
                ))
            }
            WindowLayoutType::Tiled => {
                // 瓦片布局 - 平铺所有窗口
                self.calculate_tiled_position(size).await
            }
            WindowLayoutType::Grid { rows, cols } => {
                // 网格布局
                self.calculate_grid_position(*rows, *cols, size).await
            }
            WindowLayoutType::Stacked => {
                // 堆叠布局 - 所有窗口在同一位置
                Ok((
                    self.config.screen_bounds.x + self.config.padding as i32,
                    self.config.screen_bounds.y + self.config.padding as i32,
                ))
            }
            WindowLayoutType::Tabbed => {
                // 标签页布局 - 类似堆叠但有标签指示
                Ok((
                    self.config.screen_bounds.x + self.config.padding as i32,
                    self.config.screen_bounds.y + self.config.padding as i32 + 30, // 为标签页留出空间
                ))
            }
        }
    }

    /// 计算瓦片布局位置
    async fn calculate_tiled_position(&self, size: (u32, u32)) -> Result<(i32, i32)> {
        let current_layout = self.layouts.get(&self.current_layout).unwrap();
        let window_count = current_layout.len() + 1;

        // 计算网格维度
        let cols = (window_count as f64).sqrt().ceil() as u32;
        let rows = (window_count as f64 / cols as f64).ceil() as u32;

        // 计算可用空间
        let available_width = self.config.screen_bounds.width
            - (self.config.padding * 2)
            - (self.config.gap * (cols - 1));
        let available_height = self.config.screen_bounds.height
            - (self.config.padding * 2)
            - (self.config.gap * (rows - 1));

        // 计算每个窗口的大小
        let tile_width = available_width / cols;
        let tile_height = available_height / rows;

        // 计算当前窗口的位置
        let index = current_layout.len();
        let row = index / cols as usize;
        let col = index % cols as usize;

        let x = self.config.screen_bounds.x
            + self.config.padding as i32
            + (col as i32 * (tile_width + self.config.gap) as i32);
        let y = self.config.screen_bounds.y
            + self.config.padding as i32
            + (row as i32 * (tile_height + self.config.gap) as i32);

        Ok((x, y))
    }

    /// 计算网格布局位置
    async fn calculate_grid_position(
        &self,
        rows: u32,
        cols: u32,
        size: (u32, u32),
    ) -> Result<(i32, i32)> {
        let current_layout = self.layouts.get(&self.current_layout).unwrap();
        let index = current_layout.len();

        // 计算网格位置
        let row = (index / cols as usize) % rows as usize;
        let col = index % cols as usize;

        // 计算每个网格单元的大小
        let cell_width = (self.config.screen_bounds.width
            - (self.config.padding * 2)
            - (self.config.gap * (cols - 1)))
            / cols;
        let cell_height = (self.config.screen_bounds.height
            - (self.config.padding * 2)
            - (self.config.gap * (rows - 1)))
            / rows;

        let x = self.config.screen_bounds.x
            + self.config.padding as i32
            + (col as i32 * (cell_width + self.config.gap) as i32);
        let y = self.config.screen_bounds.y
            + self.config.padding as i32
            + (row as i32 * (cell_height + self.config.gap) as i32);

        Ok((x, y))
    }

    /// 应用布局
    pub async fn apply_layout(&mut self) -> Result<()> {
        let app_handle = self
            .app_handle
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("应用句柄未初始化"))?;

        if let Some(layout) = self.layouts.get(&self.current_layout) {
            for window_info in layout {
                if let Some(window) = app_handle.get_webview_window(&window_info.window_id) {
                    // 设置窗口位置
                    let _ = window.set_position(tauri::Position::Physical(
                        tauri::PhysicalPosition::new(
                            window_info.position.0,
                            window_info.position.1,
                        ),
                    ));

                    // 设置窗口大小
                    let _ = window.set_size(tauri::Size::Physical(tauri::PhysicalSize::new(
                        window_info.size.0,
                        window_info.size.1,
                    )));
                }
            }
        }

        Ok(())
    }

    /// 切换布局类型
    pub async fn switch_layout_type(&mut self, layout_type: WindowLayoutType) -> Result<()> {
        self.config.layout_type = layout_type;
        self.recalculate_all_positions().await?;
        self.apply_layout().await?;

        log::info!("布局类型已切换: {:?}", self.config.layout_type);
        Ok(())
    }

    /// 重新计算所有窗口位置
    async fn recalculate_all_positions(&mut self) -> Result<()> {
        let current_layout_name = self.current_layout.clone();
        let window_infos = if let Some(layout) = self.layouts.get(&current_layout_name) {
            layout.clone()
        } else {
            return Ok(());
        };

        // 清空当前布局
        if let Some(layout) = self.layouts.get_mut(&current_layout_name) {
            layout.clear();
        }

        // 重新计算每个窗口的位置
        for window_info in window_infos {
            let position = self
                .calculate_window_position(&window_info.window_id, window_info.size)
                .await?;

            if let Some(layout) = self.layouts.get_mut(&current_layout_name) {
                layout.push(WindowLayoutInfo {
                    position,
                    ..window_info
                });
            }
        }

        Ok(())
    }

    /// 聚焦窗口
    pub async fn focus_window(&mut self, window_id: &str) -> Result<()> {
        // 更新焦点堆栈
        self.focus_stack.retain(|id| id != window_id);
        self.focus_stack.push(window_id.to_string());

        // 获取新的 z_order，避免借用冲突
        let new_z_order = self.next_z_order();

        // 更新布局信息
        if let Some(layout) = self.layouts.get_mut(&self.current_layout) {
            for window_info in layout {
                window_info.is_focused = window_info.window_id == window_id;
                if window_info.is_focused {
                    window_info.z_order = new_z_order;
                }
            }
        }

        log::info!("窗口已聚焦: {}", window_id);
        Ok(())
    }

    /// 获取焦点窗口
    pub fn get_focused_window(&self) -> Option<&str> {
        self.focus_stack.last().map(|s| s.as_str())
    }

    /// 循环聚焦窗口
    pub async fn cycle_focus(&mut self) -> Result<Option<String>> {
        if self.focus_stack.is_empty() {
            return Ok(None);
        }

        // 获取当前焦点窗口的下一个窗口
        let current_focused = self.focus_stack.last().cloned();
        let current_layout_name = self.current_layout.clone();

        // 提取需要的数据，避免借用冲突
        let next_window_id = if let Some(current_layout) = self.layouts.get(&current_layout_name) {
            if let Some(current_id) = current_focused {
                let current_index = current_layout
                    .iter()
                    .position(|info| info.window_id == current_id);
                if let Some(index) = current_index {
                    let next_index = (index + 1) % current_layout.len();
                    Some(current_layout[next_index].window_id.clone())
                } else {
                    // 如果找不到当前窗口，聚焦第一个窗口
                    current_layout.first().map(|w| w.window_id.clone())
                }
            } else {
                // 如果没有当前焦点，聚焦第一个窗口
                current_layout.first().map(|w| w.window_id.clone())
            }
        } else {
            None
        };

        if let Some(window_id) = next_window_id {
            self.focus_window(&window_id).await?;
            return Ok(Some(window_id));
        }

        Ok(None)
    }

    /// 获取下一个 Z 顺序
    fn next_z_order(&mut self) -> i32 {
        self.z_order_counter += 1;
        self.z_order_counter
    }

    /// 获取当前布局信息
    pub fn get_current_layout(&self) -> Option<&Vec<WindowLayoutInfo>> {
        self.layouts.get(&self.current_layout)
    }

    /// 获取所有布局
    pub fn get_all_layouts(&self) -> &HashMap<String, Vec<WindowLayoutInfo>> {
        &self.layouts
    }

    /// 创建新布局
    pub fn create_layout(&mut self, layout_name: String) -> Result<()> {
        if self.layouts.contains_key(&layout_name) {
            return Err(anyhow::anyhow!("布局已存在: {}", layout_name));
        }

        self.layouts.insert(layout_name.clone(), Vec::new());
        log::info!("新布局已创建: {}", layout_name);
        Ok(())
    }

    /// 删除布局
    pub fn delete_layout(&mut self, layout_name: &str) -> Result<()> {
        if layout_name == "default" {
            return Err(anyhow::anyhow!("不能删除默认布局"));
        }

        if layout_name == self.current_layout {
            return Err(anyhow::anyhow!("不能删除当前布局"));
        }

        self.layouts.remove(layout_name);
        log::info!("布局已删除: {}", layout_name);
        Ok(())
    }

    /// 切换布局
    pub async fn switch_layout(&mut self, layout_name: &str) -> Result<()> {
        if !self.layouts.contains_key(layout_name) {
            return Err(anyhow::anyhow!("布局不存在: {}", layout_name));
        }

        self.current_layout = layout_name.to_string();
        self.apply_layout().await?;

        log::info!("已切换到布局: {}", layout_name);
        Ok(())
    }

    /// 获取布局统计信息
    pub fn get_layout_stats(&self) -> HashMap<String, serde_json::Value> {
        let mut stats = HashMap::new();

        let total_layouts = self.layouts.len();
        let current_layout_windows = self
            .layouts
            .get(&self.current_layout)
            .map(|l| l.len())
            .unwrap_or(0);
        let focused_window = self.get_focused_window().map(|s| s.to_string());

        stats.insert(
            "total_layouts".to_string(),
            serde_json::Value::Number(total_layouts.into()),
        );
        stats.insert(
            "current_layout".to_string(),
            serde_json::Value::String(self.current_layout.clone()),
        );
        stats.insert(
            "current_layout_windows".to_string(),
            serde_json::Value::Number(current_layout_windows.into()),
        );
        stats.insert(
            "layout_type".to_string(),
            serde_json::to_value(&self.config.layout_type).unwrap_or(serde_json::Value::Null),
        );

        if let Some(focused) = focused_window {
            stats.insert(
                "focused_window".to_string(),
                serde_json::Value::String(focused),
            );
        }

        stats
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_layout_manager_basic() {
        let mut manager = WindowLayoutManager::new();

        // 测试默认配置
        assert!(matches!(manager.config.layout_type, WindowLayoutType::Free));
        assert_eq!(manager.current_layout, "default");

        // 测试布局创建
        manager.create_layout("test".to_string()).unwrap();
        assert!(manager.layouts.contains_key("test"));

        // 测试布局删除
        manager.delete_layout("test").unwrap();
        assert!(!manager.layouts.contains_key("test"));
    }

    #[tokio::test]
    async fn test_window_position_calculation() {
        let manager = WindowLayoutManager::new();

        // 测试自由布局位置计算
        let position = manager
            .calculate_window_position("test", (800, 600))
            .await
            .unwrap();
        assert_eq!(position, (10, 10)); // 基于默认配置
    }
}
