//! # 多窗口布局引擎
//!
//! 提供高级布局算法、动画系统和性能优化功能
//! 支持智能空间分割、布局模板和自适应布局

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::{Duration, Instant};

/// 窗口位置和大小信息
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct WindowBounds {
    pub x: f64,
    pub y: f64,
    pub width: f64,
    pub height: f64,
}

/// 布局动画配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LayoutAnimation {
    pub duration: Duration,
    pub easing: EasingFunction,
    pub enabled: bool,
}

/// 缓动函数类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EasingFunction {
    Linear,
    EaseInOut,
    EaseOut,
    EaseIn,
    <PERSON><PERSON>ce,
    Elastic,
}

/// 布局约束
#[derive(<PERSON>bug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct LayoutConstraints {
    pub min_window_width: f64,
    pub min_window_height: f64,
    pub max_window_width: Option<f64>,
    pub max_window_height: Option<f64>,
    pub window_gap: f64,
    pub screen_margin: f64,
}

/// 屏幕信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScreenInfo {
    pub width: f64,
    pub height: f64,
    pub scale_factor: f64,
    pub workarea: WindowBounds, // 可用工作区域（排除任务栏等）
}

/// 布局模板
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LayoutTemplate {
    pub id: String,
    pub name: String,
    pub description: String,
    pub layout_type: String,
    pub config: LayoutTemplateConfig,
    pub created_at: DateTime<Utc>,
    pub usage_count: u32,
}

/// 布局模板配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LayoutTemplateConfig {
    pub primary_ratio: f64,         // 主窗口占比 (0.0-1.0)
    pub secondary_ratio: f64,       // 次窗口占比
    pub direction: SplitDirection,  // 分割方向
    pub max_windows: Option<usize>, // 最大窗口数
    pub auto_balance: bool,         // 自动平衡
}

/// 分割方向
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SplitDirection {
    Horizontal, // 水平分割
    Vertical,   // 垂直分割
    Auto,       // 自动选择
}

/// BSP (Binary Space Partitioning) 节点
#[derive(Debug, Clone)]
pub struct BspNode {
    pub bounds: WindowBounds,
    pub window_id: Option<String>,
    pub left: Option<Box<BspNode>>,
    pub right: Option<Box<BspNode>>,
    pub split_direction: SplitDirection,
    pub split_ratio: f64,
}

/// 布局算法统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LayoutStats {
    pub calculation_time: Duration,
    pub windows_processed: usize,
    pub layout_efficiency: f64, // 空间利用率
    pub balance_score: f64,     // 布局平衡度
}

/// 增强的布局引擎
pub struct LayoutEngine {
    pub screen_info: ScreenInfo,
    pub constraints: LayoutConstraints,
    pub animation: LayoutAnimation,
    pub templates: HashMap<String, LayoutTemplate>,
    pub bsp_tree: Option<BspNode>,
    pub performance_cache: HashMap<String, (LayoutStats, Instant)>,
}

impl Default for LayoutConstraints {
    fn default() -> Self {
        Self {
            min_window_width: 300.0,
            min_window_height: 200.0,
            max_window_width: None,
            max_window_height: None,
            window_gap: 8.0,
            screen_margin: 16.0,
        }
    }
}

impl Default for LayoutAnimation {
    fn default() -> Self {
        Self {
            duration: Duration::from_millis(300),
            easing: EasingFunction::EaseInOut,
            enabled: true,
        }
    }
}

impl LayoutEngine {
    /// 创建新的布局引擎
    pub fn new(screen_info: ScreenInfo) -> Self {
        Self {
            screen_info,
            constraints: LayoutConstraints::default(),
            animation: LayoutAnimation::default(),
            templates: Self::default_templates(),
            bsp_tree: None,
            performance_cache: HashMap::new(),
        }
    }

    /// 获取默认布局模板
    fn default_templates() -> HashMap<String, LayoutTemplate> {
        let mut templates = HashMap::new();

        // 主副窗口模板
        templates.insert(
            "main-side".to_string(),
            LayoutTemplate {
                id: "main-side".to_string(),
                name: "主副窗口".to_string(),
                description: "一个主窗口占据大部分空间，副窗口在右侧".to_string(),
                layout_type: "Tiled".to_string(),
                config: LayoutTemplateConfig {
                    primary_ratio: 0.7,
                    secondary_ratio: 0.3,
                    direction: SplitDirection::Vertical,
                    max_windows: Some(8),
                    auto_balance: true,
                },
                created_at: Utc::now(),
                usage_count: 0,
            },
        );

        // 三列布局模板
        templates.insert(
            "three-column".to_string(),
            LayoutTemplate {
                id: "three-column".to_string(),
                name: "三列布局".to_string(),
                description: "将屏幕分为三个相等的列".to_string(),
                layout_type: "Grid".to_string(),
                config: LayoutTemplateConfig {
                    primary_ratio: 0.33,
                    secondary_ratio: 0.33,
                    direction: SplitDirection::Vertical,
                    max_windows: Some(12),
                    auto_balance: true,
                },
                created_at: Utc::now(),
                usage_count: 0,
            },
        );

        // 黄金比例模板
        templates.insert(
            "golden-ratio".to_string(),
            LayoutTemplate {
                id: "golden-ratio".to_string(),
                name: "黄金比例".to_string(),
                description: "使用黄金比例(1.618)分割空间".to_string(),
                layout_type: "Tiled".to_string(),
                config: LayoutTemplateConfig {
                    primary_ratio: 0.618,
                    secondary_ratio: 0.382,
                    direction: SplitDirection::Auto,
                    max_windows: Some(6),
                    auto_balance: false,
                },
                created_at: Utc::now(),
                usage_count: 0,
            },
        );

        templates
    }

    /// 智能平铺布局算法
    pub fn auto_tile_layout(
        &mut self,
        window_ids: &[String],
    ) -> Result<HashMap<String, WindowBounds>, String> {
        if window_ids.is_empty() {
            return Ok(HashMap::new());
        }

        let start_time = Instant::now();
        let workarea = &self.screen_info.workarea;

        // 根据窗口数量选择最佳布局策略
        let layout_bounds = match window_ids.len() {
            1 => self.single_window_layout(workarea),
            2 => self.two_window_layout(workarea),
            3..=4 => self.quad_layout(workarea, window_ids.len()),
            5..=8 => self.grid_layout(workarea, window_ids.len()),
            _ => {
                // 创建BSP树并提取边界
                let root = self.build_bsp_tree(workarea.clone(), window_ids);
                let mut bounds = Vec::new();
                self.extract_window_bounds(&root, &mut bounds);
                // 稍后更新BSP树状态
                bounds
            }
        };

        // 创建窗口ID到位置的映射
        let mut result = HashMap::new();
        for (i, window_id) in window_ids.iter().enumerate() {
            if let Some(bounds) = layout_bounds.get(i) {
                result.insert(window_id.clone(), bounds.clone());
            }
        }

        // 如果使用了BSP布局，更新BSP树状态
        if window_ids.len() > 8 {
            let root = self.build_bsp_tree(workarea.clone(), window_ids);
            self.bsp_tree = Some(root);
        }

        // 记录性能统计
        let calculation_time = start_time.elapsed();
        let stats = LayoutStats {
            calculation_time,
            windows_processed: window_ids.len(),
            layout_efficiency: self.calculate_efficiency(&result),
            balance_score: self.calculate_balance_score(&result),
        };
        self.performance_cache
            .insert("auto_tile".to_string(), (stats, Instant::now()));

        Ok(result)
    }

    /// 单窗口布局
    fn single_window_layout(&self, workarea: &WindowBounds) -> Vec<WindowBounds> {
        vec![WindowBounds {
            x: workarea.x + self.constraints.screen_margin,
            y: workarea.y + self.constraints.screen_margin,
            width: workarea.width - 2.0 * self.constraints.screen_margin,
            height: workarea.height - 2.0 * self.constraints.screen_margin,
        }]
    }

    /// 双窗口布局
    fn two_window_layout(&self, workarea: &WindowBounds) -> Vec<WindowBounds> {
        let available_width =
            workarea.width - 2.0 * self.constraints.screen_margin - self.constraints.window_gap;
        let available_height = workarea.height - 2.0 * self.constraints.screen_margin;

        // 根据屏幕比例决定分割方向
        let horizontal_split = workarea.width > workarea.height * 1.2;

        if horizontal_split {
            // 垂直分割（左右布局）
            let window_width = available_width / 2.0;
            vec![
                WindowBounds {
                    x: workarea.x + self.constraints.screen_margin,
                    y: workarea.y + self.constraints.screen_margin,
                    width: window_width,
                    height: available_height,
                },
                WindowBounds {
                    x: workarea.x
                        + self.constraints.screen_margin
                        + window_width
                        + self.constraints.window_gap,
                    y: workarea.y + self.constraints.screen_margin,
                    width: window_width,
                    height: available_height,
                },
            ]
        } else {
            // 水平分割（上下布局）
            let window_height = (available_height - self.constraints.window_gap) / 2.0;
            vec![
                WindowBounds {
                    x: workarea.x + self.constraints.screen_margin,
                    y: workarea.y + self.constraints.screen_margin,
                    width: available_width,
                    height: window_height,
                },
                WindowBounds {
                    x: workarea.x + self.constraints.screen_margin,
                    y: workarea.y
                        + self.constraints.screen_margin
                        + window_height
                        + self.constraints.window_gap,
                    width: available_width,
                    height: window_height,
                },
            ]
        }
    }

    /// 四宫格布局
    fn quad_layout(&self, workarea: &WindowBounds, window_count: usize) -> Vec<WindowBounds> {
        let available_width =
            workarea.width - 2.0 * self.constraints.screen_margin - self.constraints.window_gap;
        let available_height =
            workarea.height - 2.0 * self.constraints.screen_margin - self.constraints.window_gap;

        let window_width = available_width / 2.0;
        let window_height = available_height / 2.0;

        let mut bounds = Vec::new();
        let positions = [
            (0.0, 0.0),                                         // 左上
            (window_width + self.constraints.window_gap, 0.0),  // 右上
            (0.0, window_height + self.constraints.window_gap), // 左下
            (
                window_width + self.constraints.window_gap,
                window_height + self.constraints.window_gap,
            ), // 右下
        ];

        for i in 0..window_count.min(4) {
            let (offset_x, offset_y) = positions[i];
            bounds.push(WindowBounds {
                x: workarea.x + self.constraints.screen_margin + offset_x,
                y: workarea.y + self.constraints.screen_margin + offset_y,
                width: window_width,
                height: window_height,
            });
        }

        bounds
    }

    /// 网格布局
    fn grid_layout(&self, workarea: &WindowBounds, window_count: usize) -> Vec<WindowBounds> {
        let (cols, rows) = self.calculate_grid_dimensions(window_count);

        let available_width = workarea.width
            - 2.0 * self.constraints.screen_margin
            - (cols - 1) as f64 * self.constraints.window_gap;
        let available_height = workarea.height
            - 2.0 * self.constraints.screen_margin
            - (rows - 1) as f64 * self.constraints.window_gap;

        let window_width = available_width / cols as f64;
        let window_height = available_height / rows as f64;

        let mut bounds = Vec::new();

        for i in 0..window_count {
            let row = i / cols;
            let col = i % cols;

            let x = workarea.x
                + self.constraints.screen_margin
                + col as f64 * (window_width + self.constraints.window_gap);
            let y = workarea.y
                + self.constraints.screen_margin
                + row as f64 * (window_height + self.constraints.window_gap);

            bounds.push(WindowBounds {
                x,
                y,
                width: window_width,
                height: window_height,
            });
        }

        bounds
    }

    /// 构建BSP树
    fn build_bsp_tree(&self, bounds: WindowBounds, window_ids: &[String]) -> BspNode {
        if window_ids.len() <= 1 {
            return BspNode {
                bounds,
                window_id: window_ids.first().cloned(),
                left: None,
                right: None,
                split_direction: SplitDirection::Auto,
                split_ratio: 0.5,
            };
        }

        // 选择分割方向（优先长边分割）
        let split_direction = if bounds.width > bounds.height {
            SplitDirection::Vertical
        } else {
            SplitDirection::Horizontal
        };

        // 动态计算分割比例（基于窗口数量）
        let left_count = window_ids.len() / 2;
        let split_ratio = left_count as f64 / window_ids.len() as f64;

        let (left_bounds, right_bounds) =
            self.split_bounds(&bounds, split_direction.clone(), split_ratio);

        let left_windows = &window_ids[..left_count];
        let right_windows = &window_ids[left_count..];

        BspNode {
            bounds,
            window_id: None,
            left: Some(Box::new(self.build_bsp_tree(left_bounds, left_windows))),
            right: Some(Box::new(self.build_bsp_tree(right_bounds, right_windows))),
            split_direction,
            split_ratio,
        }
    }

    /// 分割边界
    fn split_bounds(
        &self,
        bounds: &WindowBounds,
        direction: SplitDirection,
        ratio: f64,
    ) -> (WindowBounds, WindowBounds) {
        match direction {
            SplitDirection::Vertical => {
                let split_x = bounds.x + bounds.width * ratio - self.constraints.window_gap / 2.0;
                (
                    WindowBounds {
                        x: bounds.x,
                        y: bounds.y,
                        width: bounds.width * ratio - self.constraints.window_gap / 2.0,
                        height: bounds.height,
                    },
                    WindowBounds {
                        x: split_x + self.constraints.window_gap,
                        y: bounds.y,
                        width: bounds.width * (1.0 - ratio) - self.constraints.window_gap / 2.0,
                        height: bounds.height,
                    },
                )
            }
            SplitDirection::Horizontal => {
                let split_y = bounds.y + bounds.height * ratio - self.constraints.window_gap / 2.0;
                (
                    WindowBounds {
                        x: bounds.x,
                        y: bounds.y,
                        width: bounds.width,
                        height: bounds.height * ratio - self.constraints.window_gap / 2.0,
                    },
                    WindowBounds {
                        x: bounds.x,
                        y: split_y + self.constraints.window_gap,
                        width: bounds.width,
                        height: bounds.height * (1.0 - ratio) - self.constraints.window_gap / 2.0,
                    },
                )
            }
            SplitDirection::Auto => {
                // 自动选择最佳分割方向
                if bounds.width > bounds.height {
                    self.split_bounds(bounds, SplitDirection::Vertical, ratio)
                } else {
                    self.split_bounds(bounds, SplitDirection::Horizontal, ratio)
                }
            }
        }
    }

    /// 从BSP树提取窗口边界
    fn extract_window_bounds(&self, node: &BspNode, bounds: &mut Vec<WindowBounds>) {
        if let Some(_) = &node.window_id {
            bounds.push(node.bounds.clone());
        } else {
            if let Some(left) = &node.left {
                self.extract_window_bounds(left, bounds);
            }
            if let Some(right) = &node.right {
                self.extract_window_bounds(right, bounds);
            }
        }
    }

    /// 计算最佳网格维度
    fn calculate_grid_dimensions(&self, window_count: usize) -> (usize, usize) {
        let aspect_ratio = self.screen_info.workarea.width / self.screen_info.workarea.height;

        // 寻找最接近屏幕比例的网格配置
        let mut best_cols = 1;
        let mut best_rows = window_count;
        let mut best_diff = f64::INFINITY;

        for cols in 1..=window_count {
            let rows = (window_count as f64 / cols as f64).ceil() as usize;
            let grid_ratio = cols as f64 / rows as f64;
            let diff = (grid_ratio - aspect_ratio).abs();

            if diff < best_diff {
                best_diff = diff;
                best_cols = cols;
                best_rows = rows;
            }
        }

        (best_cols, best_rows)
    }

    /// 应用布局模板
    pub fn apply_template(
        &mut self,
        template_id: &str,
        window_ids: &[String],
    ) -> Result<HashMap<String, WindowBounds>, String> {
        let template = self
            .templates
            .get(template_id)
            .ok_or_else(|| format!("Template '{}' not found", template_id))?
            .clone();

        // 更新使用计数
        if let Some(t) = self.templates.get_mut(template_id) {
            t.usage_count += 1;
        }

        match template.config.direction {
            SplitDirection::Vertical => self.apply_vertical_template(&template, window_ids),
            SplitDirection::Horizontal => self.apply_horizontal_template(&template, window_ids),
            SplitDirection::Auto => {
                // 根据屏幕比例自动选择
                if self.screen_info.workarea.width > self.screen_info.workarea.height {
                    self.apply_vertical_template(&template, window_ids)
                } else {
                    self.apply_horizontal_template(&template, window_ids)
                }
            }
        }
    }

    /// 应用垂直模板
    fn apply_vertical_template(
        &self,
        template: &LayoutTemplate,
        window_ids: &[String],
    ) -> Result<HashMap<String, WindowBounds>, String> {
        if window_ids.is_empty() {
            return Ok(HashMap::new());
        }

        let workarea = &self.screen_info.workarea;
        let available_width =
            workarea.width - 2.0 * self.constraints.screen_margin - self.constraints.window_gap;

        let primary_width = available_width * template.config.primary_ratio;
        let secondary_width = available_width * template.config.secondary_ratio;

        let mut result = HashMap::new();

        // 主窗口
        if let Some(window_id) = window_ids.first() {
            result.insert(
                window_id.clone(),
                WindowBounds {
                    x: workarea.x + self.constraints.screen_margin,
                    y: workarea.y + self.constraints.screen_margin,
                    width: primary_width,
                    height: workarea.height - 2.0 * self.constraints.screen_margin,
                },
            );
        }

        // 副窗口（堆叠在右侧）
        if window_ids.len() > 1 {
            let secondary_windows = &window_ids[1..];
            let window_height = if secondary_windows.len() > 1 {
                (workarea.height
                    - 2.0 * self.constraints.screen_margin
                    - (secondary_windows.len() - 1) as f64 * self.constraints.window_gap)
                    / secondary_windows.len() as f64
            } else {
                workarea.height - 2.0 * self.constraints.screen_margin
            };

            for (i, window_id) in secondary_windows.iter().enumerate() {
                let y = workarea.y
                    + self.constraints.screen_margin
                    + i as f64 * (window_height + self.constraints.window_gap);
                result.insert(
                    window_id.clone(),
                    WindowBounds {
                        x: workarea.x
                            + self.constraints.screen_margin
                            + primary_width
                            + self.constraints.window_gap,
                        y,
                        width: secondary_width,
                        height: window_height,
                    },
                );
            }
        }

        Ok(result)
    }

    /// 应用水平模板
    fn apply_horizontal_template(
        &self,
        template: &LayoutTemplate,
        window_ids: &[String],
    ) -> Result<HashMap<String, WindowBounds>, String> {
        if window_ids.is_empty() {
            return Ok(HashMap::new());
        }

        let workarea = &self.screen_info.workarea;
        let available_height =
            workarea.height - 2.0 * self.constraints.screen_margin - self.constraints.window_gap;

        let primary_height = available_height * template.config.primary_ratio;
        let secondary_height = available_height * template.config.secondary_ratio;

        let mut result = HashMap::new();

        // 主窗口
        if let Some(window_id) = window_ids.first() {
            result.insert(
                window_id.clone(),
                WindowBounds {
                    x: workarea.x + self.constraints.screen_margin,
                    y: workarea.y + self.constraints.screen_margin,
                    width: workarea.width - 2.0 * self.constraints.screen_margin,
                    height: primary_height,
                },
            );
        }

        // 副窗口（堆叠在下方）
        if window_ids.len() > 1 {
            let secondary_windows = &window_ids[1..];
            let window_width = if secondary_windows.len() > 1 {
                (workarea.width
                    - 2.0 * self.constraints.screen_margin
                    - (secondary_windows.len() - 1) as f64 * self.constraints.window_gap)
                    / secondary_windows.len() as f64
            } else {
                workarea.width - 2.0 * self.constraints.screen_margin
            };

            for (i, window_id) in secondary_windows.iter().enumerate() {
                let x = workarea.x
                    + self.constraints.screen_margin
                    + i as f64 * (window_width + self.constraints.window_gap);
                result.insert(
                    window_id.clone(),
                    WindowBounds {
                        x,
                        y: workarea.y
                            + self.constraints.screen_margin
                            + primary_height
                            + self.constraints.window_gap,
                        width: window_width,
                        height: secondary_height,
                    },
                );
            }
        }

        Ok(result)
    }

    /// 计算布局效率（空间利用率）
    pub fn calculate_efficiency(&self, layout: &HashMap<String, WindowBounds>) -> f64 {
        if layout.is_empty() {
            return 0.0;
        }

        let total_window_area: f64 = layout
            .values()
            .map(|bounds| bounds.width * bounds.height)
            .sum();

        let workarea = &self.screen_info.workarea;
        let total_area = workarea.width * workarea.height;

        total_window_area / total_area
    }

    /// 计算布局平衡度
    pub fn calculate_balance_score(&self, layout: &HashMap<String, WindowBounds>) -> f64 {
        if layout.len() <= 1 {
            return 1.0;
        }

        let areas: Vec<f64> = layout
            .values()
            .map(|bounds| bounds.width * bounds.height)
            .collect();

        let mean_area = areas.iter().sum::<f64>() / areas.len() as f64;
        let variance = areas
            .iter()
            .map(|area| (area - mean_area).powi(2))
            .sum::<f64>()
            / areas.len() as f64;

        // 返回基于方差的平衡度分数（0-1，1表示完全平衡）
        1.0 / (1.0 + variance / mean_area.powi(2))
    }

    /// 计算布局动画的中间状态
    pub fn calculate_animation_frame(
        &self,
        from: &HashMap<String, WindowBounds>,
        to: &HashMap<String, WindowBounds>,
        progress: f64, // 0.0 到 1.0
    ) -> HashMap<String, WindowBounds> {
        let eased_progress = self.apply_easing(progress);
        let mut result = HashMap::new();

        for (window_id, to_bounds) in to {
            if let Some(from_bounds) = from.get(window_id) {
                // 插值计算
                let interpolated = WindowBounds {
                    x: from_bounds.x + (to_bounds.x - from_bounds.x) * eased_progress,
                    y: from_bounds.y + (to_bounds.y - from_bounds.y) * eased_progress,
                    width: from_bounds.width
                        + (to_bounds.width - from_bounds.width) * eased_progress,
                    height: from_bounds.height
                        + (to_bounds.height - from_bounds.height) * eased_progress,
                };
                result.insert(window_id.clone(), interpolated);
            } else {
                // 新窗口从中心淡入
                let workarea = &self.screen_info.workarea;
                let center_x = workarea.x + workarea.width / 2.0;
                let center_y = workarea.y + workarea.height / 2.0;

                let interpolated = WindowBounds {
                    x: center_x + (to_bounds.x - center_x) * eased_progress,
                    y: center_y + (to_bounds.y - center_y) * eased_progress,
                    width: to_bounds.width * eased_progress,
                    height: to_bounds.height * eased_progress,
                };
                result.insert(window_id.clone(), interpolated);
            }
        }

        result
    }

    /// 应用缓动函数
    fn apply_easing(&self, progress: f64) -> f64 {
        match self.animation.easing {
            EasingFunction::Linear => progress,
            EasingFunction::EaseInOut => {
                if progress < 0.5 {
                    2.0 * progress * progress
                } else {
                    1.0 - 2.0 * (1.0 - progress).powi(2)
                }
            }
            EasingFunction::EaseOut => 1.0 - (1.0 - progress).powi(2),
            EasingFunction::EaseIn => progress.powi(2),
            EasingFunction::Bounce => {
                if progress < 1.0 / 2.75 {
                    7.5625 * progress * progress
                } else if progress < 2.0 / 2.75 {
                    let p = progress - 1.5 / 2.75;
                    7.5625 * p * p + 0.75
                } else if progress < 2.5 / 2.75 {
                    let p = progress - 2.25 / 2.75;
                    7.5625 * p * p + 0.9375
                } else {
                    let p = progress - 2.625 / 2.75;
                    7.5625 * p * p + 0.984375
                }
            }
            EasingFunction::Elastic => {
                if progress == 0.0 || progress == 1.0 {
                    progress
                } else {
                    let p = progress - 1.0;
                    -(2.0_f64.powf(10.0 * p)
                        * ((p * 10.0 - 0.75) * 2.0 * std::f64::consts::PI / 3.0).sin())
                }
            }
        }
    }

    /// 获取性能统计
    pub fn get_performance_stats(&self) -> HashMap<String, LayoutStats> {
        self.performance_cache
            .iter()
            .map(|(k, (stats, _))| (k.clone(), stats.clone()))
            .collect()
    }

    /// 清理过期的性能缓存
    pub fn cleanup_performance_cache(&mut self) {
        let now = Instant::now();
        let expiry_duration = Duration::from_secs(300); // 5分钟过期

        self.performance_cache
            .retain(|_, (_, timestamp)| now.duration_since(*timestamp) < expiry_duration);
    }

    /// 更新屏幕信息
    pub fn update_screen_info(&mut self, screen_info: ScreenInfo) {
        self.screen_info = screen_info;
        // 清理缓存，因为屏幕尺寸可能改变
        self.performance_cache.clear();
    }

    /// 添加自定义布局模板
    pub fn add_template(&mut self, template: LayoutTemplate) {
        self.templates.insert(template.id.clone(), template);
    }

    /// 获取所有模板
    pub fn get_templates(&self) -> Vec<&LayoutTemplate> {
        self.templates.values().collect()
    }

    /// 获取最常用的模板
    pub fn get_popular_templates(&self, limit: usize) -> Vec<&LayoutTemplate> {
        let mut templates: Vec<&LayoutTemplate> = self.templates.values().collect();
        templates.sort_by(|a, b| b.usage_count.cmp(&a.usage_count));
        templates.into_iter().take(limit).collect()
    }
}
