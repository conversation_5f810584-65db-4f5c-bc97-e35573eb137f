//! 插件清单解析器模块
//!
//! 负责解析 TypeScript 插件定义并转换为 Rust 插件系统格式
//! 这是从 TypeScript 到 Rust 迁移的关键组件

use crate::plugin_system::{PluginAction, PluginMetadata, PluginMode, PluginSetting};
use anyhow::{Context, Result};
use regex::Regex;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use std::path::{Path, PathBuf};

/// TypeScript 插件清单结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TypeScriptPluginManifest {
    pub id: String,
    pub name: String,
    pub icon: String,
    pub mode: String,
    pub keyword: String,
    pub description: String,
    pub aliases: Vec<String>,
    pub actions: Vec<TypeScriptPluginAction>,
    pub settings: Vec<TypeScriptPluginSetting>,
    pub file_path: PathBuf,
}

/// TypeScript 插件动作结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TypeScriptPluginAction {
    pub id: String,
    pub name: String,
    pub pattern: String,
    pub description: String,
    pub is_interactive: Option<bool>,
}

/// TypeScript 插件设置结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TypeScriptPluginSetting {
    pub id: String,
    pub title: String,
    pub description: String,
    pub setting_type: String,
    pub default_value: serde_json::Value,
    pub options: Option<Vec<SettingOption>>,
}

/// 设置选项
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SettingOption {
    pub label: String,
    pub value: String,
}

/// 插件清单解析器
#[derive(Debug)]
pub struct PluginManifestParser {
    /// 插件目录路径
    plugins_dir: PathBuf,
    /// 缓存的插件清单
    cached_manifests: HashMap<String, TypeScriptPluginManifest>,
}

impl PluginManifestParser {
    /// 创建新的插件清单解析器
    pub fn new(plugins_dir: impl AsRef<Path>) -> Self {
        Self {
            plugins_dir: plugins_dir.as_ref().to_path_buf(),
            cached_manifests: HashMap::new(),
        }
    }

    /// 扫描并解析所有插件清单
    pub async fn scan_plugins(&mut self) -> Result<Vec<TypeScriptPluginManifest>> {
        let mut manifests = Vec::new();

        // 读取插件目录
        let entries = fs::read_dir(&self.plugins_dir).context("无法读取插件目录")?;

        for entry in entries {
            let entry = entry.context("读取目录项失败")?;
            let path = entry.path();

            // 只处理目录
            if path.is_dir() {
                let plugin_name = path
                    .file_name()
                    .and_then(|name| name.to_str())
                    .unwrap_or_default();

                // 解析插件清单
                if let Ok(manifest) = self.parse_plugin_manifest(&path).await {
                    log::info!("成功解析插件: {}", plugin_name);
                    self.cached_manifests
                        .insert(plugin_name.to_string(), manifest.clone());
                    manifests.push(manifest);
                } else {
                    log::warn!("跳过无效插件目录: {}", plugin_name);
                }
            }
        }

        Ok(manifests)
    }

    /// 解析单个插件清单
    pub async fn parse_plugin_manifest(
        &self,
        plugin_dir: &Path,
    ) -> Result<TypeScriptPluginManifest> {
        let index_path = plugin_dir.join("index.ts");

        // 检查 index.ts 是否存在
        if !index_path.exists() {
            return Err(anyhow::anyhow!("插件目录中未找到 index.ts 文件"));
        }

        // 读取 TypeScript 文件内容
        let content = fs::read_to_string(&index_path).context("无法读取插件文件")?;

        // 解析 TypeScript 插件定义
        self.parse_typescript_plugin(&content, index_path)
    }

    /// 解析 TypeScript 插件内容
    fn parse_typescript_plugin(
        &self,
        content: &str,
        file_path: PathBuf,
    ) -> Result<TypeScriptPluginManifest> {
        // 使用正则表达式提取插件定义
        let plugin_regex = Regex::new(
            r"export\s+const\s+(\w+):\s*(?:ListPlugin|ViewPlugin)\s*=\s*\{([\s\S]*?)\};",
        )?;

        if let Some(captures) = plugin_regex.captures(content) {
            let plugin_object = captures.get(2).unwrap().as_str();

            // 解析插件对象属性
            let manifest = self.parse_plugin_object(plugin_object, file_path)?;
            Ok(manifest)
        } else {
            Err(anyhow::anyhow!("无法找到插件导出定义"))
        }
    }

    /// 解析插件对象属性
    fn parse_plugin_object(
        &self,
        object_str: &str,
        file_path: PathBuf,
    ) -> Result<TypeScriptPluginManifest> {
        // 提取各个属性
        let id = self.extract_string_property(object_str, "id")?;
        let name = self.extract_string_property(object_str, "name")?;
        let icon = self.extract_string_property(object_str, "icon")?;
        let mode = self.extract_string_property(object_str, "mode")?;
        let keyword = self
            .extract_string_property(object_str, "keyword")
            .unwrap_or_default();
        let description = self.extract_string_property(object_str, "description")?;

        // 解析数组属性
        let aliases = self.extract_array_property(object_str, "aliases")?;
        let actions = self.extract_actions(object_str)?;
        let settings = self.extract_settings(object_str)?;

        Ok(TypeScriptPluginManifest {
            id,
            name,
            icon,
            mode,
            keyword,
            description,
            aliases,
            actions,
            settings,
            file_path,
        })
    }

    /// 提取字符串属性
    fn extract_string_property(&self, object_str: &str, property: &str) -> Result<String> {
        let regex = Regex::new(&format!(r#"{}:\s*['"`]([^'"`]+)['"`]"#, property))?;

        if let Some(captures) = regex.captures(object_str) {
            Ok(captures.get(1).unwrap().as_str().to_string())
        } else {
            Err(anyhow::anyhow!("无法找到属性: {}", property))
        }
    }

    /// 提取数组属性
    fn extract_array_property(&self, object_str: &str, property: &str) -> Result<Vec<String>> {
        let regex = Regex::new(&format!(r"{}:\s*\[(.*?)\]", property))?;

        if let Some(captures) = regex.captures(object_str) {
            let array_content = captures.get(1).unwrap().as_str();
            let item_regex = Regex::new(r#"['"`]([^'"`]+)['"`]"#)?;

            let items: Vec<String> = item_regex
                .captures_iter(array_content)
                .map(|cap| cap.get(1).unwrap().as_str().to_string())
                .collect();

            Ok(items)
        } else {
            Ok(Vec::new())
        }
    }

    /// 提取动作定义
    fn extract_actions(&self, object_str: &str) -> Result<Vec<TypeScriptPluginAction>> {
        let actions_regex = Regex::new(r"actions:\s*\[([\s\S]*?)\]")?;

        if let Some(captures) = actions_regex.captures(object_str) {
            let actions_content = captures.get(1).unwrap().as_str();
            let mut actions = Vec::new();

            // 解析每个动作对象，支持多行内容
            let action_regex = Regex::new(r"\{([^}]*(?:\}[^}]*)*?)\}")?;
            for action_match in action_regex.captures_iter(actions_content) {
                let action_str = action_match.get(1).unwrap().as_str();

                if let (Ok(id), Ok(name), Ok(pattern), Ok(description)) = (
                    self.extract_string_property(action_str, "id"),
                    self.extract_string_property(action_str, "name"),
                    self.extract_string_property(action_str, "pattern"),
                    self.extract_string_property(action_str, "description"),
                ) {
                    let is_interactive = self.extract_boolean_property(action_str, "isInteractive");

                    actions.push(TypeScriptPluginAction {
                        id,
                        name,
                        pattern,
                        description,
                        is_interactive,
                    });
                }
            }

            Ok(actions)
        } else {
            Ok(Vec::new())
        }
    }

    /// 提取设置定义
    fn extract_settings(&self, object_str: &str) -> Result<Vec<TypeScriptPluginSetting>> {
        let settings_regex = Regex::new(r"settings:\s*\[([\s\S]*?)\]")?;

        if let Some(captures) = settings_regex.captures(object_str) {
            let settings_content = captures.get(1).unwrap().as_str();
            let mut settings = Vec::new();

            // 解析每个设置对象，支持多行内容
            let setting_regex = Regex::new(r"\{([^}]*(?:\}[^}]*)*?)\}")?;
            for setting_match in setting_regex.captures_iter(settings_content) {
                let setting_str = setting_match.get(1).unwrap().as_str();

                if let (Ok(id), Ok(title), Ok(description), Ok(setting_type)) = (
                    self.extract_string_property(setting_str, "id"),
                    self.extract_string_property(setting_str, "title"),
                    self.extract_string_property(setting_str, "description"),
                    self.extract_string_property(setting_str, "type"),
                ) {
                    let default_value = self.extract_default_value(setting_str)?;
                    let options = self.extract_setting_options(setting_str)?;

                    settings.push(TypeScriptPluginSetting {
                        id,
                        title,
                        description,
                        setting_type,
                        default_value,
                        options,
                    });
                }
            }

            Ok(settings)
        } else {
            Ok(Vec::new())
        }
    }

    /// 提取布尔属性
    fn extract_boolean_property(&self, object_str: &str, property: &str) -> Option<bool> {
        let regex = Regex::new(&format!(r"{}:\s*(true|false)", property)).ok()?;

        if let Some(captures) = regex.captures(object_str) {
            match captures.get(1).unwrap().as_str() {
                "true" => Some(true),
                "false" => Some(false),
                _ => None,
            }
        } else {
            None
        }
    }

    /// 提取默认值
    fn extract_default_value(&self, setting_str: &str) -> Result<serde_json::Value> {
        let regex = Regex::new(r#"defaultValue:\s*['"`]?([^'"`\s,}]+)['"`]?"#)?;

        if let Some(captures) = regex.captures(setting_str) {
            let value_str = captures.get(1).unwrap().as_str();

            // 尝试解析为不同类型的值
            if value_str == "true" {
                Ok(serde_json::Value::Bool(true))
            } else if value_str == "false" {
                Ok(serde_json::Value::Bool(false))
            } else if let Ok(num) = value_str.parse::<i64>() {
                Ok(serde_json::Value::Number(num.into()))
            } else if let Ok(float) = value_str.parse::<f64>() {
                Ok(serde_json::Value::Number(
                    serde_json::Number::from_f64(float).unwrap(),
                ))
            } else {
                Ok(serde_json::Value::String(value_str.to_string()))
            }
        } else {
            Ok(serde_json::Value::Null)
        }
    }

    /// 提取设置选项
    fn extract_setting_options(&self, setting_str: &str) -> Result<Option<Vec<SettingOption>>> {
        let options_regex = Regex::new(r"options:\s*\[(.*?)\]")?;

        if let Some(captures) = options_regex.captures(setting_str) {
            let options_content = captures.get(1).unwrap().as_str();
            let mut options = Vec::new();

            let option_regex = Regex::new(r"\{([^}]+)\}")?;
            for option_match in option_regex.captures_iter(options_content) {
                let option_str = option_match.get(1).unwrap().as_str();

                let label = self.extract_string_property(option_str, "label")?;
                let value = self.extract_string_property(option_str, "value")?;

                options.push(SettingOption { label, value });
            }

            Ok(Some(options))
        } else {
            Ok(None)
        }
    }

    /// 将 TypeScript 插件清单转换为 Rust 插件元数据
    pub fn convert_to_rust_metadata(
        &self,
        ts_manifest: &TypeScriptPluginManifest,
    ) -> Result<PluginMetadata> {
        let mode = match ts_manifest.mode.as_str() {
            "list" => PluginMode::List,
            "view" => PluginMode::View,
            _ => return Err(anyhow::anyhow!("不支持的插件模式: {}", ts_manifest.mode)),
        };

        let actions: Vec<PluginAction> = ts_manifest
            .actions
            .iter()
            .map(|ts_action| PluginAction {
                id: ts_action.id.clone(),
                name: ts_action.name.clone(),
                pattern: ts_action.pattern.clone(),
                description: ts_action.description.clone(),
                is_interactive: ts_action.is_interactive.unwrap_or(false),
            })
            .collect();

        let settings: Vec<PluginSetting> = ts_manifest
            .settings
            .iter()
            .map(|ts_setting| PluginSetting {
                id: ts_setting.id.clone(),
                title: ts_setting.title.clone(),
                description: ts_setting.description.clone(),
                setting_type: ts_setting.setting_type.clone(),
                default_value: ts_setting.default_value.clone(),
                options: ts_setting
                    .options
                    .clone()
                    .map(|opts| opts.into_iter().map(|opt| (opt.label, opt.value)).collect()),
            })
            .collect();

        Ok(PluginMetadata {
            id: ts_manifest.id.clone(),
            name: ts_manifest.name.clone(),
            icon: ts_manifest.icon.clone(),
            mode,
            keyword: ts_manifest.keyword.clone(),
            description: ts_manifest.description.clone(),
            aliases: ts_manifest.aliases.clone(),
            actions,
            settings,
            enabled: true,
            version: "1.0.0".to_string(),
        })
    }

    /// 获取缓存的插件清单
    pub fn get_cached_manifest(&self, plugin_id: &str) -> Option<&TypeScriptPluginManifest> {
        self.cached_manifests.get(plugin_id)
    }

    /// 清除缓存
    pub fn clear_cache(&mut self) {
        self.cached_manifests.clear();
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::fs;
    use tempfile::TempDir;

    #[tokio::test]
    async fn test_parse_calculator_plugin() {
        let temp_dir = TempDir::new().unwrap();
        let plugin_dir = temp_dir.path().join("calculator");
        fs::create_dir_all(&plugin_dir).unwrap();

        let plugin_content = r#"
import { ListPlugin, ListPluginRunner } from '../../core/types';

export const calculatorPlugin: ListPlugin = {
  id: 'list-calculator', 
  name: 'Calculator', 
  icon: 'Calculator', 
  mode: 'list', 
  keyword: '', 
  description: 'Evaluate a mathematical expression.',
  aliases: ['calc'],
  actions: [
    { id: 'action-calculate', name: 'Calculate Expression', pattern: '^=(.*)', description: 'e.g. =(2+2)*4' }
  ],
  settings: [
    { id: 'precision', title: 'Result Precision', description: 'Number of decimal places for the result.', type: 'string', defaultValue: '2' }
  ],
  run: () => {},
};
"#;

        fs::write(plugin_dir.join("index.ts"), plugin_content).unwrap();

        let mut parser = PluginManifestParser::new(temp_dir.path());
        let manifest = parser.parse_plugin_manifest(&plugin_dir).await.unwrap();

        assert_eq!(manifest.id, "list-calculator");
        assert_eq!(manifest.name, "Calculator");
        assert_eq!(manifest.mode, "list");
        assert_eq!(manifest.aliases, vec!["calc"]);
        assert_eq!(manifest.actions.len(), 1);
        assert_eq!(manifest.settings.len(), 1);
    }
}
