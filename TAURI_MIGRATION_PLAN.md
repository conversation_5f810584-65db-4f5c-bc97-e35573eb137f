# NovaRay Tauri 2 迁移架构规划

## 执行概述

基于对 NovaRay 当前架构的全面分析，本文档提供了向 Tauri 2 迁移的详细计划。该迁移将保持现有的插件系统设计理念，同时充分利用 Tauri 2 的原生能力实现性能和用户体验的显著提升。

## 1. 当前架构分析

### 1.1 核心架构组件

#### 插件系统 (`src/core/`)
- **`types.ts`**: 定义了完整的插件类型系统，包括 `ListPlugin` 和 `ViewPlugin` 两种模式
- **`pluginRegistry.ts`**: 实现插件注册、搜索和动作执行的核心逻辑
- **`settingsManager.ts`**: 基于 localStorage 的设置管理系统

#### 插件模式分析
- **List 模式插件** (如 calculator, notes, clipboard):
  - 在 TypeScript 中实现核心逻辑
  - 返回结构化数据供主应用渲染
  - 支持直接动作和交互式操作
  - 通过 `run` 函数处理输入并返回 JSON 结果

- **View 模式插件** (如 shell, notepad, viewDemo):
  - 提供完整的 HTML/CSS/JS 界面
  - 通过 iframe 在主应用中加载
  - 使用 `postMessage` 进行双向通信
  - 具有完全的UI控制权

#### 命令总线与直接动作
- **模式匹配**: 通过正则表达式匹配用户输入到插件动作
- **直接动作**: 用户无需先激活插件即可执行特定功能
- **搜索机制**: 支持插件关键字、别名和动作的统一搜索

### 1.2 现有通信机制

```mermaid
graph TD
    A[用户输入] --> B[App.tsx]
    B --> C[pluginRegistry.search]
    C --> D[动作匹配]
    D --> E[ListPlugin.run]
    D --> F[ViewPlugin 加载]
    E --> G[JSON 结果处理]
    F --> H[iframe + postMessage]
    H --> I[插件HTML界面]
    I --> J[postMessage 回调]
    J --> B
```

## 2. Tauri 2 迁移技术挑战

### 2.1 核心挑战

#### 通信机制重构
- **现状**: 同步函数调用和 `postMessage`
- **目标**: Tauri 事件总线和 `invoke` 命令
- **复杂度**: 高 - 需要重新设计整个通信协议

#### 插件加载机制
- **现状**: iframe 加载本地 HTML 文件
- **目标**: 独立 Tauri 窗口 + `plugin://` 协议
- **复杂度**: 中 - 需要实现自定义协议处理

#### 设置系统迁移
- **现状**: localStorage 键值存储
- **目标**: Rust 后端持久化 + 文件系统
- **复杂度**: 低 - 相对简单的数据迁移

#### 性能优化重构
- **现状**: 前端 TypeScript 执行所有逻辑
- **目标**: 后端 Rust 处理计算密集型任务
- **复杂度**: 高 - 需要重写核心插件逻辑

### 2.2 架构依赖关系

```mermaid
graph TD
    A[Rust 后端基础] --> B[事件总线设计]
    A --> C[插件协议实现]
    B --> D[通信协议重构]
    C --> E[View 插件迁移]
    D --> F[List 插件迁移]
    E --> G[多窗口管理]
    F --> G
    G --> H[性能优化]
```

## 3. 迁移实施计划

### 第一阶段：基础设施搭建 (优先级: 最高)

#### 3.1 Rust 后端核心 (2-3 周)
- **任务**: 创建 Tauri 2 项目结构
- **输出**: 
  - 基础 Rust 后端框架
  - 基础事件总线实现
  - 基础窗口管理器
- **依赖**: 无
- **关键文件**: `src-tauri/src/main.rs`, `src-tauri/Cargo.toml`

#### 3.2 插件系统基础 (2 周)
- **任务**: 将现有类型系统迁移到 Rust
- **输出**:
  - Rust 插件特征 (Traits)
  - 插件注册表实现
  - 插件清单解析器
- **依赖**: 3.1
- **关键文件**: `src-tauri/src/plugins/mod.rs`

#### 3.3 设置系统迁移 (1 周)
- **任务**: 实现 Rust 后端设置管理
- **输出**:
  - 设置持久化存储
  - 设置 API 命令
  - 前端设置接口适配
- **依赖**: 3.1
- **关键文件**: `src-tauri/src/settings.rs`

### 第二阶段：通信协议重构 (优先级: 高)

#### 3.4 事件总线设计 (2 周)
- **任务**: 设计完整的事件通信协议
- **输出**:
  - 事件类型定义
  - 事件路由器实现
  - 前端事件适配器
- **依赖**: 3.1
- **关键文件**: `src-tauri/src/events.rs`

#### 3.5 命令处理系统 (2 周)
- **任务**: 重构命令匹配和执行逻辑
- **输出**:
  - Rust 命令处理器
  - 动作执行引擎
  - 前端命令接口
- **依赖**: 3.2, 3.4
- **关键文件**: `src-tauri/src/command_bus.rs`

### 第三阶段：插件迁移 (优先级: 中)

#### 3.6 List 插件迁移 (3-4 周)
- **任务**: 迁移现有 List 模式插件
- **输出**:
  - Calculator 插件 (Rust 实现)
  - Notes 插件 (Rust 实现)
  - Clipboard 插件 (Rust 实现)
- **依赖**: 3.5
- **关键文件**: `src-tauri/src/plugins/calculator.rs` 等

#### 3.7 自定义协议实现 (2 周)
- **任务**: 实现 `plugin://` 协议支持
- **输出**:
  - 自定义协议处理器
  - 插件资源加载器
  - 安全沙箱配置
- **依赖**: 3.2
- **关键文件**: `src-tauri/src/protocol.rs`

#### 3.8 View 插件迁移 (3-4 周)
- **任务**: 迁移现有 View 模式插件
- **输出**:
  - Shell 插件 (多窗口 + 事件总线)
  - Notepad 插件 (独立窗口)
  - ViewDemo 插件 (演示实现)
- **依赖**: 3.4, 3.7
- **关键文件**: `src-tauri/src/plugins/shell.rs` 等

### 第四阶段：高级功能 (优先级: 中)

#### 3.9 多窗口管理系统 (2-3 周)
- **任务**: 实现高级窗口管理功能
- **输出**:
  - 窗口生命周期管理
  - 窗口间状态同步
  - 窗口焦点管理
- **依赖**: 3.8
- **关键文件**: `src-tauri/src/window_manager.rs`

#### 3.10 性能优化 (2 周)
- **任务**: 实施性能优化策略
- **输出**:
  - 异步任务处理
  - 内存使用优化
  - 启动时间优化
- **依赖**: 3.6, 3.8
- **关键文件**: 优化现有实现

### 第五阶段：完善与测试 (优先级: 中)

#### 3.11 错误处理与日志 (1 周)
- **任务**: 实现完整的错误处理和日志系统
- **输出**:
  - 统一错误处理
  - 结构化日志
  - 错误报告机制
- **依赖**: 3.10
- **关键文件**: `src-tauri/src/error.rs`

#### 3.12 测试与文档 (2 周)
- **任务**: 完成测试覆盖和文档编写
- **输出**:
  - 单元测试套件
  - 集成测试
  - 开发者文档
- **依赖**: 3.11
- **关键文件**: `src-tauri/tests/` 目录

## 4. 技术实现细节

### 4.1 Rust 后端架构

```rust
// src-tauri/src/main.rs
use tauri::{Manager, Window};
use crate::plugins::PluginManager;
use crate::command_bus::CommandBus;

#[derive(Clone, serde::Serialize)]
struct SearchResult {
    plugin_id: String,
    action_id: Option<String>,
    title: String,
    description: String,
}

#[tauri::command]
async fn search_plugins(query: String, state: tauri::State<'_, AppState>) -> Result<Vec<SearchResult>, String> {
    state.command_bus.search(&query).await
}

#[tauri::command]
async fn execute_action(
    plugin_id: String,
    action_id: Option<String>,
    input: String,
    state: tauri::State<'_, AppState>
) -> Result<String, String> {
    state.plugin_manager.execute_action(&plugin_id, action_id.as_deref(), &input).await
}
```

### 4.2 事件总线设计

```mermaid
sequenceDiagram
    participant F as 前端
    participant R as Rust 后端
    participant P as 插件窗口
    
    F->>R: invoke('search_plugins', query)
    R->>R: 模式匹配 & 搜索
    R->>F: 返回搜索结果
    F->>R: invoke('execute_action', plugin_id, action_id, input)
    R->>R: 执行插件逻辑
    R->>P: emit('plugin:data', result)
    P->>R: emit('plugin:user_action', action)
    R->>F: emit('ui:update', state)
```

### 4.3 插件特征定义

```rust
// src-tauri/src/plugins/mod.rs
#[async_trait]
pub trait Plugin: Send + Sync {
    fn id(&self) -> &str;
    fn name(&self) -> &str;
    fn description(&self) -> &str;
    fn mode(&self) -> PluginMode;
    fn actions(&self) -> Vec<PluginAction>;
    fn settings(&self) -> Vec<PluginSetting>;
}

#[async_trait]
pub trait ListPlugin: Plugin {
    async fn run(&self, context: &ExecutionContext) -> Result<ListResult, PluginError>;
}

#[async_trait]
pub trait ViewPlugin: Plugin {
    async fn create_window(&self, app_handle: &AppHandle) -> Result<Window, PluginError>;
    async fn handle_event(&self, event: &PluginEvent) -> Result<(), PluginError>;
}
```

## 5. 风险评估与缓解策略

### 5.1 高风险项目

#### 通信协议重构
- **风险**: 复杂的异步通信可能导致竞态条件
- **缓解**: 实施严格的事件序列化和状态管理

#### 插件兼容性
- **风险**: 现有插件逻辑在 Rust 中的实现可能与 TypeScript 版本不一致
- **缓解**: 建立详细的测试用例对比两个版本的行为

#### 性能回归
- **风险**: 早期实现可能比现有 Web 版本性能更差
- **缓解**: 建立性能基准测试，持续监控关键指标

### 5.2 中风险项目

#### 多窗口管理
- **风险**: 窗口状态同步可能出现不一致
- **缓解**: 实施单一状态源和严格的状态更新协议

#### 自定义协议
- **风险**: 安全性和资源加载可能出现问题
- **缓解**: 严格的沙箱配置和资源验证

## 6. 成功指标

### 6.1 功能完整性
- [ ] 所有现有插件成功迁移
- [ ] 所有现有功能保持一致的行为
- [ ] 新增的原生功能正常工作

### 6.2 性能指标
- [ ] 应用启动时间 < 1 秒
- [ ] 搜索响应时间 < 100ms
- [ ] 内存使用 < 100MB
- [ ] 插件窗口创建时间 < 500ms

### 6.3 用户体验
- [ ] 键盘快捷键全部正常工作
- [ ] 所有现有工作流程保持不变
- [ ] 错误处理和用户反馈完善

## 7. 时间线总结

| 阶段 | 持续时间 | 累计时间 | 关键里程碑 |
|------|----------|----------|------------|
| 第一阶段 | 5-6 周 | 5-6 周 | 基础设施完成 |
| 第二阶段 | 4 周 | 9-10 周 | 通信协议就绪 |
| 第三阶段 | 8-10 周 | 17-20 周 | 插件迁移完成 |
| 第四阶段 | 4-5 周 | 21-25 周 | 高级功能就绪 |
| 第五阶段 | 3 周 | 24-28 周 | 产品发布就绪 |

**总预计时间**: 24-28 周 (约 6-7 个月)

## 8. 下一步行动

1. **立即开始**: 第一阶段的 Rust 后端核心搭建
2. **团队准备**: 确保团队具备 Rust 和 Tauri 开发能力
3. **工具准备**: 建立开发、测试和部署工具链
4. **持续集成**: 建立自动化测试和构建流程

---

*此文档将在迁移过程中持续更新，以反映实际进展和遇到的挑战。*