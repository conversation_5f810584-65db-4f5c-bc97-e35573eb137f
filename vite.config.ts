import path from 'path';
import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, '.', '');
  return {
    plugins: [react()],
    define: {
      'process.env.API_KEY': JSON.stringify(env.GEMINI_API_KEY),
      'process.env.GEMINI_API_KEY': JSON.stringify(env.GEMINI_API_KEY)
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, '.'),
      }
    },
    // 用于开发环境的配置
    clearScreen: false,
    server: {
      port: 5173,
      strictPort: true,
      watch: {
        // 用于在 Windows 上的更好的性能
        ignored: ['**/src-tauri/**']
      }
    }
  };
});
