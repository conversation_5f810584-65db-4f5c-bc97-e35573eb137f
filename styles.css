/* --- THEME VARIABLES --- */
:root {
    /* Light Theme */
    --background-color: #f7f7f8;
    --surface-color: #ffffff;
    --primary-text-color: #202124;
    --secondary-text-color: #5f6368;
    --accent-color: #1a73e8;
    --border-color: #dadce0;
    --hover-color: #f1f3f4;
    --button-text-color: #3c4043;
    --button-active-bg: #e8f0fe;
    --button-active-text: #1967d2;
    --icon-button-color: #5f6368;
    --icon-button-hover-bg: #f0f0f0;
    --error-color: #d93025;
    --error-bg-color: #fce8e6;
    --success-color: #1e8e3e;
    --highlight-bg-color: #e8f0fe;
    --response-bg-color: #e8f0fe;
    --scrollbar-thumb-color: #c1c1c1;
    --scrollbar-track-color: transparent;

    --badge-agent-bg: #e6f7ff;
    --badge-agent-text: #1890ff;
    --badge-agent-border: #91d5ff;
    --badge-view-bg: #f3e8fd;
    --badge-view-text: #7e57c2;
    --badge-view-border: #d1c4e9;
    --badge-installed-bg: #e6f4ea;
    --badge-installed-text: #1e8e3e;
    --badge-installed-border: #b7e1c1;
}

html.dark {
    /* Dark Theme */
    --background-color: #202124;
    --surface-color: #292a2d;
    --primary-text-color: #e8eaed;
    --secondary-text-color: #9aa0a6;
    --accent-color: #8ab4f8;
    --border-color: #3c4043;
    --hover-color: #3c4043;
    --button-text-color: #e8eaed;
    --button-active-bg: #3c4043;
    --button-active-text: #8ab4f8;
    --icon-button-color: #9aa0a6;
    --icon-button-hover-bg: #3c4043;
    --error-color: #f28b82;
    --error-bg-color: #3c2b2a;
    --success-color: #81c995;
    --highlight-bg-color: #373c46;
    --response-bg-color: #373c46;
    --scrollbar-thumb-color: #5f6368;
    --scrollbar-track-color: transparent;
    
    --badge-agent-bg: #2a3a4c;
    --badge-agent-text: #8ab4f8;
    --badge-agent-border: #3c5e85;
    --badge-view-bg: #393144;
    --badge-view-text: #c5a6ff;
    --badge-view-border: #5e4a7d;
    --badge-installed-bg: #2a4131;
    --badge-installed-text: #81c995;
    --badge-installed-border: #3d6a4a;
}

/* --- GLOBAL & ROOT STYLES --- */
body {
    font-family: 'Google Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    margin: 0;
    background-color: var(--background-color);
    color: var(--primary-text-color);
    display: flex;
    justify-content: center;
    align-items: flex-start; /* Align to top to see full container with results */
    min-height: 100vh;
    padding: 20px;
    box-sizing: border-box;
    transition: background-color 0.3s ease, color 0.3s ease;
}

#root {
    width: 100%;
    max-width: 760px; 
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    transition: max-width 0.3s ease-in-out;
}

#root.list-view-active {
    max-width: 880px;
}
#root.settings-active {
    max-width: 880px;
}


/* Custom Scrollbars */
*::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}
*::-webkit-scrollbar-track {
    background: var(--scrollbar-track-color);
}
*::-webkit-scrollbar-thumb {
    background-color: var(--scrollbar-thumb-color);
    border-radius: 10px;
    border: 2px solid transparent;
    background-clip: content-box;
}

/* --- COMPONENT STYLES --- */

/* App.tsx */
.novaray-container {
    background-color: var(--surface-color);
    border-radius: 28px;
    border: 1px solid var(--border-color);
    box-shadow: 0 1px 6px rgba(0,0,0,.28);
    width: 100%;
    display: flex;
    flex-direction: column;
    margin-top: 50px;
    transition: background-color 0.3s ease, border-color 0.3s ease;
    /* overflow: hidden;  <-- This was the problem, removing it for now. Portals are a better fix. */
}

.output-area {
    order: 1; /* Results on top */
    min-height: 0; /* Prevents flexbox overflow issues */
    padding: 12px 12px 0 12px;
}
.output-area:empty {
    display: none;
}
.input-container {
    order: 2; /* Input at bottom */
    padding: 16px 20px;
    display: flex;
    flex-direction: column;
    gap: 12px;
}
.input-container.has-output {
    border-top: 1px solid var(--border-color);
    transition: border-color 0.3s ease;
}

/* FloatingStatusDisplay.tsx */
.floating-status {
    position: fixed;
    top: 15px;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--surface-color);
    color: var(--primary-text-color);
    padding: 8px 12px;
    border-radius: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    display: flex;
    align-items: center;
    gap: 8px;
    z-index: 1000;
    border: 1px solid var(--border-color);
    font-size: 14px;
    max-width: calc(100vw - 40px);
    box-sizing: border-box;
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

.floating-status-icon {
    display: flex;
    align-items: center;
}
.floating-status-icon svg {
    width: 18px;
    height: 18px;
}

.floating-status-text {
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex-shrink: 1;
    min-width: 0;
}

.floating-status-clear {
    background: none;
    border: none;
    color: var(--secondary-text-color);
    font-size: 20px;
    line-height: 1;
    padding: 0 4px;
    cursor: pointer;
    margin-left: 4px;
    transition: color 0.2s ease;
}
.floating-status-clear:hover {
    color: var(--primary-text-color);
}

/* ChatInputArea.tsx */
.chat-input-area {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    padding: 4px;
}

.chat-textarea {
    flex-grow: 1;
    padding: 10px 12px;
    font-size: 15px;
    line-height: 1.5;
    border: none;
    background-color: transparent;
    color: var(--primary-text-color);
    outline: none;
    resize: none;
    font-family: inherit;
    min-height: 24px;
    max-height: 150px;
    overflow-y: auto;
}

.chat-textarea::placeholder {
    color: var(--secondary-text-color);
    opacity: 0.9;
}

.chat-icon-button {
    background-color: transparent;
    border: none;
    color: var(--icon-button-color);
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    transition: background-color 0.2s ease, color 0.2s ease;
}
.chat-icon-button svg {
    width: 20px;
    height: 20px;
}
.chat-icon-button:hover:not(:disabled) {
    background-color: var(--icon-button-hover-bg);
}
.chat-icon-button:disabled {
    opacity: 0.5;
    cursor: default;
}

.send-button {
     margin-top: 1px;
     color: var(--accent-color);
}
.send-button:disabled {
    color: var(--icon-button-color);
    opacity: 0.5;
}

/* AIResponseDisplay.tsx */
.ai-response-display {
    font-size: 14px;
    line-height: 1.6;
    word-wrap: break-word;
    margin: 0;
    padding: 12px;
    border-radius: 8px;
}

.ai-response-display.loading {
    color: var(--secondary-text-color);
}
.ai-response-display.error {
    color: var(--error-color);
    background-color: var(--error-bg-color);
    border: 1px solid var(--error-color);
}
.ai-response-display.response {
    background-color: var(--response-bg-color);
    color: var(--primary-text-color);
}

.ai-response-display img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    margin-top: 8px;
}

/* ResultsList.tsx */
.results-container {
    max-height: 350px;
    overflow-y: auto;
}

.result-group + .result-group {
    margin-top: 8px;
}

.result-group-header {
    text-transform: uppercase;
    font-size: 11px;
    font-weight: 600;
    color: var(--secondary-text-color);
    padding: 4px 12px;
    letter-spacing: 0.5px;
}

.results-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

/* ResultItem.tsx */
.result-item {
    display: flex;
    align-items: center;
    padding: 10px 12px;
    cursor: pointer;
    border-radius: 8px;
    margin: 2px 4px;
    transition: background-color 0.2s ease;
}
.result-item:hover, 
.result-item.selected {
    background-color: var(--highlight-bg-color);
}

.result-item-content {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    overflow: hidden;
    gap: 2px;
}

.result-item-top-line {
    display: flex;
    align-items: center;
    gap: 8px;
}

.result-item-icon {
    margin-right: 12px;
    color: var(--secondary-text-color);
    width: 24px;
    height: 24px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    transition: color 0.2s ease;
}
.result-item-icon svg {
    width: 20px;
    height: 20px;
}
.plugin-icon-image {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    object-fit: contain;
}
 .result-item.selected .result-item-icon,
 .result-item.selected .result-item-icon svg {
    color: var(--accent-color);
}

.result-item-text {
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.result-item-name {
    font-weight: 500;
    color: var(--primary-text-color);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: color 0.2s ease;
}
.result-item.selected .result-item-name {
     color: var(--accent-color);
}

.result-item-description {
    font-size: 12px;
    color: var(--secondary-text-color);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.result-item-badge {
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 4px;
    border: 1px solid;
    margin-left: auto;
    flex-shrink: 0;
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

.result-item-badge.agent {
    background-color: var(--badge-agent-bg);
    color: var(--badge-agent-text);
    border-color: var(--badge-agent-border);
}

.result-item-badge.view {
    background-color: var(--badge-view-bg);
    color: var(--badge-view-text);
    border-color: var(--badge-view-border);
}
.result-item-badge.installed {
    background-color: var(--badge-installed-bg);
    color: var(--badge-installed-text);
    border-color: var(--badge-installed-border);
}


/* AttachmentPreview.tsx */
.attachment-preview {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 12px;
    background-color: var(--hover-color);
    border-radius: 8px;
    border: 1px solid var(--border-color);
    font-size: 13px;
    transition: background-color 0.3s ease, border-color 0.3s ease;
}
.attachment-icon {
    color: var(--secondary-text-color);
    flex-shrink: 0;
}
.attachment-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
    overflow: hidden;
}
.attachment-name {
    color: var(--primary-text-color);
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.attachment-size {
    color: var(--secondary-text-color);
    font-size: 11px;
}
.remove-attachment-button {
    background-color: transparent;
    border: none;
    color: var(--secondary-text-color);
    padding: 4px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: auto;
    transition: background-color 0.2s ease, color 0.2s ease;
}
.remove-attachment-button:hover {
    background-color: var(--border-color);
    color: var(--primary-text-color);
}

/* ListView.tsx */
.list-view {
    font-size: 14px;
    line-height: 1.6;
    word-wrap: break-word;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
}

.list-view-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.list-view-item {
    display: flex;
    align-items: center;
    padding: 10px 12px;
    cursor: pointer;
    border-radius: 8px;
    margin: 2px 4px;
    transition: background-color 0.2s ease;
    position: relative; /* For badge positioning */
}
.list-view-item:hover, .list-view-item.selected {
    background-color: var(--highlight-bg-color);
}
.list-view-item-installed-badge {
    position: absolute;
    top: 6px;
    right: 8px;
    font-size: 9px;
    font-weight: 600;
    padding: 2px 5px;
    border-radius: 4px;
    background-color: var(--badge-installed-bg);
    color: var(--badge-installed-text);
    border: 1px solid var(--badge-installed-border);
}


.list-view-item-icon {
    margin-right: 12px;
    color: var(--secondary-text-color);
    width: 24px;
    height: 24px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    transition: color 0.2s ease;
}
.list-view-item-icon svg,
.list-view-item-icon .plugin-icon-image {
    width: 20px;
    height: 20px;
}

.list-view-item.selected .list-view-item-icon,
.list-view-item.selected .list-view-item-icon svg {
    color: var(--accent-color);
}

.list-view-item-info {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    flex-grow: 1;
}

.list-view-item-title {
    font-weight: 500;
    color: var(--primary-text-color);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: color 0.2s ease;
}
.list-view-item.selected .list-view-item-title {
     color: var(--accent-color);
}

.list-view-item-description {
    font-size: 12px;
    color: var(--secondary-text-color);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.list-view.list-view-container {
    display: flex;
    flex-direction: row; /* Side-by-side for dual column */
    height: 350px; /* Fixed height for dual column view */
}

.list-view-pane {
    width: 45%;
    flex-shrink: 0;
    overflow-y: auto;
    border-right: 1px solid var(--border-color);
    transition: border-color 0.3s ease;
    padding-right: 4px;
}

.item-preview-pane {
    width: 55%;
    padding: 12px 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.list-view:not(.list-view-container) .list-view-list {
    max-height: 350px;
    overflow-y: auto;
}

/* ItemPreview.tsx */
.item-preview-header {
    padding-bottom: 8px;
    margin-bottom: 8px;
    border-bottom: 1px solid var(--border-color);
    transition: border-color 0.3s ease;
    flex-shrink: 0;
}

.item-preview-title {
    font-size: 16px;
    font-weight: 500;
    color: var(--primary-text-color);
    display: flex;
    align-items: center;
    gap: 8px;
}
.plugin-preview-meta {
    font-size: 12px;
    color: var(--secondary-text-color);
    margin-top: 4px;
}

.item-preview-content {
    font-size: 13px;
    line-height: 1.6;
    color: var(--secondary-text-color);
    white-space: pre-wrap;
    word-wrap: break-word;
    flex-grow: 1;
}
.item-preview-content.markdown {
    background-color: var(--hover-color);
    padding: 8px 12px;
    border-radius: 6px;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
}

.screenshot-gallery {
    margin-top: 12px;
    border-top: 1px solid var(--border-color);
    padding-top: 12px;
}
.screenshot-gallery-title {
    font-size: 11px;
    font-weight: 600;
    color: var(--secondary-text-color);
    text-transform: uppercase;
    margin-bottom: 8px;
    letter-spacing: 0.5px;
}
.screenshot-gallery-images {
    display: flex;
    overflow-x: auto;
    gap: 10px;
    padding: 4px 0;
}

.screenshot-gallery-images img {
    height: 120px;
    border-radius: 6px;
    object-fit: contain;
    border: 1px solid var(--border-color);
    background-color: var(--hover-color);
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.screenshot-gallery-images img:hover {
    transform: scale(1.03);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.item-preview-action-hint {
    margin-top: auto;
    padding-top: 12px;
    font-size: 12px;
    color: var(--secondary-text-color);
    font-style: italic;
    text-align: right;
    flex-shrink: 0;
}

/* CustomViewDisplay.tsx */
.custom-view-container {
    height: 500px;
}

.custom-view-iframe {
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 8px;
}

/* Breadcrumbs.tsx */
.breadcrumbs {
    display: flex;
    align-items: center;
    padding: 0px 4px 8px 4px;
    font-size: 13px;
    color: var(--secondary-text-color);
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 8px;
}
.breadcrumb-item {
    cursor: pointer;
    color: var(--secondary-text-color);
    text-decoration: none;
    padding: 4px 6px;
    border-radius: 6px;
    transition: background-color 0.2s ease, color 0.2s ease;
}
.breadcrumb-item:hover {
    background-color: var(--hover-color);
    color: var(--primary-text-color);
}
.breadcrumb-separator {
    margin: 0 4px;
    color: var(--border-color);
}
.breadcrumb-current {
    color: var(--primary-text-color);
    font-weight: 500;
    padding: 4px 6px;
}

/* --- Settings Components --- */
.settings-view {
    display: flex;
    height: 500px;
}
.settings-sidebar {
    width: 220px;
    flex-shrink: 0;
    padding: 12px;
    border-right: 1px solid var(--border-color);
    overflow-y: auto;
}
.settings-nav-header {
    text-transform: uppercase;
    font-size: 11px;
    font-weight: 600;
    color: var(--secondary-text-color);
    padding: 8px 12px;
    letter-spacing: 0.5px;
}
.settings-nav-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: var(--primary-text-color);
    transition: background-color 0.2s ease;
    outline: none; /* For keyboard nav */
}
.settings-nav-item:hover {
    background-color: var(--hover-color);
}
.settings-nav-item.active,
.settings-nav-item:focus {
    background-color: var(--button-active-bg);
    color: var(--button-active-text);
}
.settings-nav-item svg {
    width: 18px;
    height: 18px;
}
.settings-content {
    flex-grow: 1;
    padding: 24px;
    overflow-y: auto;
}
.settings-content:focus {
    outline: none;
}

.plugin-settings-form-header {
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 12px;
    margin-bottom: 20px;
}
.plugin-settings-form-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-text-color);
    margin: 0;
}
.plugin-settings-form-description {
    font-size: 14px;
    color: var(--secondary-text-color);
    margin-top: 4px;
}

.setting-item {
    display: flex;
    flex-direction: column;
    gap: 6px;
}
.setting-item + .setting-item {
    margin-top: 24px;
}
.setting-item-label-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.setting-item-label {
    font-size: 14px;
    font-weight: 500;
    color: var(--primary-text-color);
}
.setting-item-description {
    font-size: 12px;
    color: var(--secondary-text-color);
    margin-top: 4px;
}
.setting-item-control {
    width: 100%;
    padding: 8px 12px;
    font-size: 14px;
    border-radius: 6px;
    border: 1px solid var(--border-color);
    background-color: var(--surface-color);
    color: var(--primary-text-color);
    box-sizing: border-box;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}
.setting-item-control:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px var(--button-active-bg);
}

select.setting-item-control {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%235f6368'%3e%3cpath d='M8 11L2 5h12z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.5rem center;
    background-size: 16px;
    padding-right: 2rem;
}

html.dark select.setting-item-control {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%239aa0a6'%3e%3cpath d='M8 11L2 5h12z'/%3e%3c/svg%3e");
}


/* ToggleSwitch.tsx */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 22px;
}
.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}
.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--border-color);
    transition: .4s;
    border-radius: 22px;
}
.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}
input:checked + .toggle-slider {
    background-color: var(--accent-color);
}
input:focus-visible + .toggle-slider {
    box-shadow: 0 0 0 2px var(--button-active-bg);
}
input:checked + .toggle-slider:before {
    transform: translateX(18px);
}
