/**
 * 事件总线演示组件
 * 展示前端与后端事件通信的完整功能
 */

import React, { useState, useEffect } from 'react';
import { eventBus, useEventSubscription, useEventEmitter } from '../core/eventBus';

interface EventLog {
    id: string;
    timestamp: number;
    type: string;
    payload: any;
    source: string;
}

export const EventBusDemo: React.FC = () => {
    const [eventHistory, setEventHistory] = useState<EventLog[]>([]);
    const [inputValue, setInputValue] = useState('');
    const [searchResults, setSearchResults] = useState<string[]>([]);
    const [currentTheme, setCurrentTheme] = useState('light');
    const [customEventName, setCustomEventName] = useState('');
    const [customEventData, setCustomEventData] = useState('');
    const [isLoading, setIsLoading] = useState(false);

    const emitter = useEventEmitter();

    // 订阅自定义事件
    useEventSubscription('custom-event', (eventData: any) => {
        console.log('收到自定义事件:', eventData);
        addEventToHistory('custom-event', eventData, 'backend');
    });

    // 订阅UI事件
    useEventSubscription('ui-event', (eventData: any) => {
        console.log('收到UI事件:', eventData);
        addEventToHistory('ui-event', eventData, 'backend');
    });

    // 订阅插件事件
    useEventSubscription('plugin-event', (eventData: any) => {
        console.log('收到插件事件:', eventData);
        addEventToHistory('plugin-event', eventData, 'backend');
    });

    // 添加事件到历史记录
    const addEventToHistory = (type: string, payload: any, source: string) => {
        const newEvent: EventLog = {
            id: Date.now().toString(),
            timestamp: Date.now(),
            type,
            payload,
            source
        };
        setEventHistory(prev => [newEvent, ...prev].slice(0, 20)); // 只保留最近20条
    };

    // 加载事件历史记录
    const loadEventHistory = async () => {
        setIsLoading(true);
        try {
            const history = await eventBus.getEventHistory(10);
            console.log('加载事件历史:', history);
            // 转换为UI格式
            const formattedHistory = history.map(event => ({
                id: event.id,
                timestamp: event.timestamp,
                type: JSON.stringify(event.event_type),
                payload: event.event_type,
                source: event.source
            }));
            setEventHistory(formattedHistory);
        } catch (error) {
            console.error('加载事件历史失败:', error);
        } finally {
            setIsLoading(false);
        }
    };

    // 清空事件历史
    const clearEventHistory = async () => {
        try {
            await eventBus.clearEventHistory();
            setEventHistory([]);
            console.log('事件历史已清空');
        } catch (error) {
            console.error('清空事件历史失败:', error);
        }
    };

    // 处理输入变化
    const handleInputChange = async (value: string) => {
        setInputValue(value);
        await emitter.onInputChanged(value);
        addEventToHistory('input-changed', { value }, 'frontend');
    };

    // 处理搜索结果变化
    const handleSearchResultsChange = async () => {
        const results = inputValue.split(' ').filter(Boolean);
        setSearchResults(results);
        await emitter.onSearchResultsChanged(results);
        addEventToHistory('search-results-changed', { results }, 'frontend');
    };

    // 处理主题变化
    const handleThemeChange = async (theme: string) => {
        setCurrentTheme(theme);
        await emitter.onThemeChanged(theme);
        addEventToHistory('theme-changed', { theme }, 'frontend');
    };

    // 发送自定义事件
    const sendCustomEvent = async () => {
        if (!customEventName.trim()) return;

        try {
            let payload = {};
            if (customEventData.trim()) {
                payload = JSON.parse(customEventData);
            }

            const success = await emitter.emitEvent(customEventName, payload);
            if (success) {
                addEventToHistory('custom-event-sent', { name: customEventName, payload }, 'frontend');
                setCustomEventName('');
                setCustomEventData('');
            }
        } catch (error) {
            console.error('发送自定义事件失败:', error);
        }
    };

    // 模拟插件执行完成事件
    const simulatePluginExecution = async () => {
        await emitter.onPluginExecutionComplete(
            'demo-plugin',
            'search-action',
            '搜索结果: 找到 5 个匹配项',
            true
        );
        addEventToHistory('plugin-execution-complete', {
            plugin_id: 'demo-plugin',
            action_id: 'search-action',
            success: true
        }, 'frontend');
    };

    // 组件挂载时加载事件历史
    useEffect(() => {
        loadEventHistory();
    }, []);

    return (
        <div className="event-bus-demo">
            <h2>事件总线系统演示</h2>

            {/* 基本事件操作 */}
            <div className="demo-section">
                <h3>基本事件操作</h3>

                <div className="event-controls">
                    <div className="control-group">
                        <label>输入事件:</label>
                        <input
                            type="text"
                            value={inputValue}
                            onChange={(e) => handleInputChange(e.target.value)}
                            placeholder="输入文本触发事件"
                        />
                    </div>

                    <div className="control-group">
                        <label>搜索结果:</label>
                        <button onClick={handleSearchResultsChange}>
                            更新搜索结果 ({searchResults.length} 项)
                        </button>
                    </div>

                    <div className="control-group">
                        <label>主题切换:</label>
                        <select
                            value={currentTheme}
                            onChange={(e) => handleThemeChange(e.target.value)}
                        >
                            <option value="light">浅色主题</option>
                            <option value="dark">深色主题</option>
                            <option value="system">系统主题</option>
                        </select>
                    </div>
                </div>
            </div>

            {/* 自定义事件 */}
            <div className="demo-section">
                <h3>自定义事件</h3>

                <div className="custom-event-controls">
                    <div className="control-group">
                        <label>事件名称:</label>
                        <input
                            type="text"
                            value={customEventName}
                            onChange={(e) => setCustomEventName(e.target.value)}
                            placeholder="输入事件名称"
                        />
                    </div>

                    <div className="control-group">
                        <label>事件数据 (JSON):</label>
                        <textarea
                            value={customEventData}
                            onChange={(e) => setCustomEventData(e.target.value)}
                            placeholder='{"key": "value"}'
                            rows={3}
                        />
                    </div>

                    <button onClick={sendCustomEvent} disabled={!customEventName.trim()}>
                        发送自定义事件
                    </button>
                </div>
            </div>

            {/* 插件事件 */}
            <div className="demo-section">
                <h3>插件事件</h3>

                <div className="plugin-controls">
                    <button onClick={simulatePluginExecution}>
                        模拟插件执行完成
                    </button>
                </div>
            </div>

            {/* 事件历史 */}
            <div className="demo-section">
                <h3>事件历史</h3>

                <div className="history-controls">
                    <button onClick={loadEventHistory} disabled={isLoading}>
                        {isLoading ? '加载中...' : '刷新历史'}
                    </button>
                    <button onClick={clearEventHistory}>
                        清空历史
                    </button>
                </div>

                <div className="event-history">
                    {eventHistory.length === 0 ? (
                        <p>暂无事件历史记录</p>
                    ) : (
                        eventHistory.map((event) => (
                            <div key={event.id} className="event-item">
                                <div className="event-header">
                                    <span className="event-type">{event.type}</span>
                                    <span className="event-source">{event.source}</span>
                                    <span className="event-time">
                                        {new Date(event.timestamp).toLocaleTimeString()}
                                    </span>
                                </div>
                                <div className="event-payload">
                                    <pre>{JSON.stringify(event.payload, null, 2)}</pre>
                                </div>
                            </div>
                        ))
                    )}
                </div>
            </div>

            <style dangerouslySetInnerHTML={{
                __html: `
                .event-bus-demo {
                  padding: 20px;
                  max-width: 1200px;
                  margin: 0 auto;
                }
                
                .demo-section {
                  margin-bottom: 30px;
                  padding: 20px;
                  border: 1px solid #e0e0e0;
                  border-radius: 8px;
                }
                
                .demo-section h3 {
                  margin-top: 0;
                  color: #333;
                }
                
                .event-controls,
                .custom-event-controls,
                .plugin-controls,
                .history-controls {
                  display: flex;
                  flex-wrap: wrap;
                  gap: 15px;
                  align-items: center;
                }
                
                .control-group {
                  display: flex;
                  flex-direction: column;
                  gap: 5px;
                }
                
                .control-group label {
                  font-weight: 600;
                  color: #555;
                  font-size: 14px;
                }
                
                .control-group input,
                .control-group select,
                .control-group textarea {
                  padding: 8px;
                  border: 1px solid #ddd;
                  border-radius: 4px;
                  font-size: 14px;
                }
                
                .control-group textarea {
                  resize: vertical;
                  font-family: monospace;
                }
                
                .event-bus-demo button {
                  padding: 8px 16px;
                  background: #007bff;
                  color: white;
                  border: none;
                  border-radius: 4px;
                  cursor: pointer;
                  font-size: 14px;
                }
                
                .event-bus-demo button:hover {
                  background: #0056b3;
                }
                
                .event-bus-demo button:disabled {
                  background: #ccc;
                  cursor: not-allowed;
                }
                
                .event-history {
                  max-height: 400px;
                  overflow-y: auto;
                  margin-top: 15px;
                }
                
                .event-item {
                  background: #f8f9fa;
                  border: 1px solid #e9ecef;
                  border-radius: 4px;
                  padding: 12px;
                  margin-bottom: 8px;
                }
                
                .event-header {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  margin-bottom: 8px;
                }
                
                .event-type {
                  font-weight: 600;
                  color: #007bff;
                }
                
                .event-source {
                  background: #e9ecef;
                  padding: 2px 8px;
                  border-radius: 12px;
                  font-size: 12px;
                  color: #6c757d;
                }
                
                .event-time {
                  font-size: 12px;
                  color: #6c757d;
                }
                
                .event-payload {
                  background: #fff;
                  border: 1px solid #e9ecef;
                  border-radius: 4px;
                  padding: 8px;
                  overflow-x: auto;
                }
                
                .event-payload pre {
                  margin: 0;
                  font-size: 12px;
                  color: #495057;
                }
              `
            }} />
        </div>
    );
};