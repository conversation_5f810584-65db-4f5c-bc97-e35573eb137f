import React, { useEffect } from 'react';
import { Send } from 'lucide-react';

interface ChatInputAreaProps {
  inputValue: string;
  onInputChange: (value: string) => void;
  onSubmit: () => void;
  placeholder: string;
  textareaRef: React.RefObject<HTMLTextAreaElement>;
}

const ChatInputArea: React.FC<ChatInputAreaProps> = ({ 
  inputValue, 
  onInputChange, 
  onSubmit, 
  placeholder, 
  textareaRef 
}) => {

  const handleKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      if (inputValue.trim()) {
        onSubmit();
      }
    }
  };

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [inputValue, textareaRef]);

  return (
    <div className="chat-input-area">
      <textarea
        ref={textareaRef}
        value={inputValue}
        onChange={(e) => onInputChange(e.target.value)}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        className="chat-textarea"
        aria-label="Chat input"
        rows={1}
      />
      <button className="chat-icon-button send-button" onClick={onSubmit} aria-label="Send message" disabled={!inputValue.trim()}>
        <Send size={20} />
      </button>
    </div>
  );
};

export default ChatInputArea;
