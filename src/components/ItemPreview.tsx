import React from 'react';
import { ListItem } from '../core/types';
import Icon from './Icon';

interface ItemPreviewProps {
  item: ListItem | null;
}

const ItemPreview: React.FC<ItemPreviewProps> = ({ item }) => {
  if (!item) {
    return null; // Or a placeholder
  }

  const getActionHint = () => {
    if (!item.onSelectAction) return null;
    switch (item.onSelectAction.type) {
      case 'copy':
        return `Press Enter to copy`;
      case 'open-url':
        return 'Press Enter to open URL';
      case 'show-list':
        return 'Press Enter to see actions';
      case 'install-plugin':
          return 'Press Enter to install plugin';
      case 'uninstall-plugin':
          return 'Press Enter to uninstall plugin';
      default:
        return 'Press Enter to select';
    }
  };
  
  const isPluginReadme = item.preview?.type === 'markdown';

  return (
    <>
      <div className="item-preview-header">
        <h3 className="item-preview-title">
          <Icon name={item.icon} size={18} />
          <span>{item.title}</span>
        </h3>
        {isPluginReadme && (
             <div className="plugin-preview-meta">
                {item.preview?.version && `Version: ${item.preview.version}`}
                {item.preview?.author && ` by ${item.preview.author}`}
             </div>
        )}
      </div>

      <div className={`item-preview-content ${isPluginReadme ? 'markdown' : ''}`}>
        <pre>{item.preview?.content || item.description || 'No preview available.'}</pre>
      </div>

      {item.preview?.screenshots && item.preview.screenshots.length > 0 && (
        <div className="screenshot-gallery">
            <h4 className="screenshot-gallery-title">Screenshots</h4>
            <div className="screenshot-gallery-images">
                {item.preview.screenshots.map((src, index) => (
                    <img key={index} src={src} alt={`Screenshot ${index + 1}`} />
                ))}
            </div>
        </div>
      )}

      <div className="item-preview-action-hint">
        {getActionHint()}
      </div>
    </>
  );
};

export default ItemPreview;