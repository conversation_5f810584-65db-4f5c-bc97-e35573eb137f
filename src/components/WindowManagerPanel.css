/* 窗口管理面板样式 */
.window-manager-panel {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: #333;
}

/* 面板头部 */
.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e0e0e0;
}

.panel-header h2 {
    margin: 0;
    color: #2c3e50;
    font-size: 28px;
    font-weight: 600;
}

.refresh-buttons {
    display: flex;
    gap: 10px;
}

.refresh-btn {
    padding: 8px 16px;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
}

.refresh-btn:hover {
    background: #2980b9;
    transform: translateY(-1px);
}

/* 加载状态 */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 60px;
    gap: 15px;
}

.spinner {
    width: 30px;
    height: 30px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.loading span {
    font-size: 16px;
    color: #666;
}

/* 错误信息 */
.error-section {
    margin-bottom: 25px;
    padding: 15px;
    background: #ffeaa7;
    border-left: 4px solid #fdcb6e;
    border-radius: 4px;
}

.error-section h3 {
    margin: 0 0 10px 0;
    color: #e17055;
    font-size: 16px;
}

.error {
    margin: 5px 0;
    color: #e17055;
    font-size: 14px;
}

/* 统计信息 */
.stats-section {
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.stats-section h3 {
    margin: 0 0 15px 0;
    color: #2c3e50;
    font-size: 18px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-label {
    font-weight: 500;
    color: #555;
}

.stat-value {
    font-weight: 600;
    color: #3498db;
    font-size: 16px;
}

/* 各个功能区域 */
.create-window-section,
.layout-section,
.windows-section,
.window-operations {
    margin-bottom: 30px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.create-window-section h3,
.layout-section h3,
.windows-section h3,
.window-operations h3 {
    margin: 0 0 20px 0;
    color: #2c3e50;
    font-size: 20px;
    font-weight: 600;
}

/* 表单样式 */
.form-group {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: end;
}

.form-group label {
    display: flex;
    flex-direction: column;
    gap: 5px;
    font-weight: 500;
    color: #555;
}

.form-group input,
.form-group select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    min-width: 120px;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.create-btn {
    padding: 10px 20px;
    background: #27ae60;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s;
}

.create-btn:hover {
    background: #219a52;
    transform: translateY(-1px);
}

/* 布局控制 */
.layout-controls {
    margin-bottom: 20px;
}

.layout-buttons {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.layout-btn {
    padding: 8px 16px;
    border: 2px solid #3498db;
    background: white;
    color: #3498db;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s;
}

.layout-btn:hover {
    background: #3498db;
    color: white;
}

.layout-btn.active {
    background: #3498db;
    color: white;
}

.arrange-btn {
    padding: 10px 20px;
    background: #9b59b6;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s;
}

.arrange-btn:hover {
    background: #8e44ad;
    transform: translateY(-1px);
}

/* 网格控制 */
.grid-controls {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
}

.grid-controls h4 {
    margin: 0 0 10px 0;
    color: #2c3e50;
    font-size: 16px;
}

.grid-form {
    display: flex;
    gap: 15px;
    align-items: end;
}

.grid-btn {
    padding: 8px 16px;
    background: #f39c12;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s;
}

.grid-btn:hover {
    background: #e67e22;
    transform: translateY(-1px);
}

/* 焦点控制 */
.focus-controls {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
}

.focus-controls h4 {
    margin: 0 0 10px 0;
    color: #2c3e50;
    font-size: 16px;
}

.focus-buttons {
    display: flex;
    gap: 10px;
}

.focus-btn {
    padding: 8px 16px;
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s;
}

.focus-btn:hover {
    background: #c0392b;
    transform: translateY(-1px);
}

/* 窗口列表 */
.no-windows {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 40px;
    background: #f8f9fa;
    border-radius: 6px;
}

.windows-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.window-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    transition: all 0.2s;
}

.window-item:hover {
    background: #e9ecef;
    transform: translateY(-1px);
}

.window-info {
    flex: 1;
}

.window-info h4 {
    margin: 0 0 5px 0;
    color: #2c3e50;
    font-size: 16px;
}

.window-url {
    margin: 0 0 10px 0;
    color: #666;
    font-size: 14px;
    font-family: monospace;
}

.window-state {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.state-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    background: #ecf0f1;
    color: #7f8c8d;
}

.state-badge.focused {
    background: #3498db;
    color: white;
}

.state-badge.minimized {
    background: #f39c12;
    color: white;
}

.state-badge.maximized {
    background: #27ae60;
    color: white;
}

.window-controls {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.control-btn {
    padding: 6px 12px;
    background: #95a5a6;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s;
}

.control-btn:hover {
    background: #7f8c8d;
    transform: translateY(-1px);
}

.control-btn.active {
    background: #3498db;
}

.control-btn.danger {
    background: #e74c3c;
}

.control-btn.danger:hover {
    background: #c0392b;
}

/* 窗口操作 */
.operation-group {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
}

.operation-group h4 {
    margin: 0 0 10px 0;
    color: #2c3e50;
    font-size: 16px;
}

.operation-btn {
    padding: 8px 16px;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s;
}

.operation-btn:hover {
    background: #2980b9;
    transform: translateY(-1px);
}

.batch-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.batch-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s;
}

.batch-btn.danger {
    background: #e74c3c;
    color: white;
}

.batch-btn.danger:hover {
    background: #c0392b;
    transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .window-manager-panel {
        padding: 15px;
    }

    .panel-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .window-item {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }

    .form-group {
        flex-direction: column;
        align-items: stretch;
    }

    .form-group label {
        width: 100%;
    }

    .layout-buttons {
        flex-direction: column;
    }

    .layout-btn {
        width: 100%;
    }

    .focus-buttons {
        flex-direction: column;
    }

    .focus-btn {
        width: 100%;
    }

    .window-controls {
        justify-content: center;
    }

    .batch-controls {
        flex-direction: column;
    }

    .batch-btn {
        width: 100%;
    }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
    .window-manager-panel {
        background: #1a1a1a;
        color: #e0e0e0;
    }

    .panel-header {
        border-bottom-color: #333;
    }

    .panel-header h2 {
        color: #e0e0e0;
    }

    .stats-section,
    .create-window-section,
    .layout-section,
    .windows-section,
    .window-operations {
        background: #2a2a2a;
    }

    .stat-item {
        background: #333;
    }

    .form-group input,
    .form-group select {
        background: #333;
        border-color: #555;
        color: #e0e0e0;
    }

    .window-item {
        background: #333;
        border-color: #555;
    }

    .window-item:hover {
        background: #404040;
    }

    .grid-controls,
    .focus-controls,
    .operation-group {
        background: #333;
    }

    .no-windows {
        background: #333;
    }

    .state-badge {
        background: #555;
        color: #ccc;
    }
}