import React from 'react';
import * as LucideIcons from 'lucide-react';

// A type guard to check if a key is a valid Lucide icon name
const isLucideIconName = (name: string | number | symbol): name is keyof typeof LucideIcons => {
  return typeof name === 'string' && name in LucideIcons;
};

interface IconProps extends Omit<LucideIcons.LucideProps, 'name'> {
  name: string; 
}

const Icon: React.FC<IconProps> = ({ name: iconName, className, ...props }) => {
  // 1. Check if it's an image URL (a simple but effective check)
  const isUrl = iconName.includes('/') || iconName.startsWith('data:');

  if (isUrl) {
    return <img src={iconName} alt={`${iconName} icon`} className={`plugin-icon-image ${className || ''}`} />;
  }

  // 2. Check if it's a valid Lucide icon name
  if (isLucideIconName(iconName)) {
    const LucideIconComponent = LucideIcons[iconName];
    // The props are spread correctly, ts-ignore can be used if TypeScript has trouble 
    // inferring the type of the dynamically selected component.
    // @ts-ignore 
    return <LucideIconComponent className={className} {...props} />;
  }

  // 3. Fallback to a default icon if name is invalid
  // @ts-ignore
  return <LucideIcons.HelpCircle className={className} {...props} />;
};

export default Icon;
