import React from 'react';
import { Plugin } from '../core/types';
import Icon from './Icon';

interface AgentButtonProps {
  agent: Plugin & { name: string }; // Ensure name is always present
  onClick: (agent: Plugin) => void;
  isActive: boolean;
}

const AgentButton: React.FC<AgentButtonProps> = ({ agent, onClick, isActive }) => (
  <button className={`agent-button ${isActive ? 'active' : ''}`} onClick={() => onClick(agent)} aria-pressed={isActive}>
    <span className="agent-button-icon">
      <Icon name={agent.icon} />
    </span>
    {agent.name}
  </button>
);

export default AgentButton;