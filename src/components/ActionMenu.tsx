import React, { useEffect, useRef, useLayoutEffect, useState } from 'react';
import ReactDOM from 'react-dom';
import { Scissors, Upload } from 'lucide-react';

interface ActionMenuProps {
  triggerRef: React.RefObject<HTMLElement>;
  onClose: () => void;
  onFileUpload: () => void;
  onScreenshot: () => void;
}

const ActionMenu: React.FC<ActionMenuProps> = ({ triggerRef, onClose, onFileUpload, onScreenshot }) => {
  const menuRef = useRef<HTMLDivElement>(null);
  const [position, setPosition] = useState<{ top: number; left: number }>({ top: 0, left: 0 });

  useLayoutEffect(() => {
    if (triggerRef.current && menuRef.current) {
      const triggerRect = triggerRef.current.getBoundingClientRect();
      const menuRect = menuRef.current.getBoundingClientRect();
      
      setPosition({
        top: triggerRect.top - menuRect.height - 8, // 8px gap
        left: triggerRect.left,
      });
    }
  }, [triggerRef]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Close if click is outside the menu AND the trigger button
      if (
        menuRef.current && !menuRef.current.contains(event.target as Node) &&
        triggerRef.current && !triggerRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose, triggerRef]);

  const menuContent = (
    <div 
        className="action-menu-container" 
        ref={menuRef} 
        style={{ top: `${position.top}px`, left: `${position.left}px` }}
        role="menu"
    >
      <div className="action-menu-item" onClick={onScreenshot} role="menuitem">
        <Scissors size={18} />
        <span>截图提问</span>
      </div>
      <div className="action-menu-item" onClick={onFileUpload} role="menuitem">
        <Upload size={18} />
        <span>上传文件</span>
      </div>
    </div>
  );

  const portalRoot = document.getElementById('portals');
  if (!portalRoot) return null;

  return ReactDOM.createPortal(menuContent, portalRoot);
};

export default ActionMenu;