import React from 'react';
import { Plugin } from '../core/types';
import Icon from './Icon';

interface FloatingStatusDisplayProps {
  agent: Plugin | null;
  onClear: () => void;
}

const FloatingStatusDisplay: React.FC<FloatingStatusDisplayProps> = ({ agent, onClear }) => {
  // Only show for active list-mode plugins (which function as agents)
  if (!agent || agent.mode !== 'list') {
    return null;
  }

  return (
    <div className="floating-status" role="status" aria-live="polite">
      <span className="floating-status-icon">
        <Icon name={agent.icon} />
      </span>
      <span className="floating-status-text">Active: {agent.name}</span>
      <button onClick={onClear} className="floating-status-clear" aria-label={`Clear ${agent.name} agent`}>
        &times;
      </button>
    </div>
  );
};

export default FloatingStatusDisplay;