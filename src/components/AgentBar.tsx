import React, { useState } from 'react';
import { Plugin } from '../core/types';
import AgentButton from './AgentButton';
import { MoreHorizontal, Sparkles, Mic } from 'lucide-react';

interface AgentBarProps {
  agents: Plugin[];
  onAgentSelect: (agent: Plugin) => void;
  activeAgentId: string | null;
}

const AgentBar: React.FC<AgentBarProps> = ({ agents, onAgentSelect, activeAgentId }) => {
  const [showMore, setShowMore] = useState(false);
  const agentPlugins = agents;
  const displayAgents = showMore ? agentPlugins : agentPlugins.slice(0, 4);
  const hasMoreButton = agentPlugins.length > 4;

  console.log(agents)
  return (
    <div className="agent-bar">
      <div className="agent-buttons-group">
        {displayAgents.map(agent => (
          <AgentButton key={agent.id} agent={agent} onClick={onAgentSelect} isActive={agent.id === activeAgentId} />
        ))}
        {hasMoreButton && !showMore && (
          <AgentButton key="more-button" agent={{id: 'more', name: 'More', icon: 'MoreHorizontal', mode: 'list', keyword: 'more', run: async () => ''}} onClick={() => setShowMore(true)} isActive={false} />
        )}
      </div>

      <div className="static-icons-group">
        <button className="static-icon-button" aria-label="Sparkle feature">
          <Sparkles size={18} />
        </button>
        <button className="static-icon-button" aria-label="Voice input">
          <Mic size={18} />
        </button>
      </div>
    </div>
  );
};

export default AgentBar;