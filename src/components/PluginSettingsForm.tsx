import React, { useState } from 'react';
import { Plugin, PluginSetting } from '../core/types';
import * as SettingsManager from '../core/settingsManager';
import ToggleSwitch from './ToggleSwitch';

interface PluginSettingsFormProps {
  plugin: Plugin;
}

const PluginSettingsForm: React.FC<PluginSettingsFormProps> = ({ plugin }) => {
  if (!plugin.settings || plugin.settings.length === 0) {
    return <div>This plugin has no settings.</div>;
  }

  return (
    <div className="plugin-settings-form">
      <div className="plugin-settings-form-header">
        <h2 className="plugin-settings-form-title">{plugin.name}</h2>
        <p className="plugin-settings-form-description">{plugin.description}</p>
      </div>
      {plugin.settings.map(setting => (
        <SettingItem key={setting.id} pluginId={plugin.id} setting={setting} />
      ))}
    </div>
  );
};

interface SettingItemProps {
  pluginId: string;
  setting: PluginSetting;
}

const SettingItem: React.FC<SettingItemProps> = ({ pluginId, setting }) => {
  const [value, setValue] = useState(() => SettingsManager.getSetting(pluginId, setting.id) ?? setting.defaultValue);

  const handleValueChange = (newValue: any) => {
    setValue(newValue);
    SettingsManager.setSetting(pluginId, setting.id, newValue);
  };

  const renderControl = () => {
    switch (setting.type) {
      case 'boolean':
        return (
          <ToggleSwitch
            checked={!!value}
            onChange={(e) => handleValueChange(e.target.checked)}
          />
        );
      case 'password':
        return (
           <input
            id={setting.id}
            type="password"
            value={value || ''}
            onChange={(e) => handleValueChange(e.target.value)}
            className="setting-item-control"
          />
        );
      case 'select':
        return (
          <select
            id={setting.id}
            value={value || ''}
            onChange={(e) => handleValueChange(e.target.value)}
            className="setting-item-control"
          >
            {setting.options?.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );
      case 'string':
      default:
        return (
          <input
            id={setting.id}
            type="text"
            value={value || ''}
            onChange={(e) => handleValueChange(e.target.value)}
            className="setting-item-control"
          />
        );
    }
  };

  return (
    <div className="setting-item">
       <div className="setting-item-label-container">
            <label htmlFor={setting.id} className="setting-item-label">{setting.title}</label>
            {setting.type === 'boolean' && renderControl()}
       </div>
      {setting.description && <p className="setting-item-description">{setting.description}</p>}
      {setting.type !== 'boolean' && renderControl()}
    </div>
  );
};

export default PluginSettingsForm;