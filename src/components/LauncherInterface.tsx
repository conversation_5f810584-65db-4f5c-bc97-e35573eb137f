import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Plugin, SearchResultItem, PluginAction } from '../core/types';
import * as PluginRegistry from '../core/pluginRegistry';
import { DEFAULT_PLACEHOLDER } from '../core/constants';
import { useLauncher } from '../hooks/useLauncher';
import './LauncherInterface.css';

interface LauncherInterfaceProps {
    onHeightChange?: (height: number) => void;
}

/**
 * 启动器主界面组件 - 简化版本
 * 类似 Alfred/Raycast 的搜索界面
 */
const LauncherInterface: React.FC<LauncherInterfaceProps> = ({ onHeightChange }) => {
    const [inputValue, setInputValue] = useState('');
    const [searchResults, setSearchResults] = useState<SearchResultItem[]>([]);
    const [selectedIndex, setSelectedIndex] = useState(0);

    const inputRef = useRef<HTMLInputElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);

    const {
        launcherState,
        createPluginWindow,
        hideLauncher,
        setLauncherSize,
        handleBlur,
        error: launcherError
    } = useLauncher();

    // 计算和更新界面高度
    const updateHeight = useCallback(() => {
        if (containerRef.current) {
            const baseHeight = 60; // 输入框基础高度
            const resultHeight = searchResults.length * 64; // 每个结果项64px
            const totalHeight = baseHeight + resultHeight + (searchResults.length > 0 ? 8 : 0); // 添加一些间距
            onHeightChange?.(totalHeight);
            setLauncherSize(totalHeight);
        }
    }, [searchResults.length, onHeightChange, setLauncherSize]);

    // 处理搜索
    const performSearch = useCallback(async (query: string) => {
        if (!query.trim()) {
            setSearchResults([]);
            setSelectedIndex(0);
            return;
        }

        try {
            const results = PluginRegistry.search(query);
            setSearchResults(results.slice(0, 6)); // 限制显示6个结果
            setSelectedIndex(0);
        } catch (error) {
            console.error('搜索失败:', error);
            setSearchResults([]);
        }
    }, []);

    // 处理输入变化
    const handleInputChange = useCallback(async (value: string) => {
        setInputValue(value);
        await performSearch(value);
    }, [performSearch]);

    // 执行选中的项目
    const executeSelectedItem = useCallback(async (item: SearchResultItem) => {
        if (item.type === 'plugin') {
            const plugin = item.plugin;

            if (plugin.mode === 'view') {
                // View 模式插件：创建独立窗口
                try {
                    const viewPath = plugin.id.startsWith('view-') ? plugin.id.substring('view-'.length) : plugin.id;
                    const pluginUrl = `http://localhost:5173/src/plugins/${viewPath}/view/index.html`;

                    await createPluginWindow(
                        plugin.id,
                        plugin.name,
                        pluginUrl,
                        800,
                        600
                    );

                    // 隐藏启动器
                    await hideLauncher();

                } catch (error) {
                    console.error('创建插件窗口失败:', error);
                }
            } else {
                // List 模式插件：在输入框中预填充关键词
                if (plugin.keyword) {
                    setInputValue(`${plugin.keyword} `);
                    inputRef.current?.focus();
                }
            }
        } else if (item.type === 'action') {
            const { plugin, action } = item;

            try {
                // 执行动作
                const regex = new RegExp(action.pattern, 'i');
                const match = inputValue.match(regex);
                const actionInput = match && match.length > 1 ? match[match.length - 1].trim() : '';

                const result = await PluginRegistry.runListPluginAction(plugin.id, actionInput, action);

                if (result.toLowerCase().includes('error:')) {
                    console.error('执行动作失败:', result);
                } else {
                    console.log('执行动作成功:', result);
                    await hideLauncher();
                }

            } catch (error) {
                console.error('执行动作失败:', error);
            }
        }

        // 清空搜索结果和输入
        setSearchResults([]);
        setInputValue('');
    }, [inputValue, createPluginWindow, hideLauncher]);

    // 处理键盘事件
    const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
        if (event.key === 'Escape') {
            if (searchResults.length > 0 || inputValue) {
                setSearchResults([]);
                setInputValue('');
            } else {
                hideLauncher();
            }
            return;
        }

        if (searchResults.length > 0) {
            if (event.key === 'ArrowDown') {
                event.preventDefault();
                setSelectedIndex(prev => (prev + 1) % searchResults.length);
            } else if (event.key === 'ArrowUp') {
                event.preventDefault();
                setSelectedIndex(prev => (prev - 1 + searchResults.length) % searchResults.length);
            } else if (event.key === 'Enter') {
                event.preventDefault();
                const selectedItem = searchResults[selectedIndex];
                if (selectedItem) {
                    executeSelectedItem(selectedItem);
                }
            }
        }
    }, [searchResults, inputValue, selectedIndex, executeSelectedItem, hideLauncher]);

    // 处理项目点击
    const handleItemClick = useCallback((item: SearchResultItem) => {
        executeSelectedItem(item);
    }, [executeSelectedItem]);

    // 当启动器显示时自动聚焦输入框
    useEffect(() => {
        if (launcherState.isVisible && inputRef.current) {
            const timer = setTimeout(() => {
                inputRef.current?.focus();
                inputRef.current?.select();
            }, 100);

            return () => clearTimeout(timer);
        }
    }, [launcherState.isVisible]);

    // 监听高度变化
    useEffect(() => {
        updateHeight();
    }, [searchResults.length, updateHeight]);

    // 初始化插件注册表
    useEffect(() => {
        PluginRegistry.initializeRegistry();
    }, []);

    return (
        <div ref={containerRef} className="launcher-interface">
            {/* 主搜索输入框 */}
            <div className="launcher-search-container">
                <input
                    ref={inputRef}
                    type="text"
                    value={inputValue}
                    onChange={(e) => handleInputChange(e.target.value)}
                    onKeyDown={handleKeyDown}
                    placeholder="搜索应用和功能..."
                    className="launcher-search-input"
                    spellCheck={false}
                    autoComplete="off"
                />
            </div>

            {/* 搜索结果列表 */}
            {searchResults.length > 0 && (
                <div className="launcher-results">
                    {searchResults.map((item, index) => (
                        <div
                            key={`${item.type}-${item.type === 'plugin' ? item.plugin.id : item.action.id}`}
                            className={`launcher-result-item ${index === selectedIndex ? 'selected' : ''}`}
                            onClick={() => handleItemClick(item)}
                            onMouseEnter={() => setSelectedIndex(index)}
                        >
                            <div className="result-icon">
                                {item.type === 'plugin' ? '🔌' : '⚡'}
                            </div>
                            <div className="result-content">
                                <div className="result-title">
                                    {item.type === 'plugin' ? item.plugin.name : item.action.name}
                                </div>
                                <div className="result-description">
                                    {item.type === 'plugin' ? item.plugin.description : item.plugin.name}
                                </div>
                            </div>
                            <div className="result-badge">
                                {item.type === 'plugin' ? (
                                    <span className={`mode-badge ${item.plugin.mode}`}>
                                        {item.plugin.mode === 'view' ? 'App' : 'Tool'}
                                    </span>
                                ) : (
                                    <span className="action-badge">Action</span>
                                )}
                            </div>
                        </div>
                    ))}
                </div>
            )}

            {/* 错误显示 */}
            {launcherError && (
                <div className="launcher-error">
                    {launcherError}
                </div>
            )}
        </div>
    );
};

export default LauncherInterface;