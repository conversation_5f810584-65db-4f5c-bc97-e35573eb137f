import React, { useState, useEffect, useMemo } from 'react';
import { usePerformanceMonitor } from '../hooks/usePerformanceMonitor';
import './PerformanceMonitorPanel.css';

interface MetricChartProps {
    title: string;
    data: Array<{ timestamp: string; value: number }>;
    unit: string;
    color: string;
    maxPoints?: number;
}

const MetricChart: React.FC<MetricChartProps> = ({
    title,
    data,
    unit,
    color,
    maxPoints = 50
}) => {
    const chartData = useMemo(() => {
        return data.slice(-maxPoints);
    }, [data, maxPoints]);

    const maxValue = Math.max(...chartData.map(d => d.value));
    const minValue = Math.min(...chartData.map(d => d.value));
    const range = maxValue - minValue || 1;

    return (
        <div className="metric-chart">
            <h4>{title}</h4>
            <div className="chart-container">
                <svg viewBox="0 0 400 200" className="chart-svg">
                    {/* 网格线 */}
                    <defs>
                        <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
                            <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#374151" strokeWidth="1" opacity="0.2" />
                        </pattern>
                    </defs>
                    <rect width="100%" height="100%" fill="url(#grid)" />

                    {/* 数据线 */}
                    {chartData.length > 1 && (
                        <polyline
                            fill="none"
                            stroke={color}
                            strokeWidth="2"
                            points={chartData.map((point, index) => {
                                const x = (index / (chartData.length - 1)) * 380 + 10;
                                const y = 190 - ((point.value - minValue) / range) * 180;
                                return `${x},${y}`;
                            }).join(' ')}
                        />
                    )}

                    {/* 数据点 */}
                    {chartData.map((point, index) => {
                        const x = (index / Math.max(chartData.length - 1, 1)) * 380 + 10;
                        const y = 190 - ((point.value - minValue) / range) * 180;
                        return (
                            <circle
                                key={index}
                                cx={x}
                                cy={y}
                                r="3"
                                fill={color}
                                className="chart-point"
                            >
                                <title>{`${point.value.toFixed(2)} ${unit}`}</title>
                            </circle>
                        );
                    })}

                    {/* Y轴标签 */}
                    <text x="5" y="15" fontSize="12" fill="#9ca3af">{maxValue.toFixed(1)}</text>
                    <text x="5" y="195" fontSize="12" fill="#9ca3af">{minValue.toFixed(1)}</text>
                </svg>
            </div>
            <div className="chart-footer">
                <span className="current-value" style={{ color }}>
                    {chartData.length > 0 ? `${chartData[chartData.length - 1].value.toFixed(2)} ${unit}` : `0 ${unit}`}
                </span>
            </div>
        </div>
    );
};

const PerformanceMonitorPanel: React.FC = () => {
    const {
        isMonitoring,
        systemSnapshot,
        appStats,
        performanceHistory,
        isLoading,
        error,
        startMonitoring,
        stopMonitoring,
        getMetricsByType,
        clearHistory,
        exportData,
        runBenchmark,
        refreshAllData,
        formatDuration,
        formatMemorySize,
        getCpuUsageColor,
        getMemoryUsageColor,
    } = usePerformanceMonitor();

    const [activeTab, setActiveTab] = useState<'overview' | 'charts' | 'stats' | 'tools'>('overview');
    const [cpuData, setCpuData] = useState<Array<{ timestamp: string; value: number }>>([]);
    const [memoryData, setMemoryData] = useState<Array<{ timestamp: string; value: number }>>([]);
    const [benchmarkResult, setBenchmarkResult] = useState<any>(null);

    // 更新图表数据
    useEffect(() => {
        const updateChartData = async () => {
            try {
                const cpuMetrics = await getMetricsByType('cpu_usage');
                const memoryMetrics = await getMetricsByType('memory_usage_percent');

                setCpuData(cpuMetrics.map(m => ({
                    timestamp: m.timestamp,
                    value: m.value
                })));

                setMemoryData(memoryMetrics.map(m => ({
                    timestamp: m.timestamp,
                    value: m.value
                })));
            } catch (err) {
                console.error('更新图表数据失败:', err);
            }
        };

        if (isMonitoring) {
            updateChartData();
        }
    }, [isMonitoring, performanceHistory, getMetricsByType]);

    const handleRunBenchmark = async () => {
        const result = await runBenchmark();
        setBenchmarkResult(result);
    };

    const handleExport = async (format: 'json' | 'csv') => {
        await exportData(format);
    };

    if (error) {
        return (
            <div className="performance-monitor-panel error">
                <h3>性能监控</h3>
                <div className="error-message">
                    <span>❌ 错误: {error}</span>
                    <button onClick={refreshAllData}>重试</button>
                </div>
            </div>
        );
    }

    return (
        <div className="performance-monitor-panel">
            <div className="panel-header">
                <h3>性能监控</h3>
                <div className="header-controls">
                    <span className={`status-indicator ${isMonitoring ? 'active' : 'inactive'}`}>
                        {isMonitoring ? '🟢 监控中' : '🔴 已停止'}
                    </span>
                    <button
                        onClick={isMonitoring ? stopMonitoring : startMonitoring}
                        disabled={isLoading}
                        className={isMonitoring ? 'stop-btn' : 'start-btn'}
                    >
                        {isLoading ? '⏳' : isMonitoring ? '⏹️ 停止' : '▶️ 启动'}
                    </button>
                </div>
            </div>

            <div className="panel-tabs">
                <button
                    className={activeTab === 'overview' ? 'active' : ''}
                    onClick={() => setActiveTab('overview')}
                >
                    总览
                </button>
                <button
                    className={activeTab === 'charts' ? 'active' : ''}
                    onClick={() => setActiveTab('charts')}
                >
                    图表
                </button>
                <button
                    className={activeTab === 'stats' ? 'active' : ''}
                    onClick={() => setActiveTab('stats')}
                >
                    统计
                </button>
                <button
                    className={activeTab === 'tools' ? 'active' : ''}
                    onClick={() => setActiveTab('tools')}
                >
                    工具
                </button>
            </div>

            <div className="panel-content">
                {activeTab === 'overview' && (
                    <div className="overview-tab">
                        {systemSnapshot && (
                            <div className="system-overview">
                                <div className="metric-card">
                                    <h4>CPU 使用率</h4>
                                    <div
                                        className="metric-value"
                                        style={{ color: getCpuUsageColor(systemSnapshot.cpu_usage) }}
                                    >
                                        {systemSnapshot.cpu_usage.toFixed(1)}%
                                    </div>
                                </div>

                                <div className="metric-card">
                                    <h4>内存使用率</h4>
                                    <div
                                        className="metric-value"
                                        style={{ color: getMemoryUsageColor(systemSnapshot.memory_usage_percent) }}
                                    >
                                        {systemSnapshot.memory_usage_percent.toFixed(1)}%
                                    </div>
                                    <div className="metric-detail">
                                        {formatMemorySize(systemSnapshot.memory_used)} / {formatMemorySize(systemSnapshot.memory_total)}
                                    </div>
                                </div>

                                <div className="metric-card">
                                    <h4>进程内存</h4>
                                    <div className="metric-value">
                                        {formatMemorySize(systemSnapshot.process_memory)}
                                    </div>
                                </div>

                                <div className="metric-card">
                                    <h4>运行时间</h4>
                                    <div className="metric-value">
                                        {formatDuration(systemSnapshot.uptime)}
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                )}

                {activeTab === 'charts' && (
                    <div className="charts-tab">
                        <div className="charts-grid">
                            <MetricChart
                                title="CPU 使用率"
                                data={cpuData}
                                unit="%"
                                color="#3b82f6"
                            />
                            <MetricChart
                                title="内存使用率"
                                data={memoryData}
                                unit="%"
                                color="#ef4444"
                            />
                        </div>
                    </div>
                )}

                {activeTab === 'stats' && (
                    <div className="stats-tab">
                        {appStats && (
                            <div className="app-stats">
                                <div className="stat-item">
                                    <span className="stat-label">启动时间:</span>
                                    <span className="stat-value">
                                        {appStats.startup_time ? formatDuration(appStats.startup_time) : '未知'}
                                    </span>
                                </div>
                                <div className="stat-item">
                                    <span className="stat-label">已执行命令:</span>
                                    <span className="stat-value">{appStats.total_commands_executed}</span>
                                </div>
                                <div className="stat-item">
                                    <span className="stat-label">平均命令响应时间:</span>
                                    <span className="stat-value">{appStats.average_command_response_time.toFixed(2)}ms</span>
                                </div>
                                <div className="stat-item">
                                    <span className="stat-label">已处理事件:</span>
                                    <span className="stat-value">{appStats.total_events_processed}</span>
                                </div>
                                <div className="stat-item">
                                    <span className="stat-label">平均事件延迟:</span>
                                    <span className="stat-value">{appStats.average_event_latency.toFixed(2)}ms</span>
                                </div>
                                <div className="stat-item">
                                    <span className="stat-label">已加载插件:</span>
                                    <span className="stat-value">{appStats.plugins_loaded}</span>
                                </div>
                                <div className="stat-item">
                                    <span className="stat-label">插件加载总时间:</span>
                                    <span className="stat-value">{formatDuration(appStats.total_plugin_load_time)}</span>
                                </div>
                            </div>
                        )}
                    </div>
                )}

                {activeTab === 'tools' && (
                    <div className="tools-tab">
                        <div className="tool-section">
                            <h4>数据管理</h4>
                            <div className="tool-buttons">
                                <button onClick={clearHistory}>清除历史数据</button>
                                <button onClick={() => handleExport('json')}>导出JSON</button>
                                <button onClick={() => handleExport('csv')}>导出CSV</button>
                            </div>
                        </div>

                        <div className="tool-section">
                            <h4>性能测试</h4>
                            <div className="tool-buttons">
                                <button onClick={handleRunBenchmark} disabled={isLoading}>
                                    {isLoading ? '运行中...' : '运行基准测试'}
                                </button>
                            </div>

                            {benchmarkResult && (
                                <div className="benchmark-result">
                                    <h5>基准测试结果</h5>
                                    <div className="benchmark-stats">
                                        <div>总耗时: {benchmarkResult.total_duration_ms}ms</div>
                                        {benchmarkResult.tests?.map((test: any, index: number) => (
                                            <div key={index}>
                                                {test.test_name}: {test.duration_ms}ms
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default PerformanceMonitorPanel;