/* 布局引擎管理面板样式 */
.layout-engine-panel {
    padding: 20px;
    background: var(--bg-primary, #ffffff);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    max-width: 1200px;
    margin: 0 auto;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 暗色主题 */
[data-theme="dark"] .layout-engine-panel {
    background: var(--bg-primary, #1a1a1a);
    color: var(--text-primary, #ffffff);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* 面板头部 */
.panel-header {
    margin-bottom: 24px;
    text-align: center;
}

.panel-header h2 {
    margin: 0 0 8px 0;
    font-size: 28px;
    font-weight: 700;
    color: var(--text-primary, #333333);
}

.panel-header p {
    margin: 0;
    font-size: 16px;
    color: var(--text-secondary, #666666);
}

/* 错误横幅 */
.error-banner {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    margin: 16px 0;
    background: #fee2e2;
    border: 1px solid #fecaca;
    border-radius: 8px;
    color: #dc2626;
    font-size: 14px;
}

[data-theme="dark"] .error-banner {
    background: #3f1f1f;
    border-color: #7f1d1d;
    color: #f87171;
}

.error-banner button {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    font-size: 16px;
    padding: 4px;
    border-radius: 4px;
}

.error-banner button:hover {
    background: rgba(220, 38, 38, 0.1);
}

/* 标签页导航 */
.tab-navigation {
    display: flex;
    gap: 4px;
    margin-bottom: 24px;
    background: var(--bg-secondary, #f8f9fa);
    border-radius: 12px;
    padding: 4px;
}

[data-theme="dark"] .tab-navigation {
    background: var(--bg-secondary, #2a2a2a);
}

.tab-button {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 16px;
    background: none;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-secondary, #666666);
}

.tab-button:hover {
    background: var(--bg-hover, #e9ecef);
    color: var(--text-primary, #333333);
}

.tab-button.active {
    background: var(--bg-primary, #ffffff);
    color: var(--primary-color, #007bff);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .tab-button:hover {
    background: var(--bg-hover, #3a3a3a);
    color: var(--text-primary, #ffffff);
}

[data-theme="dark"] .tab-button.active {
    background: var(--bg-primary, #1a1a1a);
    color: var(--primary-color, #4a9eff);
}

.tab-icon {
    font-size: 16px;
}

.tab-label {
    display: none;
}

@media (min-width: 768px) {
    .tab-label {
        display: block;
    }
}

/* 标签页内容 */
.tab-content {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 区块 */
.section {
    background: var(--bg-secondary, #f8f9fa);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

[data-theme="dark"] .section {
    background: var(--bg-secondary, #2a2a2a);
}

.section h3 {
    margin: 0 0 16px 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary, #333333);
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

/* 控制组 */
.control-group {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.control-group label {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-secondary, #666666);
    min-width: 120px;
}

.control-group input {
    padding: 8px 12px;
    border: 1px solid var(--border-color, #dee2e6);
    border-radius: 6px;
    background: var(--bg-primary, #ffffff);
    color: var(--text-primary, #333333);
    font-size: 14px;
}

[data-theme="dark"] .control-group input {
    background: var(--bg-primary, #1a1a1a);
    border-color: var(--border-color, #404040);
    color: var(--text-primary, #ffffff);
}

/* 布局控制 */
.layout-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    align-items: center;
}

/* 按钮样式 */
.primary-button,
.secondary-button,
.create-button,
.optimize-button,
.preview-button,
.apply-button,
.stop-button {
    padding: 10px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.primary-button {
    background: var(--primary-color, #007bff);
    color: white;
}

.primary-button:hover:not(:disabled) {
    background: var(--primary-hover, #0056b3);
    transform: translateY(-1px);
}

.secondary-button {
    background: var(--secondary-color, #6c757d);
    color: white;
}

.secondary-button:hover:not(:disabled) {
    background: var(--secondary-hover, #545b62);
}

.create-button {
    background: var(--success-color, #28a745);
    color: white;
}

.create-button:hover:not(:disabled) {
    background: var(--success-hover, #1e7e34);
}

.optimize-button {
    background: var(--warning-color, #ffc107);
    color: #212529;
}

.optimize-button:hover:not(:disabled) {
    background: var(--warning-hover, #e0a800);
}

.preview-button {
    background: var(--info-color, #17a2b8);
    color: white;
    padding: 6px 12px;
    font-size: 12px;
}

.apply-button {
    background: var(--success-color, #28a745);
    color: white;
    padding: 6px 12px;
    font-size: 12px;
}

.stop-button {
    background: var(--danger-color, #dc3545);
    color: white;
}

button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* 布局预览 */
.layout-preview {
    background: var(--bg-primary, #ffffff);
    border: 1px solid var(--border-color, #dee2e6);
    border-radius: 8px;
    padding: 16px;
    min-height: 300px;
}

[data-theme="dark"] .layout-preview {
    background: var(--bg-primary, #1a1a1a);
    border-color: var(--border-color, #404040);
}

.preview-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.preview-screen {
    position: relative;
    background: #f0f0f0;
    border: 2px solid var(--border-color, #dee2e6);
    border-radius: 6px;
    height: 200px;
    overflow: hidden;
}

[data-theme="dark"] .preview-screen {
    background: #333333;
    border-color: var(--border-color, #404040);
}

.preview-window {
    position: absolute;
    background: var(--primary-color, #007bff);
    border: 1px solid var(--primary-hover, #0056b3);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    font-weight: 500;
    opacity: 0.8;
    transition: opacity 0.2s ease;
}

.preview-window:hover {
    opacity: 1;
}

.preview-actions {
    display: flex;
    justify-content: center;
}

.no-preview {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: var(--text-secondary, #666666);
    font-style: italic;
}

/* 约束网格 */
.constraints-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.constraint-item {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.constraint-item label {
    font-size: 13px;
    font-weight: 500;
    color: var(--text-secondary, #666666);
}

.constraint-item input {
    padding: 8px 12px;
    border: 1px solid var(--border-color, #dee2e6);
    border-radius: 4px;
    background: var(--bg-primary, #ffffff);
    color: var(--text-primary, #333333);
}

[data-theme="dark"] .constraint-item input {
    background: var(--bg-primary, #1a1a1a);
    border-color: var(--border-color, #404040);
    color: var(--text-primary, #ffffff);
}

/* 模板网格 */
.templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 16px;
}

.template-card {
    background: var(--bg-primary, #ffffff);
    border: 1px solid var(--border-color, #dee2e6);
    border-radius: 8px;
    padding: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.template-card:hover {
    border-color: var(--primary-color, #007bff);
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
}

.template-card.selected {
    border-color: var(--primary-color, #007bff);
    background: rgba(0, 123, 255, 0.05);
}

[data-theme="dark"] .template-card {
    background: var(--bg-primary, #1a1a1a);
    border-color: var(--border-color, #404040);
}

[data-theme="dark"] .template-card.selected {
    background: rgba(74, 158, 255, 0.1);
}

.template-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}

.template-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary, #333333);
}

.usage-count {
    font-size: 12px;
    color: var(--text-secondary, #666666);
    background: var(--bg-secondary, #f8f9fa);
    padding: 2px 6px;
    border-radius: 4px;
}

[data-theme="dark"] .usage-count {
    background: var(--bg-secondary, #2a2a2a);
}

.template-description {
    margin: 0 0 12px 0;
    font-size: 14px;
    color: var(--text-secondary, #666666);
    line-height: 1.4;
}

.template-config {
    display: flex;
    gap: 12px;
    margin-bottom: 12px;
    font-size: 12px;
    color: var(--text-secondary, #666666);
}

.template-actions {
    display: flex;
    gap: 8px;
}

/* 模态框 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    animation: fadeIn 0.2s ease;
}

.modal {
    background: var(--bg-primary, #ffffff);
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow: auto;
    animation: slideIn 0.3s ease;
}

[data-theme="dark"] .modal {
    background: var(--bg-primary, #1a1a1a);
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }

    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color, #dee2e6);
}

[data-theme="dark"] .modal-header {
    border-bottom-color: var(--border-color, #404040);
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary, #333333);
}

.close-button {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: var(--text-secondary, #666666);
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.close-button:hover {
    background: var(--bg-secondary, #f8f9fa);
    color: var(--text-primary, #333333);
}

[data-theme="dark"] .close-button:hover {
    background: var(--bg-secondary, #2a2a2a);
    color: var(--text-primary, #ffffff);
}

.modal-content {
    padding: 20px;
}

.modal-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    padding: 16px 20px;
    border-top: 1px solid var(--border-color, #dee2e6);
}

[data-theme="dark"] .modal-actions {
    border-top-color: var(--border-color, #404040);
}

/* 表单组 */
.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary, #333333);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-color, #dee2e6);
    border-radius: 4px;
    background: var(--bg-primary, #ffffff);
    color: var(--text-primary, #333333);
    font-size: 14px;
    box-sizing: border-box;
}

[data-theme="dark"] .form-group input,
[data-theme="dark"] .form-group select,
[data-theme="dark"] .form-group textarea {
    background: var(--bg-primary, #1a1a1a);
    border-color: var(--border-color, #404040);
    color: var(--text-primary, #ffffff);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-group input[type="range"] {
    padding: 0;
}

/* 动画配置 */
.animation-config {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.config-item {
    display: flex;
    align-items: center;
    gap: 12px;
}

.config-item label {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary, #333333);
    min-width: 120px;
}

.config-item input[type="range"] {
    flex: 1;
}

.config-item select {
    min-width: 120px;
}

/* 动画状态 */
.animation-status {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 12px;
}

.progress-bar {
    flex: 1;
    height: 8px;
    background: var(--bg-secondary, #f8f9fa);
    border-radius: 4px;
    overflow: hidden;
}

[data-theme="dark"] .progress-bar {
    background: var(--bg-secondary, #2a2a2a);
}

.progress-fill {
    height: 100%;
    background: var(--primary-color, #007bff);
    transition: width 0.1s ease;
}

/* 统计网格 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
}

.stat-card {
    background: var(--bg-primary, #ffffff);
    border: 1px solid var(--border-color, #dee2e6);
    border-radius: 8px;
    padding: 16px;
}

[data-theme="dark"] .stat-card {
    background: var(--bg-primary, #1a1a1a);
    border-color: var(--border-color, #404040);
}

.stat-card h4 {
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary, #333333);
    text-transform: capitalize;
}

.stat-items {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stat-label {
    font-size: 13px;
    color: var(--text-secondary, #666666);
}

.stat-value {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary, #333333);
}

/* 统计操作 */
.stats-actions {
    display: flex;
    gap: 8px;
}

.stats-actions button {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    background: var(--secondary-color, #6c757d);
    color: white;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
}

.stats-actions button:hover {
    background: var(--secondary-hover, #545b62);
}

/* 系统信息 */
.system-info {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--border-color, #dee2e6);
}

[data-theme="dark"] .info-item {
    border-bottom-color: var(--border-color, #404040);
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-size: 14px;
    color: var(--text-secondary, #666666);
}

.info-value {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary, #333333);
}

/* 加载覆盖层 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    z-index: 100;
}

[data-theme="dark"] .loading-overlay {
    background: rgba(26, 26, 26, 0.8);
}

.loading-spinner {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 500;
    color: var(--text-primary, #333333);
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .layout-engine-panel {
        padding: 16px;
    }

    .templates-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .constraints-grid {
        grid-template-columns: 1fr;
    }

    .layout-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .control-group {
        flex-direction: column;
        align-items: stretch;
    }

    .control-group label {
        min-width: auto;
    }
}

/* 自定义滚动条 */
.layout-engine-panel *::-webkit-scrollbar {
    width: 6px;
}

.layout-engine-panel *::-webkit-scrollbar-track {
    background: var(--bg-secondary, #f8f9fa);
    border-radius: 3px;
}

[data-theme="dark"] .layout-engine-panel *::-webkit-scrollbar-track {
    background: var(--bg-secondary, #2a2a2a);
}

.layout-engine-panel *::-webkit-scrollbar-thumb {
    background: var(--text-secondary, #666666);
    border-radius: 3px;
}

.layout-engine-panel *::-webkit-scrollbar-thumb:hover {
    background: var(--text-primary, #333333);
}

[data-theme="dark"] .layout-engine-panel *::-webkit-scrollbar-thumb:hover {
    background: var(--text-primary, #ffffff);
}