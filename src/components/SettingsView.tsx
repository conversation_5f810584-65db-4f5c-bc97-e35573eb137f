import React, { useState, useMemo, useEffect, useRef, useCallback } from 'react';
import * as PluginRegistry from '../core/pluginRegistry';
import { Plugin } from '../core/types';
import Icon from './Icon';
import PluginSettingsForm from './PluginSettingsForm';

interface SettingsViewProps {
  onClose: () => void;
}

const SettingsView: React.FC<SettingsViewProps> = ({ onClose }) => {
  const [selectedItemId, setSelectedItemId] = useState<string>('general');
  const [isSidebarFocused, setIsSidebarFocused] = useState(true);

  const sidebarRef = useRef<HTMLElement>(null);
  const contentRef = useRef<HTMLElement>(null);

  const pluginsWithSettings = useMemo(() => PluginRegistry.getPluginsWithSettings(), []);

  const navItems = useMemo(() => [
    { id: 'general', name: 'Appearance', icon: 'SlidersHorizontal' },
    ...pluginsWithSettings.map(p => ({ id: p.id, name: p.name, icon: p.icon }))
  ], [pluginsWithSettings]);

  const selectedPlugin = useMemo(() => {
    if (!selectedItemId || selectedItemId === 'general') return null;
    return pluginsWithSettings.find(p => p.id === selectedItemId);
  }, [selectedItemId, pluginsWithSettings]);

  useEffect(() => {
    // Focus the sidebar when the component mounts
    sidebarRef.current?.focus();
  }, []);

  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    e.stopPropagation();

    if (e.key === 'Escape') {
        if (!isSidebarFocused) {
            e.preventDefault();
            setIsSidebarFocused(true);
            sidebarRef.current?.focus();
        } else {
            onClose();
        }
        return;
    }
    
    if (isSidebarFocused) {
        const currentIdx = navItems.findIndex(item => item.id === selectedItemId);
        if (e.key === 'ArrowUp') {
            e.preventDefault();
            const nextIdx = (currentIdx - 1 + navItems.length) % navItems.length;
            setSelectedItemId(navItems[nextIdx].id);
        } else if (e.key === 'ArrowDown') {
            e.preventDefault();
            const nextIdx = (currentIdx + 1) % navItems.length;
            setSelectedItemId(navItems[nextIdx].id);
        } else if (e.key === 'Enter' || e.key === 'ArrowRight') {
            e.preventDefault();
            setIsSidebarFocused(false);
            contentRef.current?.focus();
        }
    } else { // Content is focused
        if (e.key === 'ArrowLeft') {
            const firstContentElement = contentRef.current?.querySelector('input, select, button');
            if (document.activeElement === firstContentElement) {
                e.preventDefault();
                setIsSidebarFocused(true);
                sidebarRef.current?.focus();
            }
        }
    }
  }, [isSidebarFocused, navItems, selectedItemId, onClose]);

  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);
  
  // Update focus on sidebar items
  useEffect(() => {
    if (isSidebarFocused) {
      const activeElement = sidebarRef.current?.querySelector(`[data-id="${selectedItemId}"]`);
      (activeElement as HTMLElement)?.focus();
    }
  }, [selectedItemId, isSidebarFocused]);

  return (
    <div className="settings-view">
      <nav 
        ref={sidebarRef} 
        className="settings-sidebar"
        tabIndex={-1}
        onFocus={() => setIsSidebarFocused(true)}
      >
        <div className="settings-nav-header">General</div>
        <div
          data-id="general"
          tabIndex={-1}
          className={`settings-nav-item ${selectedItemId === 'general' ? 'active' : ''}`}
          onClick={() => setSelectedItemId('general')}
        >
          <Icon name="SlidersHorizontal" />
          <span>Appearance</span>
        </div>
        
        {pluginsWithSettings.length > 0 && (
          <>
            <div className="settings-nav-header">Plugin Settings</div>
            {pluginsWithSettings.map(plugin => (
              <div
                key={plugin.id}
                data-id={plugin.id}
                tabIndex={-1}
                className={`settings-nav-item ${selectedItemId === plugin.id ? 'active' : ''}`}
                onClick={() => setSelectedItemId(plugin.id)}
              >
                <Icon name={plugin.icon} />
                <span>{plugin.name}</span>
              </div>
            ))}
          </>
        )}
      </nav>
      <main 
        ref={contentRef} 
        className="settings-content"
        tabIndex={-1}
        onFocus={() => setIsSidebarFocused(false)}
      >
        {selectedPlugin ? (
          <PluginSettingsForm key={selectedPlugin.id} plugin={selectedPlugin} />
        ) : (
          <div>
            <div className="plugin-settings-form-header">
                <h2 className="plugin-settings-form-title">Appearance</h2>
                <p className="plugin-settings-form-description">Configure the look and feel of NovaRay.</p>
            </div>
             <p>Theme settings will be managed here in the future.</p>
          </div>
        )}
      </main>
    </div>
  );
};

export default SettingsView;
