import React, { useState, useEffect, useCallback } from 'react';
import { ListItem, OnSelectAction } from '../core/types';
import Icon from './Icon';
import ItemPreview from './ItemPreview';

type Theme = 'light' | 'dark' | 'system';

interface ListViewProps {
  items: ListItem[];
  onActionComplete: (action: OnSelectAction) => void;
  setTheme: (theme: Theme) => void;
  onShowList: (items: ListItem[], parentItem: ListItem) => void;
}

const ListView: React.FC<ListViewProps> = ({ items, onActionComplete, setTheme, onShowList }) => {
  const [selectedIndex, setSelectedIndex] = useState(0);

  const handleAction = useCallback((action: OnSelectAction, parentItem: ListItem) => {
    switch (action.type) {
      case 'copy':
        navigator.clipboard.writeText(action.payload)
          .then(() => console.log('Copied to clipboard:', action.payload))
          .catch(err => console.error('Failed to copy:', err));
        onActionComplete(action);
        break;
      case 'open-url':
        window.open(action.payload, '_blank', 'noopener,noreferrer');
        onActionComplete(action);
        break;
      case 'set-theme':
        setTheme(action.payload as Theme);
        onActionComplete(action);
        break;
      case 'show-list':
        onShowList(action.payload as ListItem[], parentItem);
        break; // Does not complete/close
      case 'install-plugin':
      case 'uninstall-plugin':
          onActionComplete(action); // Let App handle this special case
          break;
      default:
        console.warn('Unhandled action type:', (action as any).type);
        onActionComplete(action);
        break;
    }
  }, [setTheme, onShowList, onActionComplete]);

  useEffect(() => {
    setSelectedIndex(0);
  }, [items]);

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      // Allow escape to propagate to App.tsx to handle back navigation or closing
      return;
    }

    if (items.length === 0) return;
    
    event.stopPropagation(); // Stop key events from bubbling up further

    if (event.key === 'ArrowDown') {
      event.preventDefault();
      setSelectedIndex(prev => (prev + 1) % items.length);
    } else if (event.key === 'ArrowUp') {
      event.preventDefault();
      setSelectedIndex(prev => (prev - 1 + items.length) % items.length);
    } else if (event.key === 'Enter') {
      event.preventDefault();
      const selectedItem = items[selectedIndex];
      if (selectedItem) {
        handleAction(selectedItem.onSelectAction, selectedItem);
      }
    }
  }, [items, selectedIndex, handleAction]);

  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown, true); 
    return () => {
      window.removeEventListener('keydown', handleKeyDown, true);
    };
  }, [handleKeyDown]);

  if (items.length === 0) return null;

  const hasPreview = items.some(item => !!item.preview);

  const listContent = (
    <ul className="list-view-list" role="listbox">
      {items.map((item, index) => (
        <li
          key={item.id}
          className={`list-view-item ${index === selectedIndex ? 'selected' : ''}`}
          onClick={() => handleAction(items[index].onSelectAction, items[index])}
          onMouseEnter={() => setSelectedIndex(index)}
          role="option"
          aria-selected={index === selectedIndex}
        >
          <span className="list-view-item-icon">
            <Icon name={item.icon} size={20} />
          </span>
          <div className="list-view-item-info">
            <div className="list-view-item-title">{item.title}</div>
            {item.description && <div className="list-view-item-description">{item.description}</div>}
          </div>
          {item.isInstalled && (
              <span className="list-view-item-installed-badge">Installed</span>
          )}
        </li>
      ))}
    </ul>
  );

  if (hasPreview) {
    return (
      <div className="list-view list-view-container">
        <div className="list-view-pane">
          {listContent}
        </div>
        <div className="item-preview-pane">
          <ItemPreview item={items[selectedIndex]} />
        </div>
      </div>
    );
  }

  return (
    <div className="list-view">
      {listContent}
    </div>
  );
};

export default ListView;