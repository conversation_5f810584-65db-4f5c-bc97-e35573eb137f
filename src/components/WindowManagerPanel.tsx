import React, { useState } from 'react';
import { useWindowManager } from '../hooks/useWindowManager';
import { useWindowLayoutManager, LayoutType } from '../hooks/useWindowLayoutManager';
import './WindowManagerPanel.css';

interface WindowManagerPanelProps {
    className?: string;
}

export const WindowManagerPanel: React.FC<WindowManagerPanelProps> = ({ className = '' }) => {
    const {
        windows,
        stats,
        loading: windowLoading,
        error: windowError,
        createWindow,
        closeWindow,
        focusWindow,
        minimizeWindow,
        maximizeWindow,
        resizeWindow,
        moveWindow,
        setAlwaysOnTop,
        closeAllPluginWindows,
        refreshWindows
    } = useWindowManager();

    const {
        layouts,
        stats: layoutStats,
        loading: layoutLoading,
        error: layoutError,
        setLayout,
        addWindowToLayout,
        arrangeWindows,
        focusNextWindow,
        focusPreviousWindow,
        setGridSize,
        refreshLayouts
    } = useWindowLayoutManager();

    // 新窗口创建表单状态
    const [newWindowForm, setNewWindowForm] = useState({
        url: 'https://example.com',
        title: '新窗口',
        width: 800,
        height: 600
    });

    // 窗口调整表单状态
    const [resizeForm, setResizeForm] = useState({
        windowId: '',
        width: 800,
        height: 600
    });

    // 窗口移动表单状态
    const [moveForm, setMoveForm] = useState({
        windowId: '',
        x: 100,
        y: 100
    });

    // 网格设置表单状态
    const [gridForm, setGridForm] = useState({
        rows: 2,
        cols: 2
    });

    // 当前选中的布局类型
    const [currentLayout, setCurrentLayout] = useState<LayoutType>('Free');

    // 创建新窗口
    const handleCreateWindow = async () => {
        const success = await createWindow(
            newWindowForm.url,
            newWindowForm.title,
            newWindowForm.width,
            newWindowForm.height
        );

        if (success) {
            console.log('窗口创建成功');
            // 重置表单
            setNewWindowForm({
                url: 'https://example.com',
                title: '新窗口',
                width: 800,
                height: 600
            });
        }
    };

    // 调整窗口大小
    const handleResizeWindow = async () => {
        if (!resizeForm.windowId) return;

        const success = await resizeWindow(
            resizeForm.windowId,
            resizeForm.width,
            resizeForm.height
        );

        if (success) {
            console.log('窗口大小调整成功');
        }
    };

    // 移动窗口
    const handleMoveWindow = async () => {
        if (!moveForm.windowId) return;

        const success = await moveWindow(
            moveForm.windowId,
            moveForm.x,
            moveForm.y
        );

        if (success) {
            console.log('窗口移动成功');
        }
    };

    // 设置布局
    const handleSetLayout = async (layoutType: LayoutType) => {
        const success = await setLayout(layoutType);
        if (success) {
            setCurrentLayout(layoutType);
            console.log(`布局设置为: ${layoutType}`);
        }
    };

    // 重新排列窗口
    const handleArrangeWindows = async () => {
        const success = await arrangeWindows(currentLayout);
        if (success) {
            console.log('窗口重新排列成功');
        }
    };

    // 设置网格大小
    const handleSetGridSize = async () => {
        const success = await setGridSize(gridForm.rows, gridForm.cols);
        if (success) {
            console.log(`网格大小设置为: ${gridForm.rows}x${gridForm.cols}`);
        }
    };

    if (windowLoading || layoutLoading) {
        return (
            <div className={`window-manager-panel ${className}`}>
                <div className="loading">
                    <div className="spinner"></div>
                    <span>加载窗口管理器...</span>
                </div>
            </div>
        );
    }

    return (
        <div className={`window-manager-panel ${className}`}>
            <div className="panel-header">
                <h2>窗口管理器</h2>
                <div className="refresh-buttons">
                    <button onClick={refreshWindows} className="refresh-btn">
                        刷新窗口
                    </button>
                    <button onClick={refreshLayouts} className="refresh-btn">
                        刷新布局
                    </button>
                </div>
            </div>

            {/* 错误信息 */}
            {(windowError || layoutError) && (
                <div className="error-section">
                    <h3>错误信息</h3>
                    {windowError && <p className="error">窗口管理器错误: {windowError}</p>}
                    {layoutError && <p className="error">布局管理器错误: {layoutError}</p>}
                </div>
            )}

            {/* 统计信息 */}
            <div className="stats-section">
                <h3>统计信息</h3>
                <div className="stats-grid">
                    <div className="stat-item">
                        <span className="stat-label">总窗口数:</span>
                        <span className="stat-value">{stats?.total_windows || 0}</span>
                    </div>
                    <div className="stat-item">
                        <span className="stat-label">活动窗口数:</span>
                        <span className="stat-value">{stats?.active_windows || 0}</span>
                    </div>
                    <div className="stat-item">
                        <span className="stat-label">插件窗口数:</span>
                        <span className="stat-value">{stats?.plugin_windows || 0}</span>
                    </div>
                    <div className="stat-item">
                        <span className="stat-label">当前布局:</span>
                        <span className="stat-value">{layoutStats?.active_layout || 'Free'}</span>
                    </div>
                </div>
            </div>

            {/* 窗口创建 */}
            <div className="create-window-section">
                <h3>创建新窗口</h3>
                <div className="form-group">
                    <label>
                        URL:
                        <input
                            type="text"
                            value={newWindowForm.url}
                            onChange={(e) => setNewWindowForm(prev => ({ ...prev, url: e.target.value }))}
                            placeholder="https://example.com"
                        />
                    </label>
                    <label>
                        标题:
                        <input
                            type="text"
                            value={newWindowForm.title}
                            onChange={(e) => setNewWindowForm(prev => ({ ...prev, title: e.target.value }))}
                            placeholder="窗口标题"
                        />
                    </label>
                    <label>
                        宽度:
                        <input
                            type="number"
                            value={newWindowForm.width}
                            onChange={(e) => setNewWindowForm(prev => ({ ...prev, width: parseInt(e.target.value) }))}
                            min="200"
                            max="1920"
                        />
                    </label>
                    <label>
                        高度:
                        <input
                            type="number"
                            value={newWindowForm.height}
                            onChange={(e) => setNewWindowForm(prev => ({ ...prev, height: parseInt(e.target.value) }))}
                            min="200"
                            max="1080"
                        />
                    </label>
                    <button onClick={handleCreateWindow} className="create-btn">
                        创建窗口
                    </button>
                </div>
            </div>

            {/* 布局管理 */}
            <div className="layout-section">
                <h3>布局管理</h3>
                <div className="layout-controls">
                    <div className="layout-buttons">
                        {(['Free', 'Tiled', 'Stacked', 'Grid', 'Tabbed'] as LayoutType[]).map(layoutType => (
                            <button
                                key={layoutType}
                                onClick={() => handleSetLayout(layoutType)}
                                className={`layout-btn ${currentLayout === layoutType ? 'active' : ''}`}
                            >
                                {layoutType}
                            </button>
                        ))}
                    </div>
                    <button onClick={handleArrangeWindows} className="arrange-btn">
                        重新排列窗口
                    </button>
                </div>

                {/* 网格设置 */}
                <div className="grid-controls">
                    <h4>网格设置</h4>
                    <div className="grid-form">
                        <label>
                            行数:
                            <input
                                type="number"
                                value={gridForm.rows}
                                onChange={(e) => setGridForm(prev => ({ ...prev, rows: parseInt(e.target.value) }))}
                                min="1"
                                max="6"
                            />
                        </label>
                        <label>
                            列数:
                            <input
                                type="number"
                                value={gridForm.cols}
                                onChange={(e) => setGridForm(prev => ({ ...prev, cols: parseInt(e.target.value) }))}
                                min="1"
                                max="6"
                            />
                        </label>
                        <button onClick={handleSetGridSize} className="grid-btn">
                            设置网格
                        </button>
                    </div>
                </div>

                {/* 焦点控制 */}
                <div className="focus-controls">
                    <h4>焦点控制</h4>
                    <div className="focus-buttons">
                        <button onClick={focusNextWindow} className="focus-btn">
                            下一个窗口
                        </button>
                        <button onClick={focusPreviousWindow} className="focus-btn">
                            上一个窗口
                        </button>
                    </div>
                </div>
            </div>

            {/* 窗口列表 */}
            <div className="windows-section">
                <h3>窗口列表</h3>
                {windows.length === 0 ? (
                    <p className="no-windows">暂无窗口</p>
                ) : (
                    <div className="windows-list">
                        {windows.map(window => (
                            <div key={window.id} className="window-item">
                                <div className="window-info">
                                    <h4>{window.title}</h4>
                                    <p className="window-url">{window.url}</p>
                                    <div className="window-state">
                                        <span className={`state-badge ${window.state?.is_focused ? 'focused' : ''}`}>
                                            {window.state?.is_focused ? '已聚焦' : '未聚焦'}
                                        </span>
                                        <span className={`state-badge ${window.state?.is_minimized ? 'minimized' : ''}`}>
                                            {window.state?.is_minimized ? '最小化' : '正常'}
                                        </span>
                                        <span className={`state-badge ${window.state?.is_maximized ? 'maximized' : ''}`}>
                                            {window.state?.is_maximized ? '最大化' : '正常'}
                                        </span>
                                    </div>
                                </div>
                                <div className="window-controls">
                                    <button onClick={() => focusWindow(window.id)} className="control-btn">
                                        聚焦
                                    </button>
                                    <button onClick={() => minimizeWindow(window.id)} className="control-btn">
                                        最小化
                                    </button>
                                    <button onClick={() => maximizeWindow(window.id)} className="control-btn">
                                        最大化
                                    </button>
                                    <button
                                        onClick={() => setAlwaysOnTop(window.id, !window.state?.is_always_on_top)}
                                        className={`control-btn ${window.state?.is_always_on_top ? 'active' : ''}`}
                                    >
                                        置顶
                                    </button>
                                    <button onClick={() => closeWindow(window.id)} className="control-btn danger">
                                        关闭
                                    </button>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>

            {/* 窗口操作 */}
            <div className="window-operations">
                <h3>窗口操作</h3>

                {/* 调整大小 */}
                <div className="operation-group">
                    <h4>调整窗口大小</h4>
                    <div className="form-group">
                        <select
                            value={resizeForm.windowId}
                            onChange={(e) => setResizeForm(prev => ({ ...prev, windowId: e.target.value }))}
                        >
                            <option value="">选择窗口</option>
                            {windows.map(window => (
                                <option key={window.id} value={window.id}>
                                    {window.title}
                                </option>
                            ))}
                        </select>
                        <input
                            type="number"
                            value={resizeForm.width}
                            onChange={(e) => setResizeForm(prev => ({ ...prev, width: parseInt(e.target.value) }))}
                            placeholder="宽度"
                            min="200"
                        />
                        <input
                            type="number"
                            value={resizeForm.height}
                            onChange={(e) => setResizeForm(prev => ({ ...prev, height: parseInt(e.target.value) }))}
                            placeholder="高度"
                            min="200"
                        />
                        <button onClick={handleResizeWindow} className="operation-btn">
                            调整大小
                        </button>
                    </div>
                </div>

                {/* 移动窗口 */}
                <div className="operation-group">
                    <h4>移动窗口</h4>
                    <div className="form-group">
                        <select
                            value={moveForm.windowId}
                            onChange={(e) => setMoveForm(prev => ({ ...prev, windowId: e.target.value }))}
                        >
                            <option value="">选择窗口</option>
                            {windows.map(window => (
                                <option key={window.id} value={window.id}>
                                    {window.title}
                                </option>
                            ))}
                        </select>
                        <input
                            type="number"
                            value={moveForm.x}
                            onChange={(e) => setMoveForm(prev => ({ ...prev, x: parseInt(e.target.value) }))}
                            placeholder="X坐标"
                        />
                        <input
                            type="number"
                            value={moveForm.y}
                            onChange={(e) => setMoveForm(prev => ({ ...prev, y: parseInt(e.target.value) }))}
                            placeholder="Y坐标"
                        />
                        <button onClick={handleMoveWindow} className="operation-btn">
                            移动窗口
                        </button>
                    </div>
                </div>

                {/* 批量操作 */}
                <div className="operation-group">
                    <h4>批量操作</h4>
                    <div className="batch-controls">
                        <button onClick={closeAllPluginWindows} className="batch-btn danger">
                            关闭所有插件窗口
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};