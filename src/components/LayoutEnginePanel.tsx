//! # 布局引擎管理面板组件
//! 
//! 提供高级布局算法、模板管理和动画配置的用户界面

import React, { useState, useCallback, useEffect } from 'react';
import { useLayoutEngine, PresetTemplates, createLayoutTemplate } from '../hooks/useLayoutEngine';
import {
    LayoutTemplate,
    LayoutResult,
    WindowBounds,
    EasingFunction,
    SplitDirection,
    LayoutTemplateRequest
} from '../types/layoutEngine';
import './LayoutEnginePanel.css';

export const LayoutEnginePanel: React.FC = () => {
    const {
        // 状态
        screenInfo,
        constraints,
        animation,
        templates,
        animationState,
        performanceStats,
        isLoading,
        error,
        clearError,

        // 操作
        updateScreenInfo,
        setConstraints,
        setAnimation,
        createTemplate: createLayoutTemplateAction,
        getTemplates,
        getPopularTemplates,
        applyAutoTileLayout,
        applyTemplate,
        previewLayout,
        optimizeLayout,
        recommendLayout,
        startLayoutAnimation,
        stopAnimation,
        getPerformanceStats,
        cleanupPerformanceCache
    } = useLayoutEngine();

    // 本地状态
    const [activeTab, setActiveTab] = useState<'layouts' | 'templates' | 'animation' | 'performance'>('layouts');
    const [selectedTemplate, setSelectedTemplate] = useState<string>('');
    const [mockWindowIds, setMockWindowIds] = useState<string[]>(['window-1', 'window-2', 'window-3']);
    const [layoutPreview, setLayoutPreview] = useState<Record<string, WindowBounds> | null>(null);
    const [showCreateTemplate, setShowCreateTemplate] = useState(false);
    const [newTemplate, setNewTemplate] = useState<Partial<LayoutTemplateRequest>>({
        id: '',
        name: '',
        description: '',
        layout_type: 'Tiled',
        config: {
            primary_ratio: 0.6,
            secondary_ratio: 0.4,
            direction: 'Auto',
            auto_balance: true
        }
    });

    // 测试布局应用
    const handleApplyLayout = useCallback(async (layoutType: 'auto' | 'template', templateId?: string) => {
        try {
            let result: LayoutResult;

            if (layoutType === 'auto') {
                result = await applyAutoTileLayout(mockWindowIds);
            } else if (templateId) {
                result = await applyTemplate(templateId, mockWindowIds);
            } else {
                return;
            }

            if (result.success) {
                console.log('布局应用成功:', result);
                // 这里可以触发实际的窗口布局更新
            }
        } catch (err) {
            console.error('布局应用失败:', err);
        }
    }, [mockWindowIds, applyAutoTileLayout, applyTemplate]);

    // 预览布局
    const handlePreviewLayout = useCallback(async (templateId: string) => {
        try {
            const result = await previewLayout(templateId, mockWindowIds);
            if (result.success) {
                setLayoutPreview(result.layout);
            }
        } catch (err) {
            console.error('布局预览失败:', err);
        }
    }, [mockWindowIds, previewLayout]);

    // 创建模板
    const handleCreateTemplate = useCallback(async () => {
        try {
            if (!newTemplate.id || !newTemplate.name || !newTemplate.config) return;

            const template = createLayoutTemplate(
                newTemplate.id,
                newTemplate.name,
                newTemplate.description || '',
                newTemplate.config
            );

            await createLayoutTemplateAction(template);
            setShowCreateTemplate(false);
            setNewTemplate({
                id: '',
                name: '',
                description: '',
                layout_type: 'Tiled',
                config: {
                    primary_ratio: 0.6,
                    secondary_ratio: 0.4,
                    direction: 'Auto',
                    auto_balance: true
                }
            });
        } catch (err) {
            console.error('模板创建失败:', err);
        }
    }, [newTemplate, createLayoutTemplateAction]);

    // 获取推荐布局
    const handleGetRecommendation = useCallback(async () => {
        try {
            const recommendation = await recommendLayout(mockWindowIds);
            setSelectedTemplate(recommendation);
            await handlePreviewLayout(recommendation);
        } catch (err) {
            console.error('获取推荐失败:', err);
        }
    }, [mockWindowIds, recommendLayout, handlePreviewLayout]);

    // 优化当前布局
    const handleOptimizeLayout = useCallback(async () => {
        if (!layoutPreview) return;

        try {
            const result = await optimizeLayout(layoutPreview);
            if (result.success) {
                setLayoutPreview(result.layout);
            }
        } catch (err) {
            console.error('布局优化失败:', err);
        }
    }, [layoutPreview, optimizeLayout]);

    // 更新约束
    const handleUpdateConstraints = useCallback(async (field: string, value: number) => {
        if (!constraints) return;

        const updatedConstraints = { ...constraints, [field]: value };
        await setConstraints(updatedConstraints);
    }, [constraints, setConstraints]);

    // 更新动画配置
    const handleUpdateAnimation = useCallback(async (field: string, value: any) => {
        if (!animation) return;

        const updatedAnimation = { ...animation, [field]: value };
        await setAnimation(updatedAnimation);
    }, [animation, setAnimation]);

    // 组件挂载时获取数据
    useEffect(() => {
        getTemplates();
        getPerformanceStats();
    }, [getTemplates, getPerformanceStats]);

    return (
        <div className="layout-engine-panel">
            <div className="panel-header">
                <h2>🏗️ 布局引擎</h2>
                <p>高级多窗口布局管理与优化</p>
                {error && (
                    <div className="error-banner">
                        <span>{error}</span>
                        <button onClick={clearError}>✕</button>
                    </div>
                )}
            </div>

            {/* 标签页导航 */}
            <div className="tab-navigation">
                {[
                    { key: 'layouts', label: '布局管理', icon: '📐' },
                    { key: 'templates', label: '模板管理', icon: '📋' },
                    { key: 'animation', label: '动画配置', icon: '🎬' },
                    { key: 'performance', label: '性能统计', icon: '📊' }
                ].map(tab => (
                    <button
                        key={tab.key}
                        className={`tab-button ${activeTab === tab.key ? 'active' : ''}`}
                        onClick={() => setActiveTab(tab.key as any)}
                    >
                        <span className="tab-icon">{tab.icon}</span>
                        <span className="tab-label">{tab.label}</span>
                    </button>
                ))}
            </div>

            {/* 布局管理标签页 */}
            {activeTab === 'layouts' && (
                <div className="tab-content">
                    <div className="section">
                        <h3>智能布局</h3>
                        <div className="layout-controls">
                            <div className="control-group">
                                <label>测试窗口数量:</label>
                                <input
                                    type="number"
                                    min="1"
                                    max="20"
                                    value={mockWindowIds.length}
                                    onChange={(e) => {
                                        const count = parseInt(e.target.value) || 1;
                                        setMockWindowIds(Array.from({ length: count }, (_, i) => `window-${i + 1}`));
                                    }}
                                />
                            </div>
                            <button
                                className="primary-button"
                                onClick={() => handleApplyLayout('auto')}
                                disabled={isLoading}
                            >
                                🤖 智能平铺布局
                            </button>
                            <button
                                className="secondary-button"
                                onClick={handleGetRecommendation}
                                disabled={isLoading}
                            >
                                💡 获取推荐
                            </button>
                        </div>
                    </div>

                    <div className="section">
                        <h3>布局预览</h3>
                        <div className="layout-preview">
                            {layoutPreview ? (
                                <div className="preview-container">
                                    <div className="preview-screen">
                                        {Object.entries(layoutPreview).map(([windowId, bounds]) => (
                                            <div
                                                key={windowId}
                                                className="preview-window"
                                                style={{
                                                    left: `${(bounds.x / (screenInfo?.workarea.width || 1920)) * 100}%`,
                                                    top: `${(bounds.y / (screenInfo?.workarea.height || 1080)) * 100}%`,
                                                    width: `${(bounds.width / (screenInfo?.workarea.width || 1920)) * 100}%`,
                                                    height: `${(bounds.height / (screenInfo?.workarea.height || 1080)) * 100}%`
                                                }}
                                            >
                                                {windowId}
                                            </div>
                                        ))}
                                    </div>
                                    <div className="preview-actions">
                                        <button
                                            className="optimize-button"
                                            onClick={handleOptimizeLayout}
                                        >
                                            ⚡ 优化布局
                                        </button>
                                    </div>
                                </div>
                            ) : (
                                <div className="no-preview">
                                    <p>选择模板或应用布局来查看预览</p>
                                </div>
                            )}
                        </div>
                    </div>

                    <div className="section">
                        <h3>布局约束</h3>
                        {constraints && (
                            <div className="constraints-grid">
                                <div className="constraint-item">
                                    <label>最小窗口宽度:</label>
                                    <input
                                        type="number"
                                        value={constraints.min_window_width}
                                        onChange={(e) => handleUpdateConstraints('min_window_width', parseInt(e.target.value))}
                                    />
                                </div>
                                <div className="constraint-item">
                                    <label>最小窗口高度:</label>
                                    <input
                                        type="number"
                                        value={constraints.min_window_height}
                                        onChange={(e) => handleUpdateConstraints('min_window_height', parseInt(e.target.value))}
                                    />
                                </div>
                                <div className="constraint-item">
                                    <label>窗口间距:</label>
                                    <input
                                        type="number"
                                        value={constraints.window_gap}
                                        onChange={(e) => handleUpdateConstraints('window_gap', parseInt(e.target.value))}
                                    />
                                </div>
                                <div className="constraint-item">
                                    <label>屏幕边距:</label>
                                    <input
                                        type="number"
                                        value={constraints.screen_margin}
                                        onChange={(e) => handleUpdateConstraints('screen_margin', parseInt(e.target.value))}
                                    />
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            )}

            {/* 模板管理标签页 */}
            {activeTab === 'templates' && (
                <div className="tab-content">
                    <div className="section">
                        <div className="section-header">
                            <h3>布局模板</h3>
                            <button
                                className="create-button"
                                onClick={() => setShowCreateTemplate(true)}
                            >
                                ➕ 创建模板
                            </button>
                        </div>

                        <div className="templates-grid">
                            {templates.map(template => (
                                <div
                                    key={template.id}
                                    className={`template-card ${selectedTemplate === template.id ? 'selected' : ''}`}
                                    onClick={() => {
                                        setSelectedTemplate(template.id);
                                        handlePreviewLayout(template.id);
                                    }}
                                >
                                    <div className="template-header">
                                        <h4>{template.name}</h4>
                                        <span className="usage-count">{template.usage_count} 次使用</span>
                                    </div>
                                    <p className="template-description">{template.description}</p>
                                    <div className="template-config">
                                        <span>主比例: {(template.config.primary_ratio * 100).toFixed(0)}%</span>
                                        <span>方向: {template.config.direction}</span>
                                    </div>
                                    <div className="template-actions">
                                        <button
                                            className="preview-button"
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                handlePreviewLayout(template.id);
                                            }}
                                        >
                                            👁️ 预览
                                        </button>
                                        <button
                                            className="apply-button"
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                handleApplyLayout('template', template.id);
                                            }}
                                        >
                                            ✓ 应用
                                        </button>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* 创建模板对话框 */}
                    {showCreateTemplate && (
                        <div className="modal-overlay">
                            <div className="modal">
                                <div className="modal-header">
                                    <h3>创建布局模板</h3>
                                    <button
                                        className="close-button"
                                        onClick={() => setShowCreateTemplate(false)}
                                    >
                                        ✕
                                    </button>
                                </div>
                                <div className="modal-content">
                                    <div className="form-group">
                                        <label>模板ID:</label>
                                        <input
                                            type="text"
                                            value={newTemplate.id || ''}
                                            onChange={(e) => setNewTemplate(prev => ({ ...prev, id: e.target.value }))}
                                            placeholder="例如: my-custom-layout"
                                        />
                                    </div>
                                    <div className="form-group">
                                        <label>模板名称:</label>
                                        <input
                                            type="text"
                                            value={newTemplate.name || ''}
                                            onChange={(e) => setNewTemplate(prev => ({ ...prev, name: e.target.value }))}
                                            placeholder="例如: 我的自定义布局"
                                        />
                                    </div>
                                    <div className="form-group">
                                        <label>描述:</label>
                                        <textarea
                                            value={newTemplate.description || ''}
                                            onChange={(e) => setNewTemplate(prev => ({ ...prev, description: e.target.value }))}
                                            placeholder="描述这个布局模板的特点..."
                                        />
                                    </div>
                                    <div className="form-group">
                                        <label>主窗口比例:</label>
                                        <input
                                            type="range"
                                            min="0.1"
                                            max="0.9"
                                            step="0.1"
                                            value={newTemplate.config?.primary_ratio || 0.6}
                                            onChange={(e) => setNewTemplate(prev => ({
                                                ...prev,
                                                config: { ...prev.config!, primary_ratio: parseFloat(e.target.value) }
                                            }))}
                                        />
                                        <span>{((newTemplate.config?.primary_ratio || 0.6) * 100).toFixed(0)}%</span>
                                    </div>
                                    <div className="form-group">
                                        <label>分割方向:</label>
                                        <select
                                            value={newTemplate.config?.direction || 'Auto'}
                                            onChange={(e) => setNewTemplate(prev => ({
                                                ...prev,
                                                config: { ...prev.config!, direction: e.target.value as SplitDirection }
                                            }))}
                                        >
                                            <option value="Auto">自动</option>
                                            <option value="Horizontal">水平</option>
                                            <option value="Vertical">垂直</option>
                                        </select>
                                    </div>
                                </div>
                                <div className="modal-actions">
                                    <button
                                        className="secondary-button"
                                        onClick={() => setShowCreateTemplate(false)}
                                    >
                                        取消
                                    </button>
                                    <button
                                        className="primary-button"
                                        onClick={handleCreateTemplate}
                                        disabled={!newTemplate.id || !newTemplate.name}
                                    >
                                        创建
                                    </button>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            )}

            {/* 动画配置标签页 */}
            {activeTab === 'animation' && (
                <div className="tab-content">
                    <div className="section">
                        <h3>布局动画配置</h3>
                        {animation && (
                            <div className="animation-config">
                                <div className="config-item">
                                    <label>
                                        <input
                                            type="checkbox"
                                            checked={animation.enabled}
                                            onChange={(e) => handleUpdateAnimation('enabled', e.target.checked)}
                                        />
                                        启用布局动画
                                    </label>
                                </div>
                                <div className="config-item">
                                    <label>动画时长 (毫秒):</label>
                                    <input
                                        type="range"
                                        min="100"
                                        max="1000"
                                        step="50"
                                        value={animation.duration_ms}
                                        onChange={(e) => handleUpdateAnimation('duration_ms', parseInt(e.target.value))}
                                    />
                                    <span>{animation.duration_ms}ms</span>
                                </div>
                                <div className="config-item">
                                    <label>缓动函数:</label>
                                    <select
                                        value={animation.easing}
                                        onChange={(e) => handleUpdateAnimation('easing', e.target.value as EasingFunction)}
                                    >
                                        <option value="Linear">线性</option>
                                        <option value="EaseInOut">缓入缓出</option>
                                        <option value="EaseIn">缓入</option>
                                        <option value="EaseOut">缓出</option>
                                        <option value="Bounce">弹跳</option>
                                        <option value="Elastic">弹性</option>
                                    </select>
                                </div>
                            </div>
                        )}
                    </div>

                    <div className="section">
                        <h3>动画状态</h3>
                        {animationState ? (
                            <div className="animation-status">
                                <div className="status-item">
                                    <label>动画进度:</label>
                                    <div className="progress-bar">
                                        <div
                                            className="progress-fill"
                                            style={{ width: `${animationState.progress * 100}%` }}
                                        />
                                    </div>
                                    <span>{(animationState.progress * 100).toFixed(1)}%</span>
                                </div>
                                <button className="stop-button" onClick={stopAnimation}>
                                    ⏹️ 停止动画
                                </button>
                            </div>
                        ) : (
                            <p>当前没有进行中的动画</p>
                        )}
                    </div>
                </div>
            )}

            {/* 性能统计标签页 */}
            {activeTab === 'performance' && (
                <div className="tab-content">
                    <div className="section">
                        <div className="section-header">
                            <h3>性能统计</h3>
                            <div className="stats-actions">
                                <button onClick={getPerformanceStats}>🔄 刷新</button>
                                <button onClick={cleanupPerformanceCache}>🗑️ 清理缓存</button>
                            </div>
                        </div>

                        {Object.keys(performanceStats).length > 0 ? (
                            <div className="stats-grid">
                                {Object.entries(performanceStats).map(([operation, stats]: [string, any]) => (
                                    <div key={operation} className="stat-card">
                                        <h4>{operation}</h4>
                                        <div className="stat-items">
                                            <div className="stat-item">
                                                <span className="stat-label">计算时间:</span>
                                                <span className="stat-value">{stats.calculation_time_ms}ms</span>
                                            </div>
                                            <div className="stat-item">
                                                <span className="stat-label">处理窗口:</span>
                                                <span className="stat-value">{stats.windows_processed}</span>
                                            </div>
                                            <div className="stat-item">
                                                <span className="stat-label">空间利用率:</span>
                                                <span className="stat-value">{(stats.layout_efficiency * 100).toFixed(1)}%</span>
                                            </div>
                                            <div className="stat-item">
                                                <span className="stat-label">布局平衡度:</span>
                                                <span className="stat-value">{(stats.balance_score * 100).toFixed(1)}%</span>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <p>暂无性能统计数据</p>
                        )}
                    </div>

                    <div className="section">
                        <h3>系统信息</h3>
                        {screenInfo && (
                            <div className="system-info">
                                <div className="info-item">
                                    <span className="info-label">屏幕尺寸:</span>
                                    <span className="info-value">{screenInfo.width} × {screenInfo.height}</span>
                                </div>
                                <div className="info-item">
                                    <span className="info-label">缩放比例:</span>
                                    <span className="info-value">{screenInfo.scale_factor}x</span>
                                </div>
                                <div className="info-item">
                                    <span className="info-label">工作区域:</span>
                                    <span className="info-value">
                                        {screenInfo.workarea.width} × {screenInfo.workarea.height}
                                    </span>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            )}

            {isLoading && (
                <div className="loading-overlay">
                    <div className="loading-spinner">⚙️ 处理中...</div>
                </div>
            )}
        </div>
    );
};

export default LayoutEnginePanel;