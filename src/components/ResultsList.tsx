import React from 'react';
import { SearchResultItem } from '../core/types';
import ResultItem from './ResultItem';

interface ResultsListProps {
  results: SearchResultItem[];
  onResultSelect: (item: SearchResultItem) => void;
  selectedIndex: number;
}

const ResultsList: React.FC<ResultsListProps> = ({ results, onResultSelect, selectedIndex }) => {
  if (results.length === 0) return null;

  const groupedResults = results.reduce((acc, item) => {
    const groupKey = item.type; // 'action' or 'plugin'
    if (!acc[groupKey]) {
      acc[groupKey] = [];
    }
    acc[groupKey].push(item);
    return acc;
  }, {} as Record<string, SearchResultItem[]>);

  const groupOrder: (keyof typeof groupedResults)[] = ['action', 'plugin'];

  const getGroupHeader = (key: string) => {
    switch(key) {
        case 'action': return 'Actions';
        case 'plugin': return 'Plugins & Apps';
        default: return 'Results';
    }
  };

  let cumulativeIndex = 0;

  return (
    <div className="results-container">
      {groupOrder.map(groupKey => {
        const items = groupedResults[groupKey];
        if (!items || items.length === 0) return null;

        const groupItems = items.map((item) => {
            const currentIndex = cumulativeIndex;
            cumulativeIndex++;
            return (
              <ResultItem 
                key={`${item.type}-${item.type === 'action' ? item.action.id : item.plugin.id}`} 
                item={item} 
                onSelect={onResultSelect} 
                isSelected={currentIndex === selectedIndex} 
              />
            );
        });

        return (
          <div key={groupKey} className="result-group">
            <h4 className="result-group-header">{getGroupHeader(groupKey)}</h4>
            <ul className="results-list" role="listbox">
              {groupItems}
            </ul>
          </div>
        );
      })}
    </div>
  );
};

export default ResultsList;
