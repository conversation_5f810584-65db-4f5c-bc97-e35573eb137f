import React from 'react';

interface AIResponseDisplayProps {
  response: string | null;
  error: string | null;
  isLoading: boolean;
}

const AIResponseDisplay: React.FC<AIResponseDisplayProps> = ({ response, error, isLoading }) => {
  if (isLoading) return <div className="ai-response-display loading">Loading AI response...</div>;
  if (error) return <div className="ai-response-display error">Error: {error}</div>;
  if (response) return <div className="ai-response-display response" dangerouslySetInnerHTML={{ __html: response.replace(/\n/g, '<br />') }}></div>;
  return null;
};

export default AIResponseDisplay;