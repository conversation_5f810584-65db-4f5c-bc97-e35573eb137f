import React from 'react';
import { File as FileIcon, X } from 'lucide-react';

interface AttachmentPreviewProps {
  file: File;
  onRemove: () => void;
}

const AttachmentPreview: React.FC<AttachmentPreviewProps> = ({ file, onRemove }) => {
  const formatBytes = (bytes: number, decimals = 2): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  }

  return (
    <div className="attachment-preview" role="region" aria-label="Attachment preview">
      <FileIcon className="attachment-icon" size={20} />
      <div className="attachment-details">
        <span className="attachment-name" title={file.name}>{file.name}</span>
        <span className="attachment-size">{formatBytes(file.size)}</span>
      </div>
      <button onClick={onRemove} className="remove-attachment-button" aria-label={`Remove ${file.name} attachment`}>
        <X size={18} />
      </button>
    </div>
  );
};

export default AttachmentPreview;