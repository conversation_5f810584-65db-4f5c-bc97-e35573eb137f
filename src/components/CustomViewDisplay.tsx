import React, { useRef } from 'react';
import { PluginMessage } from '../core/types';

interface CustomViewDisplayProps {
  url: string;
  initialPayload: any;
}

const CustomViewDisplay: React.FC<CustomViewDisplayProps> = ({ url, initialPayload }) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);

  const handleIframeLoad = () => {
    if (iframeRef.current && iframeRef.current.contentWindow) {
      const message: PluginMessage = {
        type: 'init-plugin',
        payload: initialPayload
      };
      // In a production app with a known domain, you'd replace '*' with that origin
      // for better security. e.g., 'https://my-plugin-host.com'
      iframeRef.current.contentWindow.postMessage(message, '*');
    }
  };

  return (
    <div className="custom-view-container" role="document" aria-label="Custom Plugin View">
      <iframe 
        ref={iframeRef}
        src={url}
        onLoad={handleIframeLoad}
        className="custom-view-iframe"
        title="Custom Plugin View"
        sandbox="allow-scripts allow-same-origin allow-popups allow-forms" // Security sandbox for the iframe
        aria-label={`Content from ${url}`}
      />
    </div>
  );
};

export default CustomViewDisplay;