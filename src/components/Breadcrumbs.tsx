import React from 'react';

interface Breadcrumb {
  title: string;
}

interface BreadcrumbsProps {
  history: Breadcrumb[];
  onNavigate: (index: number) => void;
}

const Breadcrumbs: React.FC<BreadcrumbsProps> = ({ history, onNavigate }) => {
  if (history.length <= 1) {
    return null;
  }

  return (
    <nav className="breadcrumbs" aria-label="breadcrumb">
      {history.map((crumb, index) => {
        const isCurrent = index === history.length - 1;
        return (
          <React.Fragment key={index}>
            {isCurrent ? (
              <span className="breadcrumb-current" aria-current="page">
                {crumb.title}
              </span>
            ) : (
              <a
                href="#"
                className="breadcrumb-item"
                onClick={(e) => {
                  e.preventDefault();
                  onNavigate(index);
                }}
              >
                {crumb.title}
              </a>
            )}
            {!isCurrent && <span className="breadcrumb-separator">/</span>}
          </React.Fragment>
        );
      })}
    </nav>
  );
};

export default Breadcrumbs;
