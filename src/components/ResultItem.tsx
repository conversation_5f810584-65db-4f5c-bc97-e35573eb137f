import React from 'react';
import { SearchResultItem } from '../core/types';
import Icon from './Icon';

interface ResultItemProps {
  item: SearchResultItem;
  onSelect: (item: SearchResultItem) => void;
  isSelected: boolean;
}

const ResultItem: React.FC<ResultItemProps> = ({ item, onSelect, isSelected }) => {
  const handleClick = () => onSelect(item);

  const renderContent = () => {
    if (item.type === 'plugin') {
      const { plugin } = item;
      return (
        <>
          <span className="result-item-icon">
            <Icon name={plugin.icon} />
          </span>
          <div className="result-item-text">
            <span className="result-item-name">{plugin.name}</span>
            <span className="result-item-description">{plugin.description}</span>
          </div>
          <span className={`result-item-badge ${plugin.mode === 'view' ? 'view' : 'agent'}`}>
            {plugin.mode}
          </span>
        </>
      );
    }
    
    if (item.type === 'action') {
        const { action, plugin } = item;
        return (
            <>
                <span className="result-item-icon">
                    <Icon name={plugin.icon} />
                </span>
                <div className="result-item-text">
                    <span className="result-item-name">{action.name}</span>
                    <span className="result-item-description">
                      {action.description || `Action from ${plugin.name}`}
                    </span>
                </div>
                <span className="result-item-badge agent">Action</span>
            </>
        );
    }
    
    return null;
  };

  return (
    <li
      className={`result-item ${isSelected ? 'selected' : ''}`}
      onClick={handleClick}
      role="option"
      aria-selected={isSelected}
    >
      {renderContent()}
    </li>
  );
};

export default ResultItem;
