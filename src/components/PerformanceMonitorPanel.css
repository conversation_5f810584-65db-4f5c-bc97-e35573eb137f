.performance-monitor-panel {
    background: #1f2937;
    border-radius: 8px;
    padding: 20px;
    color: #f9fafb;
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
    max-height: 600px;
    overflow-y: auto;
}

.performance-monitor-panel.error {
    border: 1px solid #ef4444;
}

/* Header */
.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #374151;
}

.panel-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #f9fafb;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

.status-indicator {
    font-size: 14px;
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.status-indicator.active {
    background: rgba(16, 185, 129, 0.1);
    color: #10b981;
    border: 1px solid #10b981;
}

.status-indicator.inactive {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    border: 1px solid #ef4444;
}

.start-btn,
.stop-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.start-btn {
    background: #10b981;
    color: white;
}

.start-btn:hover {
    background: #059669;
}

.stop-btn {
    background: #ef4444;
    color: white;
}

.stop-btn:hover {
    background: #dc2626;
}

.start-btn:disabled,
.stop-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Tabs */
.panel-tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 1px solid #374151;
}

.panel-tabs button {
    background: none;
    border: none;
    padding: 12px 16px;
    color: #9ca3af;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
    border-bottom: 2px solid transparent;
}

.panel-tabs button:hover {
    color: #f9fafb;
    background: rgba(55, 65, 81, 0.5);
}

.panel-tabs button.active {
    color: #3b82f6;
    border-bottom-color: #3b82f6;
}

/* Content */
.panel-content {
    min-height: 300px;
}

/* Overview Tab */
.overview-tab {
    width: 100%;
}

.system-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.metric-card {
    background: #374151;
    border-radius: 8px;
    padding: 16px;
    text-align: center;
    transition: transform 0.2s ease;
}

.metric-card:hover {
    transform: translateY(-2px);
}

.metric-card h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: #9ca3af;
    font-weight: 500;
}

.metric-value {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 4px;
}

.metric-detail {
    font-size: 12px;
    color: #6b7280;
}

/* Charts Tab */
.charts-tab {
    width: 100%;
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.metric-chart {
    background: #374151;
    border-radius: 8px;
    padding: 16px;
}

.metric-chart h4 {
    margin: 0 0 12px 0;
    font-size: 16px;
    color: #f9fafb;
    font-weight: 600;
}

.chart-container {
    width: 100%;
    height: 200px;
    margin-bottom: 8px;
}

.chart-svg {
    width: 100%;
    height: 100%;
    background: #1f2937;
    border-radius: 4px;
}

.chart-point {
    transition: r 0.2s ease;
    cursor: pointer;
}

.chart-point:hover {
    r: 5;
}

.chart-footer {
    text-align: center;
}

.current-value {
    font-size: 16px;
    font-weight: 600;
}

/* Stats Tab */
.stats-tab {
    width: 100%;
}

.app-stats {
    background: #374151;
    border-radius: 8px;
    padding: 20px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #4b5563;
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    color: #9ca3af;
    font-weight: 500;
}

.stat-value {
    color: #f9fafb;
    font-weight: 600;
}

/* Tools Tab */
.tools-tab {
    width: 100%;
}

.tool-section {
    background: #374151;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 16px;
}

.tool-section h4 {
    margin: 0 0 16px 0;
    font-size: 16px;
    color: #f9fafb;
    font-weight: 600;
}

.tool-buttons {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.tool-buttons button {
    padding: 8px 16px;
    background: #4b5563;
    color: #f9fafb;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.tool-buttons button:hover {
    background: #6b7280;
}

.tool-buttons button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.benchmark-result {
    margin-top: 16px;
    padding: 16px;
    background: #1f2937;
    border-radius: 6px;
    border: 1px solid #4b5563;
}

.benchmark-result h5 {
    margin: 0 0 12px 0;
    color: #f9fafb;
    font-weight: 600;
}

.benchmark-stats {
    font-family: 'SF Mono', Monaco, Consolas, monospace;
    font-size: 13px;
    line-height: 1.5;
}

.benchmark-stats>div {
    padding: 4px 0;
    color: #d1d5db;
}

/* Error Message */
.error-message {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid #ef4444;
    border-radius: 6px;
    color: #ef4444;
}

.error-message button {
    padding: 6px 12px;
    background: #ef4444;
    color: white;
    border: none;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s ease;
}

.error-message button:hover {
    background: #dc2626;
}

/* Responsive Design */
@media (max-width: 768px) {
    .system-overview {
        grid-template-columns: 1fr;
    }

    .charts-grid {
        grid-template-columns: 1fr;
    }

    .tool-buttons {
        flex-direction: column;
    }

    .header-controls {
        flex-direction: column;
        gap: 8px;
        align-items: flex-end;
    }

    .panel-tabs {
        flex-wrap: wrap;
    }
}

/* Scrollbar Styling */
.performance-monitor-panel::-webkit-scrollbar {
    width: 6px;
}

.performance-monitor-panel::-webkit-scrollbar-track {
    background: #374151;
    border-radius: 3px;
}

.performance-monitor-panel::-webkit-scrollbar-thumb {
    background: #6b7280;
    border-radius: 3px;
}

.performance-monitor-panel::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

/* Animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.metric-card,
.tool-section {
    animation: fadeIn 0.3s ease-out;
}

/* Loading States */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #3b82f6;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}