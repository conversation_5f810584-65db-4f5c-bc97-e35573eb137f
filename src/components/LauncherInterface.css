/* 启动器界面样式 - 简化版 */
.launcher-interface {
    width: 100%;
    max-width: 1000px;
    background: rgba(40, 40, 40, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.08);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
    overflow: hidden;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* 搜索输入容器 */
.launcher-search-container {
    padding: 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

/* 主搜索输入框 */
.launcher-search-input {
    width: 100%;
    height: 60px;
    padding: 0 20px;
    font-size: 18px;
    font-weight: 400;
    color: #ffffff;
    background: transparent;
    border: none;
    border-radius: 0;
    outline: none;
    transition: all 0.2s ease;
}

.launcher-search-input:focus {
    background: rgba(255, 255, 255, 0.02);
}

.launcher-search-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
    font-weight: 300;
}

/* 搜索结果容器 */
.launcher-results {
    max-height: 400px;
    overflow-y: auto;
}

/* 结果项 */
.launcher-result-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    cursor: pointer;
    transition: all 0.15s ease;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    min-height: 64px;
}

.launcher-result-item:last-child {
    border-bottom: none;
}

.launcher-result-item:hover,
.launcher-result-item.selected {
    background: rgba(0, 122, 255, 0.15);
}

/* 结果图标 */
.result-icon {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    margin-right: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.08);
    border-radius: 8px;
    font-size: 20px;
}

/* 结果内容 */
.result-content {
    flex: 1;
    min-width: 0;
}

.result-title {
    font-size: 16px;
    font-weight: 500;
    color: #ffffff;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.result-description {
    font-size: 13px;
    color: rgba(255, 255, 255, 0.6);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 结果徽章 */
.result-badge {
    flex-shrink: 0;
    margin-left: 16px;
}

.mode-badge,
.action-badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.7);
}

.mode-badge.view {
    background: rgba(52, 199, 89, 0.2);
    color: #34C759;
}

.mode-badge.list {
    background: rgba(255, 149, 0, 0.2);
    color: #FF9500;
}

.action-badge {
    background: rgba(88, 86, 214, 0.2);
    color: #5856D6;
}

/* 错误显示 */
.launcher-error {
    padding: 12px 20px;
    background: rgba(255, 59, 48, 0.15);
    border-top: 1px solid rgba(255, 59, 48, 0.3);
    color: #FF3B30;
    font-size: 14px;
    text-align: center;
}

/* 滚动条样式 */
.launcher-results::-webkit-scrollbar {
    width: 6px;
}

.launcher-results::-webkit-scrollbar-track {
    background: transparent;
}

.launcher-results::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
}

.launcher-results::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* 进入动画 */
.launcher-interface {
    animation: launcherAppear 0.2s ease-out;
}

@keyframes launcherAppear {
    from {
        opacity: 0;
        transform: translateY(-8px) scale(0.98);
    }

    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 结果项进入动画 */
.launcher-result-item {
    animation: resultSlideIn 0.15s ease-out backwards;
}

.launcher-result-item:nth-child(1) {
    animation-delay: 0.02s;
}

.launcher-result-item:nth-child(2) {
    animation-delay: 0.04s;
}

.launcher-result-item:nth-child(3) {
    animation-delay: 0.06s;
}

.launcher-result-item:nth-child(4) {
    animation-delay: 0.08s;
}

.launcher-result-item:nth-child(5) {
    animation-delay: 0.10s;
}

.launcher-result-item:nth-child(6) {
    animation-delay: 0.12s;
}

@keyframes resultSlideIn {
    from {
        opacity: 0;
        transform: translateX(-8px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 响应式设计 */
@media (max-width: 480px) {
    .launcher-interface {
        margin: 0 8px;
        border-radius: 8px;
    }

    .launcher-search-container {
        padding: 12px 16px;
    }

    .launcher-search-input {
        height: 44px;
        font-size: 16px;
    }

    .launcher-result-item {
        padding: 10px 16px;
        min-height: 56px;
    }

    .result-icon {
        width: 32px;
        height: 32px;
        margin-right: 12px;
        font-size: 16px;
    }

    .result-title {
        font-size: 15px;
    }

    .result-description {
        font-size: 12px;
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {

    .launcher-interface,
    .launcher-result-item {
        animation: none;
    }
}