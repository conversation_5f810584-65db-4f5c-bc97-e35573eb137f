import { ListPlugin, ListPluginRunner, ListItem } from '../../core/types';

const run: ListPluginRunner = async () => {
    const items: ListItem[] = [
        { id: 'snippet-react', title: 'React Functional Component', description: 'A basic React functional component snippet.', preview: { type: 'markdown', content: "```javascript\nimport React from 'react';\n\nconst MyComponent = () => {\n  return <div>My Component</div>;\n};\n\nexport default MyComponent;\n```" }, onSelectAction: { type: 'copy', payload: "import React from 'react';\n\nconst MyComponent = () => {\n  return <div>My Component</div>;\n};\n\nexport default MyComponent;" } , icon: 'Code' },
        { id: 'snippet-css', title: 'CSS Flexbox Center', description: 'A common CSS snippet to center content.', preview: { type: 'markdown', content: "```css\n.container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n```" }, onSelectAction: { type: 'copy', payload: ".container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n" }, icon: 'Code' }
    ];
    return `LIST_MODE_RESULTS::${JSON.stringify({ items })}`;
};

export const snippetsPlugin: ListPlugin = {
    id: 'list-snippets',
    name: 'Snippets',
    icon: 'Code',
    mode: 'list',
    keyword: 'snippets',
    description: 'Search and copy your snippets.',
    run
};
