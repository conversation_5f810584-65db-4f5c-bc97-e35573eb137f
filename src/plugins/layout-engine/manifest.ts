//! # 布局引擎插件清单
//! 
//! 提供高级多窗口布局管理和优化功能的插件

import { ViewPlugin, PluginSetting } from '../../core/types';

export const layoutEnginePlugin: ViewPlugin = {
    id: 'layout-engine',
    name: '布局引擎',
    description: '智能多窗口布局管理与优化系统，支持自动平铺、模板管理和动画效果',
    keyword: 'layout',
    mode: 'view',
    icon: '🏗️',
    placeholderSuffix: '管理窗口布局...',
    aliases: [
        'layout', 'window-layout', 'tile', 'organize',
        '布局', '窗口布局', '平铺', '整理', '排列',
        'layout-engine', 'window-manager', 'tiling',
        'auto-tile', 'smart-layout', 'window-optimization'
    ],

    actions: [
        {
            id: 'open-layout-engine',
            name: '打开布局引擎',
            description: '启动高级布局管理面板',
            pattern: 'layout.*|布局.*'
        },
        {
            id: 'auto-tile-layout',
            name: '智能平铺布局',
            description: '自动应用最佳窗口布局',
            pattern: 'auto tile|智能平铺|自动布局'
        },
        {
            id: 'golden-ratio-layout',
            name: '黄金比例布局',
            description: '应用黄金比例窗口布局',
            pattern: 'golden ratio|黄金比例|golden'
        },
        {
            id: 'main-side-layout',
            name: '主副窗口布局',
            description: '主窗口占大部分空间，副窗口在侧边',
            pattern: 'main side|主副窗口|master slave'
        },
        {
            id: 'three-column-layout',
            name: '三列布局',
            description: '将屏幕分为三个相等的列',
            pattern: 'three column|三列布局|3列'
        },
        {
            id: 'optimize-layout',
            name: '优化布局',
            description: '优化当前窗口布局以提高空间利用率',
            pattern: 'optimize|优化布局|layout optimize'
        },
        {
            id: 'layout-templates',
            name: '布局模板',
            description: '管理和创建自定义布局模板',
            pattern: 'template|模板|layout template'
        },
        {
            id: 'layout-animation',
            name: '布局动画',
            description: '配置窗口布局切换动画效果',
            pattern: 'animation|动画|layout animation'
        },
        {
            id: 'layout-performance',
            name: '性能统计',
            description: '查看布局引擎性能统计和优化建议',
            pattern: 'performance|性能|stats|统计'
        },
        {
            id: 'grid-layout',
            name: '网格布局',
            description: '按网格排列窗口',
            pattern: 'grid|网格|grid layout'
        },
        {
            id: 'bsp-layout',
            name: 'BSP布局',
            description: '使用二进制空间分割算法排列窗口',
            pattern: 'bsp|binary space|二进制空间'
        },
        {
            id: 'recommend-layout',
            name: '推荐布局',
            description: '根据当前窗口数量推荐最佳布局',
            pattern: 'recommend|推荐|suggest|建议'
        }
    ],

    settings: [
        {
            id: 'auto-tile-enabled',
            title: '启用智能平铺',
            description: '自动应用智能平铺布局',
            type: 'boolean',
            defaultValue: true
        },
        {
            id: 'animation-enabled',
            title: '启用布局动画',
            description: '窗口布局切换时显示动画效果',
            type: 'boolean',
            defaultValue: true
        },
        {
            id: 'animation-duration',
            title: '动画时长 (毫秒)',
            description: '布局切换动画的持续时间',
            type: 'string',
            defaultValue: '300'
        },
        {
            id: 'min-window-width',
            title: '最小窗口宽度',
            description: '窗口的最小宽度像素值',
            type: 'string',
            defaultValue: '300'
        },
        {
            id: 'min-window-height',
            title: '最小窗口高度',
            description: '窗口的最小高度像素值',
            type: 'string',
            defaultValue: '200'
        },
        {
            id: 'window-gap',
            title: '窗口间距',
            description: '窗口之间的间距像素值',
            type: 'string',
            defaultValue: '8'
        },
        {
            id: 'screen-margin',
            title: '屏幕边距',
            description: '窗口到屏幕边缘的距离',
            type: 'string',
            defaultValue: '16'
        },
        {
            id: 'easing-function',
            title: '缓动函数',
            description: '布局动画的缓动效果',
            type: 'string',
            defaultValue: 'EaseInOut'
        },
        {
            id: 'performance-stats-enabled',
            title: '启用性能统计',
            description: '收集和显示布局引擎性能数据',
            type: 'boolean',
            defaultValue: true
        },
        {
            id: 'templates-enabled',
            title: '启用布局模板',
            description: '允许创建和使用自定义布局模板',
            type: 'boolean',
            defaultValue: true
        }
    ] as PluginSetting[]
};

export default layoutEnginePlugin;