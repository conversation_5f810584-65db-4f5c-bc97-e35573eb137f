/* 布局引擎插件视图样式 */

.layout-engine-view {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: var(--background-color, #ffffff);
    color: var(--text-color, #333333);
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    overflow: hidden;
}

/* 快捷操作栏 */
.quick-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    background: var(--panel-background, #f8f9fa);
    border-bottom: 1px solid var(--border-color, #e1e5e9);
    flex-shrink: 0;
}

.action-group {
    display: flex;
    gap: 12px;
    align-items: center;
}

.quick-action-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.quick-action-btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.quick-action-btn:hover:before {
    left: 100%;
}

.quick-action-btn.primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.quick-action-btn.primary:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
}

.quick-action-btn.secondary {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    color: white;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.quick-action-btn.secondary:hover {
    background: linear-gradient(135deg, #1e7e34, #155724);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
}

.quick-action-btn.tertiary {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529;
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

.quick-action-btn.tertiary:hover {
    background: linear-gradient(135deg, #e0a800, #d39e00);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.4);
}

.quick-action-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

.window-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.window-count {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-secondary, #6c757d);
    padding: 8px 12px;
    background: var(--badge-background, #e9ecef);
    border-radius: 20px;
}

.refresh-btn {
    padding: 8px;
    border: none;
    border-radius: 50%;
    background: var(--accent-color, #007bff);
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.refresh-btn:hover {
    background: var(--accent-color-hover, #0056b3);
    transform: rotate(180deg);
}

/* 预设布局 */
.preset-layouts {
    padding: 20px;
    flex-shrink: 0;
}

.preset-layouts h3 {
    margin: 0 0 16px 0;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.preset-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.preset-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    border: 2px solid var(--border-color, #e1e5e9);
    border-radius: 12px;
    background: var(--card-background, #ffffff);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.preset-btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.1), rgba(0, 123, 255, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.preset-btn:hover:before {
    opacity: 1;
}

.preset-btn:hover {
    border-color: var(--accent-color, #007bff);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 123, 255, 0.15);
}

.preset-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

.preset-icon {
    font-size: 32px;
    margin-bottom: 12px;
    position: relative;
    z-index: 1;
}

.preset-name {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 4px;
    position: relative;
    z-index: 1;
}

.preset-desc {
    font-size: 12px;
    color: var(--text-secondary, #6c757d);
    text-align: center;
    position: relative;
    z-index: 1;
}

/* 无窗口提示 */
.no-windows-notice {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
}

.notice-content {
    text-align: center;
    max-width: 400px;
}

.notice-icon {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.6;
}

.notice-content h3 {
    margin: 0 0 12px 0;
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary, #333333);
}

.notice-content p {
    margin: 0 0 24px 0;
    color: var(--text-secondary, #6c757d);
    line-height: 1.5;
}

.refresh-button {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    background: var(--accent-color, #007bff);
    color: white;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.refresh-button:hover {
    background: var(--accent-color-hover, #0056b3);
    transform: translateY(-1px);
}

/* 布局引擎面板容器 */
.layout-engine-panel-container {
    flex: 1;
    overflow: hidden;
    position: relative;
}

/* 处理中覆盖层 */
.processing-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.processing-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    padding: 32px;
    background: var(--card-background, #ffffff);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.spinner {
    font-size: 24px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

.processing-indicator span {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary, #333333);
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
    .layout-engine-view {
        --background-color: #1a1a1a;
        --text-color: #ffffff;
        --text-primary: #ffffff;
        --text-secondary: #adb5bd;
        --panel-background: #2d2d2d;
        --card-background: #2d2d2d;
        --border-color: #404040;
        --badge-background: #404040;
        --accent-color: #0d6efd;
        --accent-color-hover: #0b5ed7;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .quick-actions {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }

    .action-group {
        justify-content: center;
    }

    .window-info {
        justify-content: center;
    }

    .preset-grid {
        grid-template-columns: 1fr;
    }

    .quick-action-btn {
        padding: 12px 16px;
    }
}

@media (max-width: 480px) {
    .quick-actions {
        padding: 12px 16px;
    }

    .preset-layouts {
        padding: 16px;
    }

    .action-group {
        flex-direction: column;
        gap: 8px;
    }

    .quick-action-btn {
        width: 100%;
        justify-content: center;
    }
}