//! # 布局引擎插件视图组件
//! 
//! 提供布局引擎功能的主要用户界面

import React, { useState, useCallback, useEffect } from 'react';
import LayoutEnginePanel from '../../components/LayoutEnginePanel';
import { useLayoutEngine, PresetTemplates } from '../../hooks/useLayoutEngine';
import { useWindowManager } from '../../hooks/useWindowManager';
import { useEventBus } from '../../hooks/useEventBus';
import './LayoutEngineView.css';

export interface LayoutEngineViewProps {
    // 插件操作ID，用于直接执行特定操作
    actionId?: string;
    // 插件配置
    settings?: Record<string, any>;
}

export const LayoutEngineView: React.FC<LayoutEngineViewProps> = ({
    actionId,
    settings = {}
}) => {
    const layoutEngine = useLayoutEngine();
    const windowManager = useWindowManager();
    const { emit } = useEventBus();

    const [currentWindows, setCurrentWindows] = useState<string[]>([]);
    const [isProcessing, setIsProcessing] = useState(false);
    const [lastAction, setLastAction] = useState<string | null>(null);

    // 获取当前所有窗口ID
    const refreshWindows = useCallback(async () => {
        try {
            await windowManager.refreshWindows();
            const windowIds = windowManager.windows.map((w: any) => w.id);
            setCurrentWindows(windowIds);
        } catch (err) {
            console.error('获取窗口列表失败:', err);
        }
    }, [windowManager]);

    // 执行布局操作
    const executeLayoutAction = useCallback(async (action: string) => {
        if (isProcessing) return;

        setIsProcessing(true);
        setLastAction(action);

        try {
            let result;

            switch (action) {
                case 'auto-tile-layout':
                    result = await layoutEngine.applyAutoTileLayout(currentWindows);
                    await emit('layout:auto-tile-applied', {
                        windowIds: currentWindows,
                        result
                    });
                    break;

                case 'golden-ratio-layout':
                    result = await layoutEngine.applyTemplate(PresetTemplates.GoldenRatio, currentWindows);
                    await emit('layout:template-applied', {
                        templateId: PresetTemplates.GoldenRatio,
                        windowIds: currentWindows,
                        result
                    });
                    break;

                case 'main-side-layout':
                    result = await layoutEngine.applyTemplate(PresetTemplates.MainSide, currentWindows);
                    await emit('layout:template-applied', {
                        templateId: PresetTemplates.MainSide,
                        windowIds: currentWindows,
                        result
                    });
                    break;

                case 'three-column-layout':
                    result = await layoutEngine.applyTemplate(PresetTemplates.ThreeColumn, currentWindows);
                    await emit('layout:template-applied', {
                        templateId: PresetTemplates.ThreeColumn,
                        windowIds: currentWindows,
                        result
                    });
                    break;

                case 'optimize-layout':
                    // 获取当前窗口布局
                    const currentLayout: Record<string, any> = {};
                    for (const windowId of currentWindows) {
                        try {
                            const windowInfo = windowManager.windows.find(w => w.id === windowId);
                            if (windowInfo && windowInfo.state) {
                                const state = windowInfo.state;
                                currentLayout[windowId] = {
                                    x: state.position?.[0] || 0,
                                    y: state.position?.[1] || 0,
                                    width: state.size?.[0] || 800,
                                    height: state.size?.[1] || 600
                                };
                            }
                        } catch (err) {
                            console.warn(`无法获取窗口 ${windowId} 状态:`, err);
                        }
                    }

                    if (Object.keys(currentLayout).length > 0) {
                        result = await layoutEngine.optimizeLayout(currentLayout);
                        await emit('layout:optimized', {
                            originalLayout: currentLayout,
                            optimizedLayout: result.layout
                        });
                    }
                    break;

                case 'recommend-layout':
                    const recommendation = await layoutEngine.recommendLayout(currentWindows);
                    result = await layoutEngine.applyTemplate(recommendation, currentWindows);
                    await emit('layout:recommendation-applied', {
                        recommendedTemplate: recommendation,
                        windowIds: currentWindows,
                        result
                    });
                    break;

                default:
                    console.warn('未知的布局操作:', action);
                    return;
            }

            if (result?.success) {
                console.log(`布局操作 ${action} 执行成功:`, result.message);
            } else {
                console.error(`布局操作 ${action} 执行失败:`, result?.message);
            }

        } catch (err) {
            console.error(`执行布局操作 ${action} 时出错:`, err);
            await emit('layout:error', {
                action,
                error: err instanceof Error ? err.message : String(err)
            });
        } finally {
            setIsProcessing(false);
        }
    }, [
        isProcessing,
        currentWindows,
        layoutEngine,
        windowManager,
        emit
    ]);

    // 初始化和处理动作ID
    useEffect(() => {
        refreshWindows();

        // 如果有指定的操作ID，自动执行
        if (actionId && actionId !== 'open-layout-engine') {
            executeLayoutAction(actionId);
        }
    }, [actionId, refreshWindows, executeLayoutAction]);

    // 应用插件设置
    useEffect(() => {
        const applySettings = async () => {
            try {
                // 应用约束设置
                if (settings['min-window-width'] || settings['min-window-height'] ||
                    settings['window-gap'] || settings['screen-margin']) {
                    const constraints = {
                        min_window_width: parseInt(settings['min-window-width']) || 300,
                        min_window_height: parseInt(settings['min-window-height']) || 200,
                        window_gap: parseInt(settings['window-gap']) || 8,
                        screen_margin: parseInt(settings['screen-margin']) || 16
                    };
                    await layoutEngine.setConstraints(constraints);
                }

                // 应用动画设置
                if (settings['animation-enabled'] !== undefined ||
                    settings['animation-duration'] || settings['easing-function']) {
                    const animation = {
                        enabled: settings['animation-enabled'] !== false,
                        duration_ms: parseInt(settings['animation-duration']) || 300,
                        easing: settings['easing-function'] || 'EaseInOut'
                    };
                    await layoutEngine.setAnimation(animation);
                }

            } catch (err) {
                console.error('应用插件设置失败:', err);
            }
        };

        applySettings();
    }, [settings, layoutEngine]);

    // 快捷操作处理
    const handleQuickAction = useCallback(async (action: string) => {
        await refreshWindows();
        await executeLayoutAction(action);
    }, [refreshWindows, executeLayoutAction]);

    return (
        <div className="layout-engine-view">
            {/* 顶部快捷操作栏 */}
            <div className="quick-actions">
                <div className="action-group">
                    <button
                        className="quick-action-btn primary"
                        onClick={() => handleQuickAction('auto-tile-layout')}
                        disabled={isProcessing || currentWindows.length === 0}
                        title="智能平铺当前所有窗口"
                    >
                        {isProcessing && lastAction === 'auto-tile-layout' ? '⚙️' : '🤖'}
                        智能平铺
                    </button>

                    <button
                        className="quick-action-btn secondary"
                        onClick={() => handleQuickAction('recommend-layout')}
                        disabled={isProcessing || currentWindows.length === 0}
                        title="获取布局推荐并应用"
                    >
                        {isProcessing && lastAction === 'recommend-layout' ? '⚙️' : '💡'}
                        推荐布局
                    </button>

                    <button
                        className="quick-action-btn tertiary"
                        onClick={() => handleQuickAction('optimize-layout')}
                        disabled={isProcessing || currentWindows.length === 0}
                        title="优化当前窗口布局"
                    >
                        {isProcessing && lastAction === 'optimize-layout' ? '⚙️' : '⚡'}
                        优化布局
                    </button>
                </div>

                <div className="window-info">
                    <span className="window-count">
                        📱 {currentWindows.length} 个窗口
                    </span>
                    <button
                        className="refresh-btn"
                        onClick={refreshWindows}
                        title="刷新窗口列表"
                    >
                        🔄
                    </button>
                </div>
            </div>

            {/* 预设布局快捷按钮 */}
            <div className="preset-layouts">
                <h3>🏗️ 预设布局</h3>
                <div className="preset-grid">
                    <button
                        className="preset-btn"
                        onClick={() => handleQuickAction('golden-ratio-layout')}
                        disabled={isProcessing || currentWindows.length === 0}
                    >
                        <span className="preset-icon">✨</span>
                        <span className="preset-name">黄金比例</span>
                        <span className="preset-desc">1.618 比例分割</span>
                    </button>

                    <button
                        className="preset-btn"
                        onClick={() => handleQuickAction('main-side-layout')}
                        disabled={isProcessing || currentWindows.length === 0}
                    >
                        <span className="preset-icon">📱</span>
                        <span className="preset-name">主副窗口</span>
                        <span className="preset-desc">主窗口 + 侧边栏</span>
                    </button>

                    <button
                        className="preset-btn"
                        onClick={() => handleQuickAction('three-column-layout')}
                        disabled={isProcessing || currentWindows.length === 0}
                    >
                        <span className="preset-icon">📊</span>
                        <span className="preset-name">三列布局</span>
                        <span className="preset-desc">等分三列</span>
                    </button>
                </div>
            </div>

            {/* 状态提示 */}
            {currentWindows.length === 0 && (
                <div className="no-windows-notice">
                    <div className="notice-content">
                        <span className="notice-icon">📋</span>
                        <h3>没有发现可管理的窗口</h3>
                        <p>请先打开一些应用程序窗口，然后刷新窗口列表</p>
                        <button onClick={refreshWindows} className="refresh-button">
                            🔄 刷新窗口列表
                        </button>
                    </div>
                </div>
            )}

            {/* 主要布局引擎面板 */}
            {currentWindows.length > 0 && (
                <div className="layout-engine-panel-container">
                    <LayoutEnginePanel />
                </div>
            )}

            {/* 处理中指示器 */}
            {isProcessing && (
                <div className="processing-overlay">
                    <div className="processing-indicator">
                        <div className="spinner">⚙️</div>
                        <span>正在处理布局操作...</span>
                    </div>
                </div>
            )}
        </div>
    );
};

export default LayoutEngineView;