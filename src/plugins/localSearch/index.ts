import { ListPlugin, ListPluginRunner, ListItem } from '../../core/types';

const run: ListPluginRunner = async () => {
    const mockFileSystem = [
        { name: 'Project Proposal.docx', type: 'file', path: '~/Documents/Project Alpha/Project Proposal.docx', content: 'Project Alpha - Q3 2024\n\n1. Introduction\nThis document outlines the scope, goals, and timeline for Project Alpha...' },
        { name: 'README.md', type: 'file', path: '~/Code/NovaRay/README.md', content: '# NovaRay Project\n\nThis is the main project file for the NovaRay application, an extensible launcher built with modern web technologies.' },
        { name: 'logo.svg', type: 'file', path: '~/Assets/logo.svg', content: '<svg>...</svg>' },
        { name: 'Screenshots', type: 'folder', path: '~/Pictures/Screenshots', content: 'Contains 5 screenshots.' },
    ];
    const listItems: ListItem[] = mockFileSystem.map(item => ({
      id: item.name,
      icon: item.type === 'folder' ? 'Folder' : 'File',
      title: item.name,
      description: item.path,
      preview: { type: 'text', content: item.content },
      onSelectAction: { type: 'show-list', payload: [
        { id: `copy-path-${item.name}`, icon: 'ClipboardCopy', title: 'Copy Path', onSelectAction: { type: 'copy', payload: item.path } },
        ...(item.type === 'file' ? [{ id: `copy-content-${item.name}`, icon: 'FileText', title: 'Copy Content', onSelectAction: { type: 'copy', payload: item.content || '' } }] : []),
        { id: `show-in-finder-${item.name}`, icon: 'Locate', title: 'Show in Finder (mock)', onSelectAction: { type: 'copy', payload: `Simulating: showing ${item.path} in Finder` } },
      ]}
    }));
    return `LIST_MODE_RESULTS::${JSON.stringify({ items: listItems })}`;
};

export const localSearchPlugin: ListPlugin = {
    id: 'list-local-search', 
    name: 'Local Search', 
    icon: 'FolderSearch', 
    mode: 'list', 
    keyword: 'find', 
    placeholderSuffix: 'on your local disk...', 
    description: 'Find files and folders on your computer.',
    run
};
