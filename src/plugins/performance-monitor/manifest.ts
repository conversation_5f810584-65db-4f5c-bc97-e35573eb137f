import { ListPlugin } from '../../core/types';

export const performanceMonitorPlugin: ListPlugin = {
    id: 'performance-monitor',
    name: '性能监控',
    description: '实时监控系统性能指标，包括CPU、内存、响应时间等',
    mode: 'list',
    keyword: 'performance',
    icon: '📊',
    aliases: ['performance', 'monitor', 'system', 'cpu', 'memory', 'benchmark', '性能', '监控', '系统', '内存', 'CPU', '基准'],

    actions: [
        {
            id: 'open-performance-monitor',
            name: '打开性能监控面板',
            pattern: '.*',
            description: '打开系统性能监控面板',
            isInteractive: false
        },
        {
            id: 'show-cpu-usage',
            name: '显示CPU使用率',
            pattern: '^(cpu|处理器).*',
            description: '显示当前CPU使用率',
            isInteractive: false
        },
        {
            id: 'show-memory-usage',
            name: '显示内存使用率',
            pattern: '^(memory|内存|ram).*',
            description: '显示当前内存使用情况',
            isInteractive: false
        },
        {
            id: 'run-benchmark',
            name: '运行性能基准测试',
            pattern: '^(benchmark|基准测试|性能测试).*',
            description: '运行快速性能基准测试',
            isInteractive: false
        },
        {
            id: 'export-data',
            name: '导出性能数据',
            pattern: '^(export|导出).*',
            description: '导出性能监控数据到文件',
            isInteractive: false
        }
    ],

    async run({ input, action, getSetting }) {
        try {
            switch (action.id) {
                case 'open-performance-monitor':
                    return 'UI_ACTION::open-performance-monitor';

                case 'show-cpu-usage':
                    return 'TAURI_COMMAND::get_system_snapshot::cpu_usage';

                case 'show-memory-usage':
                    return 'TAURI_COMMAND::get_system_snapshot::memory_usage';

                case 'run-benchmark':
                    return 'TAURI_COMMAND::run_performance_benchmark';

                case 'export-data':
                    return 'TAURI_COMMAND::export_performance_data::json';

                default:
                    return '未知操作';
            }
        } catch (error) {
            return `错误: ${error}`;
        }
    }
};

export default performanceMonitorPlugin;