import { Plugin } from '../core/types';
import { localSearchPlugin } from './localSearch';
import { calculatorPlugin } from './calculator';
import { dictionaryPlugin } from './dictionary';
import { notionPlugin } from './notion';
import { clipboardPlugin } from './clipboard';
import { themePlugin } from './theme';
import { snippetsPlugin } from './snippets';
import { notesPlugin } from './notes';
import { settingsPlugin } from './settings';
import { storePlugin } from './store';
import { systemPlugin } from './system';
import { shellPlugin } from './shell';
import { notepadPlugin } from './notepad';
import { chatPlugin } from './chat';
import { translatePlugin } from './translate';
import { eventBusTestPlugin } from './event-bus-test/manifest';
import { windowManagerPlugin } from './window-manager/manifest';
import { performanceMonitorPlugin } from './performance-monitor/manifest';

// This is the single source of truth for all installed plugins.
// The order of plugins in this array determines their default order in the UI.
export const installedPlugins: Plugin[] = [
  // Core functionality
  systemPlugin,
  settingsPlugin,
  storePlugin,
  windowManagerPlugin,

  // Daily tools
  notesPlugin,
  calculatorPlugin,
  clipboardPlugin,

  // App launchers & helpers
  notionPlugin,
  themePlugin,

  // Information & Content
  dictionaryPlugin,
  localSearchPlugin,
  snippetsPlugin,

  // View Mode Plugins
  shellPlugin,
  notepadPlugin,

  // AI Agents
  chatPlugin,
  translatePlugin,

  // Development Tools
  eventBusTestPlugin,
];
