<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Self-Contained App Demo</title>
  <style>
    :root {
      --primary-text: #202124;
      --secondary-text: #5f6368;
      --border-color: #dadce0;
      --app-bg: #fff;
    }
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
      margin: 0;
      background-color: var(--app-bg);
      color: var(--primary-text);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
      box-sizing: border-box;
      text-align: center;
      padding: 20px;
    }
    .icon {
      width: 48px;
      height: 48px;
      margin-bottom: 12px;
    }
    h1 {
      margin-top: 0;
      margin-bottom: 8px;
    }
    p {
      margin-top: 0;
      margin-bottom: 24px;
      max-width: 380px;
      color: var(--secondary-text);
    }
    .theme-preview {
      width: 100%;
      max-width: 200px;
      height: 120px;
      border-radius: 8px;
      border: 1px solid var(--border-color);
      margin-bottom: 20px;
      transition: background-color 0.3s ease;
    }
    .color-swatches {
      display: flex;
      gap: 12px;
    }
    .swatch {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      cursor: pointer;
      border: 2px solid white;
      box-shadow: 0 1px 3px rgba(0,0,0,0.2);
      transition: transform 0.2s ease;
    }
    .swatch:hover {
      transform: scale(1.1);
    }
  </style>
</head>
<body>

  <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLXBhbGV0dGUiPjxtb2RpZmllZC8+PHBhdGggZD0iTTIgM2gzLjg1YTQgNCAwIDAgMSAzLjcxIDEuNzJMMTIgMTFsLjQ0LS44OGE0IDQgMCAwIDEgMy43MS0xLjcySDE5YTQgNCAwIDAgMSAyLjgzIDEuMTdMMjIgMTYiLz48cGF0aCBkPSJNNiA2TDIuNzUgOS4yNWE0IDQgMCAwIDAgMCA1LjVMNiAxOEgxN2E0IDQgMCAwIDAgNCA0VjQiLz48L3N2Zz4=" class="icon" alt="Palette Icon"/>
  <h1>Web App Plugin</h1>
  <p>This is a self-contained application. All its assets (CSS, JS, even this SVG icon) are included in this single HTML file.</p>
  
  <div id="preview" class="theme-preview" style="background-color: #e8f0fe;"></div>

  <div class="color-swatches">
    <div class="swatch" data-color="#e8f0fe" style="background-color: #e8f0fe;"></div>
    <div class="swatch" data-color="#e6f4ea" style="background-color: #e6f4ea;"></div>
    <div class="swatch" data-color="#fce8e6" style="background-color: #fce8e6;"></div>
    <div class="swatch" data-color="#fffde7" style="background-color: #fffde7;"></div>
    <div class="swatch" data-color="#f3e8fd" style="background-color: #f3e8fd;"></div>
  </div>

  <script>
    const previewBox = document.getElementById('preview');
    const swatches = document.querySelectorAll('.swatch');

    swatches.forEach(swatch => {
      swatch.addEventListener('click', () => {
        const color = swatch.getAttribute('data-color');
        previewBox.style.backgroundColor = color;
      });
    });
  </script>

</body>
</html>