import { ListPlugin, ListPluginRunner } from '../../core/types';

const run: ListPluginRunner = async () => {
    const result = { items: [{id: 'open-notion', icon: 'ExternalLink', title: 'Open Notion', description: 'https://notion.so', onSelectAction: { type: 'open-url', payload: 'https://notion.so'}}]};
    return `LIST_MODE_RESULTS::${JSON.stringify(result)}`;
};

export const notionPlugin: ListPlugin = { 
    id: 'list-open-notion', 
    name: 'Open Notion', 
    icon: 'https://upload.wikimedia.org/wikipedia/commons/4/45/Notion_app_logo.png', 
    mode: 'list', keyword: 'notion', 
    description: 'Quickly open the Notion web app.',
    actions: [
        { id: 'action-open-notion', name: 'Open Notion', pattern: '^notion$' }
    ],
    run
};
