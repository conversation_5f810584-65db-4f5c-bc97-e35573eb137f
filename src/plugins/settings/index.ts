import { ListPlugin, ListPluginRunner } from '../../core/types';

const run: ListPluginRunner = async () => {
    const result = { items: [{id: 'settings', icon: 'Settings', title: 'Open Settings', description: 'Configure NovaRay and its plugins.', onSelectAction: { type: 'open-settings', payload: null }}]};
    return `LIST_MODE_RESULTS::${JSON.stringify(result)}`;
};

export const settingsPlugin: ListPlugin = {
    id: 'list-settings-launcher',
    name: 'Settings',
    icon: 'Settings',
    mode: 'list',
    keyword: 'settings',
    description: 'Configure NovaRay and its plugins.',
    run
};
