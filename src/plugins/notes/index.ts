import { ListPlugin, ListPluginRunner, ListItem } from '../../core/types';

// --- Mock Database for this plugin ---
const MOCK_NOTES_DB: { id: number; content: string; createdAt: Date }[] = [
    { id: 1, content: "Don't forget to buy milk on the way home.", createdAt: new Date(Date.now() - 3600000 * 2) },
    { id: 2, content: "Idea for NovaRay: implement a plugin store.", createdAt: new Date(Date.now() - 3600000 * 24) },
];
let nextNoteId = 3;


const run: ListPluginRunner = async ({ input, action }) => {
    if (action.id === 'action-add-note') {
        // The search component gives us the full input, so we extract the content.
        const regex = new RegExp(action.pattern, 'i');
        const match = input.match(regex);
        const content = match && match.length > 1 ? match[match.length - 1].trim() : '';
        if (!content) {
            return "Error: Note content cannot be empty.";
        }
        MOCK_NOTES_DB.unshift({ id: nextNoteId++, content: content, createdAt: new Date() });
        return `Note saved: "${content}"`;
    }
    
    if (action.id === 'action-view-notes') {
        const searchInput = input.trim().toLowerCase();
        const filteredNotes = searchInput 
            ? MOCK_NOTES_DB.filter(note => note.content.toLowerCase().includes(searchInput))
            : MOCK_NOTES_DB;

        if (filteredNotes.length === 0) {
             const emptyStateItem: ListItem = {
                id: 'no-notes-found',
                icon: 'SearchX',
                title: searchInput ? 'No notes found' : 'No notes yet',
                description: searchInput ? `No notes match your search for "${input}"` : 'Use "note <your note>" to add one.',
                onSelectAction: { type: 'copy', payload: ''} // No-op
             };
             return `LIST_MODE_RESULTS::${JSON.stringify({ items: [emptyStateItem] })}`;
        }

        const listItems: ListItem[] = filteredNotes.map(note => ({
            id: `note-${note.id}`,
            icon: 'FileText',
            title: note.content,
            description: `Saved: ${note.createdAt.toLocaleString()}`,
            preview: { type: 'text', content: note.content },
            onSelectAction: { type: 'show-list', payload: [
                { id: `copy-note-${note.id}`, icon: 'ClipboardCopy', title: 'Copy Content', onSelectAction: { type: 'copy', payload: note.content } },
                { id: `delete-note-${note.id}`, icon: 'Trash2', title: 'Delete Note (mock)', onSelectAction: { type: 'copy', payload: `Simulating: deleted note ${note.id}` } },
            ] }
        }));
        return `LIST_MODE_RESULTS::${JSON.stringify({ items: listItems })}`;
    }
    
    return `Error: Unknown action '${action.id}' for Notes plugin.`;
};

export const notesPlugin: ListPlugin = {
  id: 'plugin-notes',
  name: 'Notes',
  icon: 'BookOpen',
  mode: 'list',
  keyword: '', // Keyword is empty, it's action-driven
  description: 'Add, view, and search your notes.',
  aliases: ['note', 'notes'],
  actions: [
    { 
      id: 'action-add-note', 
      name: 'Add Note', 
      pattern: '^(note|addnote|记笔记)\\s+(.+)', 
      description: 'e.g. note my new idea' 
    },
    { 
      id: 'action-view-notes', 
      name: 'View Notes', 
      pattern: '^(notes|viewnotes|查看笔记)$', 
      description: 'View and search all your notes.',
      isInteractive: true
    }
  ],
  run
};
