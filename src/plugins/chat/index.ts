import { ListPlugin, ListPluginRunner } from '../../core/types';
import { ai } from '../../core/ai';
import { GenerateContentResponse } from '@google/genai';

const run: ListPluginRunner = async ({ input }) => {
    if (!input) {
        return "Error: Please provide a message to chat about.";
    }
    try {
        // This is a generic AI call. More complex agents might have specific system prompts.
        const response: GenerateContentResponse = await ai.models.generateContent({ model: 'gemini-2.5-flash-preview-04-17', contents: input });
        return response.text;
    } catch(e) {
        const errorMessage = e instanceof Error ? e.message : String(e);
        console.error("Error calling AI model for Chat plugin:", e);
        return `Error calling AI model: ${errorMessage}`;
    }
};

export const chatPlugin: ListPlugin = {
  id: 'agent-chat', 
  name: 'Chat', 
  icon: 'MessageCircle', 
  mode: 'list', 
  keyword: 'chat', 
  placeholderSuffix: 'with AI...', 
  description: 'Chat with the AI model.',
  run,
};
