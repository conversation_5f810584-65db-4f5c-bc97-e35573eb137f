/**
 * 事件总线测试插件
 * 用于在应用内部测试事件总线功能
 */

import { ViewPlugin, PluginSetting } from '../../core/types';

export const eventBusTestPlugin: ViewPlugin = {
    id: 'event-bus-test',
    name: '事件总线测试',
    description: '测试前端与后端事件通信系统',
    keyword: 'event-test',
    mode: 'view',
    icon: '🚀',
    placeholderSuffix: '测试事件系统...',
    aliases: ['event', 'eventbus', 'events', 'test-event'],
    actions: [
        {
            id: 'open-test',
            name: '打开事件测试',
            description: '打开事件总线测试界面',
            pattern: 'event.*'
        },
        {
            id: 'test-custom-event',
            name: '测试自定义事件',
            description: '发送一个测试自定义事件',
            pattern: 'test custom event|测试自定义事件'
        },
        {
            id: 'clear-event-history',
            name: '清空事件历史',
            description: '清空所有事件历史记录',
            pattern: 'clear events|清空事件历史'
        }
    ],
    settings: [
        {
            id: 'auto-refresh',
            title: '自动刷新',
            description: '自动刷新事件历史',
            type: 'boolean',
            defaultValue: true
        },
        {
            id: 'max-history',
            title: '最大历史记录数',
            description: '最多保存多少条事件历史',
            type: 'string',
            defaultValue: '50'
        }
    ] as PluginSetting[]
};