<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>事件总线测试</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .status-display {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
            text-align: center;
            transition: all 0.3s ease;
        }

        .status-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .demo-section {
            background: white;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            border: 1px solid #e9ecef;
        }

        .demo-section h3 {
            color: #495057;
            margin-bottom: 20px;
            font-size: 1.3em;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }

        .button-group {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }

        button {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            min-width: 120px;
            position: relative;
            overflow: hidden;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        button:active {
            transform: translateY(0);
        }

        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
        }

        .success {
            background: linear-gradient(135deg, #28a745, #1e7e34);
            color: white;
        }

        .secondary {
            background: linear-gradient(135deg, #6c757d, #545b62);
            color: white;
        }

        .info {
            background: linear-gradient(135deg, #17a2b8, #117a8b);
            color: white;
        }

        .warning {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: #212529;
        }

        .danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
        }

        .event-history {
            max-height: 400px;
            overflow-y: auto;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            background-color: #f8f9fa;
        }

        .event-item {
            background: white;
            padding: 15px;
            margin-bottom: 12px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .event-item.system {
            border-left-color: #dc3545;
        }

        .event-item.ui {
            border-left-color: #17a2b8;
        }

        .event-item.plugin {
            border-left-color: #28a745;
        }

        .event-item.custom {
            border-left-color: #ffc107;
        }

        .event-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .event-type {
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            text-transform: uppercase;
        }

        .event-type.system {
            background-color: #dc3545;
            color: white;
        }

        .event-type.ui {
            background-color: #17a2b8;
            color: white;
        }

        .event-type.plugin {
            background-color: #28a745;
            color: white;
        }

        .event-type.custom {
            background-color: #ffc107;
            color: #212529;
        }

        .event-time {
            font-size: 12px;
            color: #6c757d;
        }

        .event-details {
            font-size: 14px;
            color: #495057;
            margin-bottom: 8px;
        }

        .event-payload {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #495057;
            border: 1px solid #dee2e6;
            overflow-x: auto;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
            font-style: italic;
        }

        .close-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            font-size: 18px;
            cursor: pointer;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .close-button:hover {
            background: rgba(0, 0, 0, 0.9);
            transform: scale(1.1);
        }

        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .button-group {
                flex-direction: column;
            }

            button {
                width: 100%;
            }
        }
    </style>
</head>

<body>
    <button class="close-button" onclick="closeView()" title="关闭">×</button>

    <div class="header">
        <h1>🚀 事件总线测试面板</h1>
        <p>测试和调试前端与后端事件通信系统</p>
    </div>

    <div class="status-display status-info" id="status-display">
        状态: 就绪
    </div>

    <div class="container">
        <!-- 自定义事件 -->
        <div class="demo-section">
            <h3>🎯 自定义事件</h3>
            <div class="button-group">
                <button class="primary" onclick="testCustomEvent()">发送自定义事件</button>
            </div>
        </div>

        <!-- UI事件 -->
        <div class="demo-section">
            <h3>🖥️ UI事件</h3>
            <div class="button-group">
                <button class="success" onclick="testUIEvent()">测试UI事件</button>
                <button class="info" onclick="simulateSearchQuery()">模拟搜索查询</button>
                <button class="warning" onclick="simulateThemeChange()">模拟主题变化</button>
                <button class="secondary" onclick="simulateWindowChange()">模拟窗口变化</button>
            </div>
        </div>

        <!-- 插件事件 -->
        <div class="demo-section">
            <h3>🔌 插件事件</h3>
            <div class="button-group">
                <button class="success" onclick="simulatePluginExecution()">模拟插件执行完成</button>
                <button onclick="simulatePluginInstall()">模拟插件安装</button>
                <button onclick="simulatePluginToggle()">模拟插件启用/禁用</button>
            </div>
        </div>

        <!-- 系统事件 -->
        <div class="demo-section">
            <h3>⚙️ 系统事件</h3>
            <div class="button-group">
                <button onclick="simulateShortcut()">模拟全局快捷键</button>
                <button onclick="simulateClipboardChange()">模拟剪贴板变化</button>
                <button onclick="simulateFileChange()">模拟文件变化</button>
            </div>
        </div>

        <!-- 事件历史 -->
        <div class="demo-section">
            <h3>📊 事件历史</h3>
            <div class="button-group">
                <button onclick="loadEventHistory()" id="load-history-btn">刷新历史</button>
                <button class="secondary" onclick="clearEventHistory()">清空历史</button>
                <button onclick="toggleAutoRefresh()" id="auto-refresh-btn">启用自动刷新</button>
            </div>
            <div class="event-history" id="event-history">
                <div class="loading">正在加载事件历史...</div>
            </div>
        </div>
    </div>

    <script>
        let eventHistory = [];
        let autoRefreshEnabled = false;
        let autoRefreshInterval = null;

        // 状态显示函数
        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('status-display');
            statusEl.textContent = `状态: ${message}`;
            statusEl.className = `status-display status-${type}`;
        }

        // 关闭视图
        function closeView() {
            window.parent.postMessage({ type: 'close-view' }, '*');
        }

        // 添加事件到历史记录
        function addEventToHistory(type, payload, source) {
            const event = {
                id: Date.now().toString(),
                timestamp: Date.now(),
                type,
                payload,
                source
            };
            eventHistory.unshift(event);
            if (eventHistory.length > 50) {
                eventHistory = eventHistory.slice(0, 50);
            }
            updateEventHistoryDisplay();
            console.log('新事件:', event);
        }

        // 发送事件到父窗口
        function sendEventToParent(command, eventData) {
            window.parent.postMessage({
                type: 'event-bus-command',
                payload: { command, eventData }
            }, '*');
        }

        // 测试自定义事件
        function testCustomEvent() {
            const eventData = {
                event_name: 'test-custom-event',
                event_type: 'Custom',
                payload: {
                    message: 'Hello from Event Bus Test!',
                    timestamp: Date.now(),
                    testData: { value: 42, enabled: true }
                },
                source: 'event-bus-test-plugin'
            };
            sendEventToParent('emit_custom_event', eventData);
        }

        // 测试UI事件
        function testUIEvent() {
            const eventData = {
                event_name: 'test-ui-event',
                event_type: 'UI',
                payload: {
                    action: 'window-resize',
                    width: 800,
                    height: 600,
                    timestamp: Date.now()
                },
                source: 'event-bus-test-plugin'
            };
            sendEventToParent('emit_ui_event', eventData);
        }

        // 模拟搜索查询
        function simulateSearchQuery() {
            const eventData = {
                event_name: 'search-query-changed',
                event_type: 'UI',
                payload: {
                    query: 'test search query',
                    timestamp: Date.now(),
                    context: 'main-search'
                },
                source: 'event-bus-test-plugin'
            };
            sendEventToParent('emit_ui_event', eventData);
        }

        // 模拟主题变化
        function simulateThemeChange() {
            const eventData = {
                event_name: 'theme-changed',
                event_type: 'UI',
                payload: {
                    theme: Math.random() > 0.5 ? 'dark' : 'light',
                    timestamp: Date.now(),
                    source: 'settings'
                },
                source: 'event-bus-test-plugin'
            };
            sendEventToParent('emit_ui_event', eventData);
        }

        // 模拟窗口变化
        function simulateWindowChange() {
            const eventData = {
                event_name: 'window-state-changed',
                event_type: 'UI',
                payload: {
                    windowId: 'main',
                    state: 'focused',
                    timestamp: Date.now(),
                    metadata: { width: 900, height: 700 }
                },
                source: 'event-bus-test-plugin'
            };
            sendEventToParent('emit_ui_event', eventData);
        }

        // 模拟插件执行完成
        function simulatePluginExecution() {
            const eventData = {
                event_name: 'plugin-execution-completed',
                event_type: 'Plugin',
                payload: {
                    pluginId: 'calculator',
                    result: '计算结果: 42',
                    duration: 150,
                    timestamp: Date.now()
                },
                source: 'event-bus-test-plugin'
            };
            sendEventToParent('emit_plugin_event', eventData);
        }

        // 模拟插件安装
        function simulatePluginInstall() {
            const eventData = {
                event_name: 'plugin-installed',
                event_type: 'Plugin',
                payload: {
                    pluginId: 'new-test-plugin',
                    version: '1.0.0',
                    timestamp: Date.now(),
                    source: 'store'
                },
                source: 'event-bus-test-plugin'
            };
            sendEventToParent('emit_plugin_event', eventData);
        }

        // 模拟插件启用/禁用
        function simulatePluginToggle() {
            const eventData = {
                event_name: 'plugin-toggled',
                event_type: 'Plugin',
                payload: {
                    pluginId: 'calculator',
                    enabled: Math.random() > 0.5,
                    timestamp: Date.now(),
                    triggeredBy: 'user'
                },
                source: 'event-bus-test-plugin'
            };
            sendEventToParent('emit_plugin_event', eventData);
        }

        // 模拟系统事件
        function simulateShortcut() {
            const eventData = {
                event_name: 'global-shortcut-triggered',
                event_type: 'System',
                payload: {
                    shortcut: 'Cmd+Space',
                    timestamp: Date.now(),
                    context: 'global'
                },
                source: 'event-bus-test-plugin'
            };
            sendEventToParent('emit_system_event', eventData);
        }

        function simulateClipboardChange() {
            const eventData = {
                event_name: 'clipboard-changed',
                event_type: 'System',
                payload: {
                    content: 'Hello from clipboard!',
                    timestamp: Date.now(),
                    format: 'text'
                },
                source: 'event-bus-test-plugin'
            };
            sendEventToParent('emit_system_event', eventData);
        }

        function simulateFileChange() {
            const eventData = {
                event_name: 'file-changed',
                event_type: 'System',
                payload: {
                    path: '/Users/<USER>/documents/test.txt',
                    action: 'modified',
                    timestamp: Date.now(),
                    size: 1024
                },
                source: 'event-bus-test-plugin'
            };
            sendEventToParent('emit_system_event', eventData);
        }

        // 加载事件历史
        function loadEventHistory() {
            sendEventToParent('get_event_history', {});
        }

        // 清空事件历史
        function clearEventHistory() {
            sendEventToParent('clear_event_history', {});
        }

        // 切换自动刷新
        function toggleAutoRefresh() {
            const btn = document.getElementById('auto-refresh-btn');
            if (autoRefreshEnabled) {
                clearInterval(autoRefreshInterval);
                autoRefreshEnabled = false;
                btn.textContent = '启用自动刷新';
                updateStatus('自动刷新已禁用', 'info');
            } else {
                autoRefreshInterval = setInterval(() => {
                    loadEventHistory();
                }, 2000);
                autoRefreshEnabled = true;
                btn.textContent = '禁用自动刷新';
                updateStatus('自动刷新已启用', 'success');
            }
        }

        // 更新事件历史显示
        function updateEventHistoryDisplay() {
            const historyEl = document.getElementById('event-history');
            if (eventHistory.length === 0) {
                historyEl.innerHTML = '<div class="loading">暂无事件历史记录</div>';
                return;
            }

            const historyHTML = eventHistory.map(event => {
                const time = new Date(event.timestamp).toLocaleTimeString();
                return `
                    <div class="event-item ${event.type.toLowerCase()}">
                        <div class="event-header">
                            <span class="event-type ${event.type.toLowerCase()}">${event.type}</span>
                            <span class="event-time">${time}</span>
                        </div>
                        <div class="event-details">
                            <strong>事件:</strong> ${event.type} | <strong>来源:</strong> ${event.source}
                        </div>
                        <div class="event-payload">${JSON.stringify(event.payload, null, 2)}</div>
                    </div>
                `;
            }).join('');

            historyEl.innerHTML = historyHTML;
        }

        // 监听来自父窗口的消息
        window.addEventListener('message', (event) => {
            const msg = event.data;
            if (!msg || !msg.type) return;

            switch (msg.type) {
                case 'init-plugin':
                    console.log('事件总线测试插件已初始化');
                    updateStatus('插件已初始化', 'success');
                    loadEventHistory();
                    break;

                case 'event-bus-response':
                    const { command, success, data, error } = msg.payload;
                    if (success) {
                        updateStatus(`${command} 执行成功`, 'success');
                        if (command === 'get_event_history') {
                            eventHistory = data || [];
                            updateEventHistoryDisplay();
                        } else if (command === 'clear_event_history') {
                            eventHistory = [];
                            updateEventHistoryDisplay();
                        } else {
                            // 对于发送事件的命令，添加到历史记录
                            if (data && data.event_type) {
                                addEventToHistory(data.event_type, data.payload, data.source);
                            }
                        }
                    } else {
                        updateStatus(`${command} 执行失败: ${error}`, 'error');
                    }
                    break;
            }
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            updateStatus('准备就绪', 'info');
        });
    </script>
</body>

</html>