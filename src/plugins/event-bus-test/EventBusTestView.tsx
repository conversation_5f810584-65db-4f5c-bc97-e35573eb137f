import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { listen } from '@tauri-apps/api/event';

interface EventHistoryItem {
    id: string;
    timestamp: number;
    type: string;
    payload: any;
    source: string;
}

export const EventBusTestView: React.FC = () => {
    const [eventHistory, setEventHistory] = useState<EventHistoryItem[]>([]);
    const [status, setStatus] = useState<{ message: string; type: string }>({ message: '就绪', type: 'info' });
    const [autoRefreshEnabled, setAutoRefreshEnabled] = useState(false);
    const [autoRefreshInterval, setAutoRefreshInterval] = useState<NodeJS.Timeout | null>(null);

    // 状态显示函数
    const updateStatus = (message: string, type: string = 'info') => {
        setStatus({ message, type });
    };

    // 添加事件到历史记录
    const addEventToHistory = (type: string, payload: any, source: string) => {
        const event: EventHistoryItem = {
            id: Date.now().toString(),
            timestamp: Date.now(),
            type,
            payload,
            source
        };
        setEventHistory(prev => {
            const newHistory = [event, ...prev];
            return newHistory.slice(0, 50); // 只保留最近50条记录
        });
        console.log('新事件:', event);
    };

    // 发送事件的通用函数
    const sendEvent = async (command: string, eventData: any) => {
        try {
            updateStatus('正在发送事件...', 'info');
            const result = await invoke(command, eventData);
            updateStatus('事件发送成功', 'success');
            addEventToHistory(eventData.event_type || 'unknown', eventData, 'test-plugin');
            return result;
        } catch (error) {
            console.error('发送事件失败:', error);
            updateStatus(`发送事件失败: ${error}`, 'error');
            throw error;
        }
    };

    // 测试自定义事件
    const testCustomEvent = async () => {
        const eventData = {
            event_name: 'test-custom-event',
            event_type: 'Custom',
            payload: {
                message: 'Hello from Event Bus Test!',
                timestamp: Date.now(),
                testData: { value: 42, enabled: true }
            },
            source: 'event-bus-test-plugin'
        };
        await sendEvent('emit_custom_event', eventData);
    };

    // 测试UI事件
    const testUIEvent = async () => {
        const eventData = {
            event_name: 'test-ui-event',
            event_type: 'UI',
            payload: {
                action: 'window-resize',
                width: 800,
                height: 600,
                timestamp: Date.now()
            },
            source: 'event-bus-test-plugin'
        };
        await sendEvent('emit_ui_event', eventData);
    };

    // 模拟搜索查询
    const simulateSearchQuery = async () => {
        const eventData = {
            event_name: 'search-query-changed',
            event_type: 'UI',
            payload: {
                query: 'test search query',
                timestamp: Date.now(),
                context: 'main-search'
            },
            source: 'event-bus-test-plugin'
        };
        await sendEvent('emit_ui_event', eventData);
    };

    // 模拟主题变化
    const simulateThemeChange = async () => {
        const eventData = {
            event_name: 'theme-changed',
            event_type: 'UI',
            payload: {
                theme: Math.random() > 0.5 ? 'dark' : 'light',
                timestamp: Date.now(),
                source: 'settings'
            },
            source: 'event-bus-test-plugin'
        };
        await sendEvent('emit_ui_event', eventData);
    };

    // 模拟窗口变化
    const simulateWindowChange = async () => {
        const eventData = {
            event_name: 'window-state-changed',
            event_type: 'UI',
            payload: {
                windowId: 'main',
                state: 'focused',
                timestamp: Date.now(),
                metadata: { width: 900, height: 700 }
            },
            source: 'event-bus-test-plugin'
        };
        await sendEvent('emit_ui_event', eventData);
    };

    // 模拟插件执行完成
    const simulatePluginExecution = async () => {
        const eventData = {
            event_name: 'plugin-execution-completed',
            event_type: 'Plugin',
            payload: {
                pluginId: 'calculator',
                result: '计算结果: 42',
                duration: 150,
                timestamp: Date.now()
            },
            source: 'event-bus-test-plugin'
        };
        await sendEvent('emit_plugin_event', eventData);
    };

    // 模拟插件安装
    const simulatePluginInstall = async () => {
        const eventData = {
            event_name: 'plugin-installed',
            event_type: 'Plugin',
            payload: {
                pluginId: 'new-test-plugin',
                version: '1.0.0',
                timestamp: Date.now(),
                source: 'store'
            },
            source: 'event-bus-test-plugin'
        };
        await sendEvent('emit_plugin_event', eventData);
    };

    // 模拟插件启用/禁用
    const simulatePluginToggle = async () => {
        const eventData = {
            event_name: 'plugin-toggled',
            event_type: 'Plugin',
            payload: {
                pluginId: 'calculator',
                enabled: Math.random() > 0.5,
                timestamp: Date.now(),
                triggeredBy: 'user'
            },
            source: 'event-bus-test-plugin'
        };
        await sendEvent('emit_plugin_event', eventData);
    };

    // 模拟系统事件
    const simulateShortcut = async () => {
        const eventData = {
            event_name: 'global-shortcut-triggered',
            event_type: 'System',
            payload: {
                shortcut: 'Cmd+Space',
                timestamp: Date.now(),
                context: 'global'
            },
            source: 'event-bus-test-plugin'
        };
        await sendEvent('emit_system_event', eventData);
    };

    const simulateClipboardChange = async () => {
        const eventData = {
            event_name: 'clipboard-changed',
            event_type: 'System',
            payload: {
                content: 'Hello from clipboard!',
                timestamp: Date.now(),
                format: 'text'
            },
            source: 'event-bus-test-plugin'
        };
        await sendEvent('emit_system_event', eventData);
    };

    const simulateFileChange = async () => {
        const eventData = {
            event_name: 'file-changed',
            event_type: 'System',
            payload: {
                path: '/Users/<USER>/documents/test.txt',
                action: 'modified',
                timestamp: Date.now(),
                size: 1024
            },
            source: 'event-bus-test-plugin'
        };
        await sendEvent('emit_system_event', eventData);
    };

    // 加载事件历史
    const loadEventHistory = async () => {
        try {
            updateStatus('正在加载事件历史...', 'info');
            const history = await invoke('get_event_history') as EventHistoryItem[];
            setEventHistory(history);
            updateStatus(`已加载 ${history.length} 条事件历史`, 'success');
        } catch (error) {
            console.error('加载事件历史失败:', error);
            updateStatus(`加载事件历史失败: ${error}`, 'error');
        }
    };

    // 清空事件历史
    const clearEventHistory = async () => {
        try {
            updateStatus('正在清空事件历史...', 'info');
            await invoke('clear_event_history');
            setEventHistory([]);
            updateStatus('事件历史已清空', 'success');
        } catch (error) {
            console.error('清空事件历史失败:', error);
            updateStatus(`清空事件历史失败: ${error}`, 'error');
        }
    };

    // 切换自动刷新
    const toggleAutoRefresh = () => {
        if (autoRefreshEnabled) {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                setAutoRefreshInterval(null);
            }
            setAutoRefreshEnabled(false);
            updateStatus('自动刷新已禁用', 'info');
        } else {
            const interval = setInterval(() => {
                loadEventHistory();
            }, 2000);
            setAutoRefreshInterval(interval);
            setAutoRefreshEnabled(true);
            updateStatus('自动刷新已启用', 'success');
        }
    };

    // 格式化时间戳
    const formatTimestamp = (timestamp: number) => {
        return new Date(timestamp).toLocaleTimeString();
    };

    // 获取事件类型颜色
    const getEventTypeColor = (type: string) => {
        switch (type) {
            case 'System': return '#ff6b6b';
            case 'UI': return '#4ecdc4';
            case 'Plugin': return '#45b7d1';
            case 'Custom': return '#96ceb4';
            default: return '#666';
        }
    };

    // 组件挂载时加载事件历史
    useEffect(() => {
        loadEventHistory();
        return () => {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
        };
    }, []);

    return (
        <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif', maxWidth: '800px', margin: '0 auto' }}>
            <div style={{ marginBottom: '20px' }}>
                <h2 style={{ color: '#333', marginBottom: '10px' }}>🚀 事件总线测试面板</h2>
                <div style={{
                    padding: '10px',
                    borderRadius: '5px',
                    backgroundColor: status.type === 'success' ? '#d4edda' : status.type === 'error' ? '#f8d7da' : '#d1ecf1',
                    color: status.type === 'success' ? '#155724' : status.type === 'error' ? '#721c24' : '#0c5460',
                    marginBottom: '20px'
                }}>
                    状态: {status.message}
                </div>
            </div>

            {/* 自定义事件 */}
            <div style={{ marginBottom: '30px', padding: '20px', border: '1px solid #ddd', borderRadius: '8px' }}>
                <h3 style={{ color: '#333', marginBottom: '15px' }}>🎯 自定义事件</h3>
                <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
                    <button onClick={testCustomEvent} style={{ padding: '10px 20px', backgroundColor: '#007bff', color: 'white', border: 'none', borderRadius: '5px', cursor: 'pointer' }}>
                        发送自定义事件
                    </button>
                </div>
            </div>

            {/* UI事件 */}
            <div style={{ marginBottom: '30px', padding: '20px', border: '1px solid #ddd', borderRadius: '8px' }}>
                <h3 style={{ color: '#333', marginBottom: '15px' }}>🖥️ UI事件</h3>
                <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
                    <button onClick={testUIEvent} style={{ padding: '10px 20px', backgroundColor: '#28a745', color: 'white', border: 'none', borderRadius: '5px', cursor: 'pointer' }}>
                        测试UI事件
                    </button>
                    <button onClick={simulateSearchQuery} style={{ padding: '10px 20px', backgroundColor: '#17a2b8', color: 'white', border: 'none', borderRadius: '5px', cursor: 'pointer' }}>
                        模拟搜索查询
                    </button>
                    <button onClick={simulateThemeChange} style={{ padding: '10px 20px', backgroundColor: '#ffc107', color: 'black', border: 'none', borderRadius: '5px', cursor: 'pointer' }}>
                        模拟主题变化
                    </button>
                    <button onClick={simulateWindowChange} style={{ padding: '10px 20px', backgroundColor: '#6c757d', color: 'white', border: 'none', borderRadius: '5px', cursor: 'pointer' }}>
                        模拟窗口变化
                    </button>
                </div>
            </div>

            {/* 插件事件 */}
            <div style={{ marginBottom: '30px', padding: '20px', border: '1px solid #ddd', borderRadius: '8px' }}>
                <h3 style={{ color: '#333', marginBottom: '15px' }}>🔌 插件事件</h3>
                <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
                    <button onClick={simulatePluginExecution} style={{ padding: '10px 20px', backgroundColor: '#28a745', color: 'white', border: 'none', borderRadius: '5px', cursor: 'pointer' }}>
                        模拟插件执行完成
                    </button>
                    <button onClick={simulatePluginInstall} style={{ padding: '10px 20px', backgroundColor: '#007bff', color: 'white', border: 'none', borderRadius: '5px', cursor: 'pointer' }}>
                        模拟插件安装
                    </button>
                    <button onClick={simulatePluginToggle} style={{ padding: '10px 20px', backgroundColor: '#6c757d', color: 'white', border: 'none', borderRadius: '5px', cursor: 'pointer' }}>
                        模拟插件启用/禁用
                    </button>
                </div>
            </div>

            {/* 系统事件 */}
            <div style={{ marginBottom: '30px', padding: '20px', border: '1px solid #ddd', borderRadius: '8px' }}>
                <h3 style={{ color: '#333', marginBottom: '15px' }}>⚙️ 系统事件</h3>
                <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
                    <button onClick={simulateShortcut} style={{ padding: '10px 20px', backgroundColor: '#dc3545', color: 'white', border: 'none', borderRadius: '5px', cursor: 'pointer' }}>
                        模拟全局快捷键
                    </button>
                    <button onClick={simulateClipboardChange} style={{ padding: '10px 20px', backgroundColor: '#fd7e14', color: 'white', border: 'none', borderRadius: '5px', cursor: 'pointer' }}>
                        模拟剪贴板变化
                    </button>
                    <button onClick={simulateFileChange} style={{ padding: '10px 20px', backgroundColor: '#6f42c1', color: 'white', border: 'none', borderRadius: '5px', cursor: 'pointer' }}>
                        模拟文件变化
                    </button>
                </div>
            </div>

            {/* 事件历史 */}
            <div style={{ marginBottom: '30px', padding: '20px', border: '1px solid #ddd', borderRadius: '8px' }}>
                <h3 style={{ color: '#333', marginBottom: '15px' }}>📊 事件历史</h3>
                <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap', marginBottom: '20px' }}>
                    <button onClick={loadEventHistory} style={{ padding: '10px 20px', backgroundColor: '#007bff', color: 'white', border: 'none', borderRadius: '5px', cursor: 'pointer' }}>
                        刷新历史
                    </button>
                    <button onClick={clearEventHistory} style={{ padding: '10px 20px', backgroundColor: '#6c757d', color: 'white', border: 'none', borderRadius: '5px', cursor: 'pointer' }}>
                        清空历史
                    </button>
                    <button onClick={toggleAutoRefresh} style={{ padding: '10px 20px', backgroundColor: autoRefreshEnabled ? '#dc3545' : '#28a745', color: 'white', border: 'none', borderRadius: '5px', cursor: 'pointer' }}>
                        {autoRefreshEnabled ? '禁用自动刷新' : '启用自动刷新'}
                    </button>
                </div>

                <div style={{ maxHeight: '400px', overflowY: 'auto', border: '1px solid #eee', borderRadius: '5px', padding: '10px' }}>
                    {eventHistory.length === 0 ? (
                        <div style={{ textAlign: 'center', color: '#666', padding: '20px' }}>暂无事件历史记录</div>
                    ) : (
                        eventHistory.map((event) => (
                            <div key={event.id} style={{ marginBottom: '10px', padding: '10px', border: '1px solid #eee', borderRadius: '5px', backgroundColor: '#f8f9fa' }}>
                                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '5px' }}>
                                    <span style={{ fontWeight: 'bold', color: getEventTypeColor(event.type) }}>{event.type}</span>
                                    <span style={{ fontSize: '12px', color: '#666' }}>{formatTimestamp(event.timestamp)}</span>
                                </div>
                                <div style={{ fontSize: '14px', marginBottom: '5px' }}>
                                    <strong>事件:</strong> {event.type}
                                </div>
                                <div style={{ fontSize: '14px', marginBottom: '5px' }}>
                                    <strong>来源:</strong> {event.source}
                                </div>
                                <div style={{ fontSize: '12px', color: '#666', backgroundColor: '#fff', padding: '5px', borderRadius: '3px' }}>
                                    <strong>数据:</strong> {JSON.stringify(event.payload, null, 2)}
                                </div>
                            </div>
                        ))
                    )}
                </div>
            </div>
        </div>
    );
};

export default EventBusTestView;