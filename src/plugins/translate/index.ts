import { ListPlugin, ListPluginRunner } from '../../core/types';
import { ai } from '../../core/ai';
import { GenerateContentResponse } from '@google/genai';

const run: ListPluginRunner = async ({ input, getSetting }) => {
    if (!input) {
        return "Error: Please provide text to translate.";
    }

    const targetLanguage = getSetting('targetLanguage') || 'English';
    const prompt = `Translate the following text to ${targetLanguage}: "${input}"`;

    try {
        const response: GenerateContentResponse = await ai.models.generateContent({ model: 'gemini-2.5-flash-preview-04-17', contents: prompt });
        return response.text;
    } catch(e) {
        const errorMessage = e instanceof Error ? e.message : String(e);
        console.error("Error calling AI model for Translate plugin:", e);
        return `Error calling AI model: ${errorMessage}`;
    }
};

export const translatePlugin: ListPlugin = { 
    id: 'agent-translate', 
    name: 'Translate', 
    icon: 'Languages', 
    mode: 'list', 
    keyword: 'translate', 
    placeholderSuffix: 'text to translate...', 
    description: 'Translate text to a specified language.',
    settings: [
        { id: 'apiKey', title: 'API Key', description: 'Your personal API key for the translation service.', type: 'password', defaultValue: '' },
        { id: 'targetLanguage', title: 'Target Language', description: 'The language to translate the text into.', type: 'string', defaultValue: 'English' }
    ],
    run
};
