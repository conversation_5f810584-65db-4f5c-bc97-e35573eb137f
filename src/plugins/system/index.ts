import { ListPlugin, ListPluginRunner } from '../../core/types';

const run: ListPluginRunner = async ({ action }) => {
    if (action?.id === 'action-screenshot') return 'UI_ACTION::screenshot';
    if (action?.id === 'action-upload') return 'UI_ACTION::upload';
    return 'Error: Unknown system action.';
};

export const systemPlugin: ListPlugin = {
    id: 'system-tools',
    name: 'System Tools',
    icon: 'Cog',
    mode: 'list',
    keyword: '',
    description: 'Core system functions.',
    actions: [
      { id: 'action-screenshot', name: 'Take Screenshot', pattern: '^screenshot$', description: 'Capture a portion of the screen' },
      { id: 'action-upload', name: 'Upload File', pattern: '^upload$', description: 'Upload a file for processing' }
    ],
    run
};
