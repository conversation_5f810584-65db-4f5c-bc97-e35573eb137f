import { ListPlugin, ListPluginRunner, ListItem } from '../../core/types';

const run: ListPluginRunner = async () => {
    const items: ListItem[] = [
        { id: 'def-1', title: 'Extensible', description: '(adj) capable of being extended or expanded.', icon: 'Book', onSelectAction: { type: 'copy', payload: 'Extensible: (adj) capable of being extended or expanded.' } },
        { id: 'def-2', title: 'Launcher', description: '(n) a program used to start other programs.', icon: 'Book', onSelectAction: { type: 'copy', payload: 'Launcher: (n) a program used to start other programs.' } }
    ];
    return `LIST_MODE_RESULTS::${JSON.stringify({ items })}`;
};

export const dictionaryPlugin: ListPlugin = { 
    id: 'list-dictionary', 
    name: 'Dictionary', 
    icon: 'Book', 
    mode: 'list', 
    keyword: 'define', 
    placeholderSuffix: 'a word to define...', 
    description: 'Look up a word in the dictionary.',
    run
};
