import { ListPlugin, ListPluginRunner, ListItem } from '../../core/types';

const run: ListPluginRunner = async ({ action }) => {
    const items: ListItem[] = [
        { id: 'theme-light', icon: 'Sun', title: 'Switch to Light Mode', onSelectAction: { type: 'set-theme', payload: 'light' } },
        { id: 'theme-dark', icon: 'Moon', title: 'Switch to Dark Mode', onSelectAction: { type: 'set-theme', payload: 'dark' } },
        { id: 'theme-system', icon: 'Laptop', title: 'Use System Setting', onSelectAction: { type: 'set-theme', payload: 'system' } },
     ];
     
     // Special handling for direct actions
     if (action?.id === 'action-theme-light') {
        return `LIST_MODE_RESULTS::${JSON.stringify({ items: [items[0]] })}`;
     }
     if (action?.id === 'action-theme-dark') {
        return `LIST_MODE_RESULTS::${JSON.stringify({ items: [items[1]] })}`;
     }

     // Default behavior: show all options
     return `LIST_MODE_RESULTS::${JSON.stringify({ items })}`;
};

export const themePlugin: ListPlugin = {
    id: 'list-theme-switcher', 
    name: 'Switch Theme', 
    icon: 'SunMoon', 
    mode: 'list', 
    keyword: 'theme', 
    description: 'Change the application theme.',
    actions: [
        { id: 'action-theme-light', name: 'Switch to Light Theme', pattern: '^(theme light|light theme)$' },
        { id: 'action-theme-dark', name: 'Switch to Dark Theme', pattern: '^(theme dark|dark theme)$' },
    ],
    run
};
