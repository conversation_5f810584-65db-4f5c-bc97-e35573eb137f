import { ListPlugin, ListPluginRunner } from '../../core/types';

const run: ListPluginRunner = async ({ input, getSetting }) => {
    try {
        // A simple, unsafe eval for demonstration. A real implementation should use a proper math parser.
        // eslint-disable-next-line no-eval
        const resultValue = eval(input);
        const precision = parseInt(getSetting('precision') || '2', 10);
        const formattedResult = Number(resultValue).toFixed(precision);
        const result = { items: [{ id: 'calc-result', icon: 'Calculator', title: formattedResult, description: `Result of: ${input}`, preview: { type: 'text', content: `Expression: ${input}\nResult: ${formattedResult}`}, onSelectAction: { type: 'copy', payload: formattedResult } }] };
        return `LIST_MODE_RESULTS::${JSON.stringify(result)}`;
    } catch (e) {
        const errorResult = { items: [{ id: 'calc-error', icon: 'XCircle', title: 'Invalid Expression', description: 'Could not evaluate the mathematical expression.', onSelectAction: { type: 'copy', payload: '' } }] };
        return `LIST_MODE_RESULTS::${JSON.stringify(errorResult)}`;
    }
};

export const calculatorPlugin: ListPlugin = {
  id: 'list-calculator', 
  name: 'Calculator', 
  icon: 'Calculator', 
  mode: 'list', 
  keyword: '', // Keyword is empty as it's action-driven now
  description: 'Evaluate a mathematical expression.',
  aliases: ['calc'],
  actions: [
    { id: 'action-calculate', name: 'Calculate Expression', pattern: '^=(.*)', description: 'e.g. =(2+2)*4' }
  ],
  settings: [
      { id: 'precision', title: 'Result Precision', description: 'Number of decimal places for the result.', type: 'string', defaultValue: '2' },
      { id: 'showHint', title: 'Show Action Hint', description: 'Display "Press Enter to copy" in the preview.', type: 'boolean', defaultValue: true }
  ],
  run,
};
