import { ListPlugin, ListPluginRunner, ListItem } from '../../core/types';

const MOCK_CLIPBOARD_DB: { id: number, content: string, type: 'text' | 'image' }[] = [
    { id: 1, content: 'https://github.com/google/gemini-api', type: 'text' },
    { id: 2, content: 'NovaRay: An extensible launcher built with modern web technologies.', type: 'text' },
    { id: 3, content: `{\n  "id": "list-clipboard-history",\n  "name": "Clipboard History",\n  "icon": "ClipboardList",\n  "mode": "list",\n  "keyword": "clipboard"\n}`, type: 'text' }
];

const run: ListPluginRunner = async () => {
    const items: ListItem[] = MOCK_CLIPBOARD_DB.map(entry => ({
        id: `clip-${entry.id}`,
        icon: 'Clipboard',
        title: entry.content.length > 80 ? entry.content.substring(0, 80) + '...' : entry.content,
        description: `Type: ${entry.type}`,
        preview: { type: 'text', content: entry.content },
        onSelectAction: { type: 'copy', payload: entry.content }
    }));
    return `LIST_MODE_RESULTS::${JSON.stringify({ items })}`;
};

export const clipboardPlugin: ListPlugin = {
  id: 'list-clipboard-history',
  name: 'Clipboard History',
  icon: 'ClipboardList',
  mode: 'list',
  keyword: 'clipboard',
  description: 'View and use your clipboard history.',
  run
};
