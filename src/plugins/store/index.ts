import { ListPlugin, ListPluginRunner, <PERSON><PERSON>tem, Plugin, RemotePlugin } from '../../core/types';
import { getPlugins } from '../../core/pluginRegistry';

// In a real app, this would be fetched from a remote server.
const MOCK_REMOTE_PLUGIN_REPO: RemotePlugin[] = [
    // We'll dynamically add the currently "installed" plugins to this list
    // to simulate a comprehensive store view.
    {
        id: 'view-webapp-demo', name: 'Web App Demo', icon: 'https://cdn.dribbble.com/users/128483/screenshots/2347274/al-dente.png', mode: 'view', keyword: 'webapp-demo', description: 'A demo of a self-contained web app as a plugin.',
        author: 'NovaRay Team', version: '1.1.0',
        readme: `# Web App Demo Plugin\n\nThis plugin demonstrates how a fully self-contained web application can be integrated into NovaRay. It has its own HTML, CSS, and JS.\n\n- **No External Dependencies**\n- **Sandboxed Environment**`,
        screenshots: [
            "https://i.imgur.com/gS4a3bM.png",
            "https://i.imgur.com/O1x9VZQ.png",
            "https://i.imgur.com/gS4a3bM.png",
        ]
    },
    {
        id: 'agent-deep-research', name: 'Deep Research', icon: 'Search', mode: 'list', keyword: 'research', placeholderSuffix: 'topic for Deep Research...', description: 'Perform in-depth research.',
        author: 'Community Contributor', version: '0.9.0',
        readme: `# Deep Research Agent\n\nThis agent uses AI to perform in-depth research on a given topic. It is currently in beta.`,
        run: async () => "Error: This plugin is not installed and cannot be run.",
        screenshots: [
            "https://i.imgur.com/O1x9VZQ.png",
            "https://i.imgur.com/gS4a3bM.png",
        ]
    }
];

const run: ListPluginRunner = async () => {
    const installedPlugins = getPlugins();
    const installedPluginIds = new Set(installedPlugins.map(p => p.id));

    const allStorePlugins = [ ...MOCK_REMOTE_PLUGIN_REPO ];
    
    // Add installed plugins to the store view if they aren't already there from the remote repo
    installedPlugins.forEach(p => {
        if (!allStorePlugins.some(sp => sp.id === p.id) && p.id !== 'system-tools') {
            allStorePlugins.unshift({
                ...p,
                author: 'NovaRay Team',
                version: '1.0.0',
                readme: `# ${p.name}\n\nThis is a core plugin for NovaRay, providing essential functionality.\n\n**Version**: 1.0.0\n**Author**: NovaRay Team`,
            });
        }
    });

    const items: ListItem[] = allStorePlugins.map(plugin => {
        const isInstalled = installedPluginIds.has(plugin.id);
        const actionsSubMenu: ListItem[] = [
            isInstalled 
                ? { id: `uninstall-${plugin.id}`, icon: 'Trash2', title: 'Uninstall Plugin', onSelectAction: { type: 'uninstall-plugin', payload: plugin.id } }
                : { id: `install-${plugin.id}`, icon: 'Download', title: 'Install Plugin', onSelectAction: { type: 'install-plugin', payload: plugin.id } },
            { id: `details-${plugin.id}`, icon: 'Info', title: 'View Details', onSelectAction: { type: 'copy', payload: `Version: ${plugin.version}\nAuthor: ${plugin.author}`}},
        ];

        return {
            id: plugin.id,
            icon: plugin.icon,
            title: plugin.name,
            description: plugin.description || '',
            preview: {
                type: 'markdown',
                content: plugin.readme,
                author: plugin.author,
                version: plugin.version,
                screenshots: plugin.screenshots,
            },
            onSelectAction: { type: 'show-list', payload: actionsSubMenu },
            isInstalled: isInstalled,
        };
    });
    const result = { items };
    return `LIST_MODE_RESULTS::${JSON.stringify(result)}`;
};


export const storePlugin: ListPlugin = {
    id: 'list-plugin-store',
    name: 'Plugin Store',
    icon: 'Store',
    mode: 'list',
    keyword: 'store',
    description: 'Discover and manage plugins.',
    run
};

// This function simulates the backend action of installing/uninstalling a plugin.
// In a real Tauri app, this would be a call to the Rust backend.
export const handleStoreSpecialAction = (action: { type: string, payload: any }): boolean => {
    if (action.type === 'install-plugin' || action.type === 'uninstall-plugin') {
        alert(`Simulating "${action.type}" for plugin "${action.payload}".\nIn a real app, this would modify the backend and the app would refresh.`);
        // Returning true tells the app the action was "handled" and it can proceed.
        return true;
    }
    return false;
}