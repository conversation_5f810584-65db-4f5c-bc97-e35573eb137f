import { ListPlugin } from '../../core/types';

export const windowManagerPlugin: ListPlugin = {
    id: 'window-manager',
    name: '窗口管理器',
    description: '管理应用程序窗口和布局',
    mode: 'list',
    keyword: 'window',
    icon: '🪟',
    actions: [
        {
            id: 'open-window-manager',
            name: '打开窗口管理器',
            pattern: '.*',
            isInteractive: false
        }
    ],
    async run({ input, action, getSetting }) {
        if (action.id === 'open-window-manager') {
            return 'UI_ACTION::open-window-manager';
        }
        return 'Error: Unknown action';
    }
};