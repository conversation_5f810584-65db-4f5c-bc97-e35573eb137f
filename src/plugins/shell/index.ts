import { ViewPlugin } from '../../core/types';

export const shellPlugin: ViewPlugin = { 
  id: 'view-shell', 
  name: 'Shell', 
  icon: 'Terminal', 
  mode: 'view', 
  keyword: 'shell', 
  description: 'Execute shell commands.',
  aliases: ['$', '>'],
  settings: [
      { 
          id: 'defaultShell', 
          title: 'Default Shell', 
          description: 'The shell to use for executing commands.', 
          type: 'select', 
          defaultValue: 'bash',
          options: [
              { label: 'Bash', value: 'bash' },
              { label: 'Zsh', value: 'zsh' },
              { label: 'PowerShell', value: 'powershell' },
              { label: 'CMD', value: 'cmd' },
          ]
      }
  ]
};
