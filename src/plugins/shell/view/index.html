<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Shell Plugin</title>
  <style>
    :root {
      --bg-color: #1e1e1e;
      --text-color: #d4d4d4;
      --prompt-color: #61afef;
      --cursor-color: #d4d4d4;
    }
    html, body {
      margin: 0;
      padding: 0;
      height: 100%;
      width: 100%;
      overflow: hidden;
    }
    body {
      background-color: var(--bg-color);
      color: var(--text-color);
      font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
      font-size: 13px;
      line-height: 1.5;
    }
    #terminal {
      height: 100%;
      width: 100%;
      padding: 10px;
      box-sizing: border-box;
      overflow-y: auto;
      cursor: text;
      position: relative;
    }
    #terminal:focus {
      outline: none;
    }
    #output {
      white-space: pre-wrap;
      word-break: break-all;
    }
    .input-line {
      display: flex;
      align-items: center;
    }
    .prompt {
      color: var(--prompt-color);
      margin-right: 8px;
    }
    #input {
      background: transparent;
      border: none;
      color: var(--text-color);
      font: inherit;
      padding: 0;
      flex-grow: 1;
      caret-color: var(--cursor-color);
    }
    #input:focus {
      outline: none;
    }
    .command-echo {
      display: flex;
    }
  </style>
</head>
<body>

  <div id="terminal" tabindex="0">
    <div id="output"></div>
    <div class="input-line">
      <span class="prompt" id="prompt-span">&gt;</span>
      <input type="text" id="input" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" autofocus />
    </div>
  </div>

  <script>
    const terminal = document.getElementById('terminal');
    const output = document.getElementById('output');
    const input = document.getElementById('input');
    const promptSpan = document.getElementById('prompt-span');

    let commandHistory = [];
    let historyIndex = -1;
    let shell = 'bash'; // default

    const scrollToBottom = () => {
      terminal.scrollTop = terminal.scrollHeight;
    };

    const newPrompt = () => {
      input.value = '';
      input.disabled = false;
      input.focus();
      historyIndex = -1; // Reset history navigation
      scrollToBottom();
    };
    
    const appendOutput = (text) => {
        output.append(document.createTextNode(text));
        scrollToBottom();
    };

    const runCommand = (command) => {
      if(command.trim() === '') {
        newPrompt();
        return;
      }

      // Echo command
      const echo = document.createElement('div');
      echo.className = 'command-echo';
      echo.innerHTML = `<span class="prompt">${promptSpan.textContent}</span><span></span>`;
      echo.lastChild.textContent = command;
      output.appendChild(echo);
      
      commandHistory.unshift(command);
      input.disabled = true;

      // Handle local 'clear' command
      if (command.trim().toLowerCase() === 'clear') {
        output.innerHTML = '';
        newPrompt();
        return;
      }

      window.parent.postMessage({
        type: 'run-shell-command',
        payload: { command: command, shell: shell }
      }, '*');
    };

    // --- Event Listeners ---

    input.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') {
        e.preventDefault();
        runCommand(input.value);
      } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        if (historyIndex < commandHistory.length - 1) {
          historyIndex++;
          input.value = commandHistory[historyIndex];
        }
      } else if (e.key === 'ArrowDown') {
        e.preventDefault();
        if (historyIndex > 0) {
          historyIndex--;
          input.value = commandHistory[historyIndex];
        } else if (historyIndex === 0) {
            historyIndex = -1;
            input.value = '';
        }
      }
    });

    terminal.addEventListener('click', () => {
      input.focus();
    });

    window.addEventListener('message', (event) => {
      const msg = event.data;
      if (!msg || !msg.type) return;

      switch(msg.type) {
        case 'init-plugin':
          // Here you could get config from the main app, e.g. settings
          console.log('Shell plugin initialized with payload:', msg.payload);
          appendOutput('Welcome to NovaRay Shell!\n\n');
          break;
        
        case 'shell-output':
          if (msg.payload.stdout) {
            appendOutput(msg.payload.stdout + '\n');
          }
          if (msg.payload.stderr) {
            appendOutput(msg.payload.stderr + '\n');
          }
          if (msg.payload.command_done) {
            newPrompt();
          }
          break;
      }
    });

    // Initial focus
    input.focus();

  </script>

</body>
</html>
