<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Notepad Plugin</title>
  <style>
    :root {
      --primary-color: #1a73e8;
      --background-color: #ffffff;
      --text-color: #202124;
      --border-color: #dadce0;
      --button-hover: #f1f3f4;
    }
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
      margin: 0;
      background-color: var(--background-color);
      color: var(--text-color);
      display: flex;
      flex-direction: column;
      height: 100vh;
      box-sizing: border-box;
    }
    .container {
      padding: 16px;
      display: flex;
      flex-direction: column;
      flex-grow: 1;
    }
    h1 {
      font-size: 1.2rem;
      margin-top: 0;
      margin-bottom: 12px;
      color: #3c4043;
    }
    textarea {
      flex-grow: 1;
      width: 100%;
      box-sizing: border-box;
      border: 1px solid var(--border-color);
      border-radius: 8px;
      padding: 12px;
      font-size: 14px;
      line-height: 1.5;
      font-family: inherit;
      resize: none;
      margin-bottom: 12px;
    }
    textarea:focus {
        outline: 2px solid var(--primary-color);
        border-color: transparent;
    }
    .actions {
      display: flex;
      justify-content: flex-end;
    }
    button {
      background-color: var(--primary-color);
      color: white;
      border: none;
      padding: 10px 20px;
      font-size: 14px;
      font-weight: 500;
      border-radius: 20px;
      cursor: pointer;
      transition: opacity 0.2s ease;
    }
    button:hover {
      opacity: 0.9;
    }
  </style>
</head>
<body>

  <div class="container">
    <h1>Notepad</h1>
    <textarea id="notepad-area" placeholder="Write your notes here..."></textarea>
    <div class="actions">
      <button id="save-btn">Save & Close</button>
    </div>
  </div>

  <script>
    const notepad = document.getElementById('notepad-area');
    const saveButton = document.getElementById('save-btn');

    // 1. Listen for initialization message from the main app
    window.addEventListener('message', (event) => {
      // It's good practice to check the origin, but skipped here for simplicity in local dev
      const message = event.data;
      if (message && message.type === 'init-plugin') {
        if (message.payload) {
          notepad.value = message.payload;
        }
      }
    });

    // 2. Send messages back to the main app on button click
    saveButton.addEventListener('click', () => {
      const currentText = notepad.value;
      
      // Send a message to set the main input's value
      window.parent.postMessage({
        type: 'set-input-value',
        payload: currentText
      }, '*'); // Replace '*' with target origin in production

      // Send a message to close the view
      window.parent.postMessage({
        type: 'close-view'
      }, '*');
    });
  </script>

</body>
</html>