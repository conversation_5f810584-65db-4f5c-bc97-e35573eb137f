<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>View Plugin Demo</title>
  <style>
    :root {
      --primary-color: #1a73e8;
      --background-color: #f7f7f8;
      --text-color: #202124;
    }
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: var(--background-color);
      color: var(--text-color);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
      box-sizing: border-box;
      text-align: center;
    }
    h1 {
      color: var(--primary-color);
      margin-bottom: 8px;
    }
    p {
      margin-top: 0;
      margin-bottom: 24px;
      max-width: 300px;
    }
    .counter-display {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 16px;
        color: #333;
    }
    button {
      background-color: var(--primary-color);
      color: white;
      border: none;
      padding: 12px 24px;
      font-size: 16px;
      font-weight: 500;
      border-radius: 24px;
      cursor: pointer;
      transition: transform 0.1s ease, box-shadow 0.2s ease;
    }
    button:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }
    button:active {
        transform: scale(0.98);
    }
  </style>
</head>
<body>

  <h1>Custom Plugin View</h1>
  <p>This entire view is an independent HTML file running inside an iframe, completely sandboxed from the main app.</p>
  
  <div id="counter" class="counter-display">0</div>
  
  <button id="increment-btn">Click Me!</button>

  <script>
    const counterDisplay = document.getElementById('counter');
    const incrementBtn = document.getElementById('increment-btn');
    let count = 0;

    incrementBtn.addEventListener('click', () => {
      count++;
      counterDisplay.textContent = count;
    });
  </script>

</body>
</html>
