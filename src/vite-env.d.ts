/// <reference types="vite/client" />

declare module '*.css' {
    const content: string;
    export default content;
}

declare module '*.module.css' {
    const classes: { [key: string]: string };
    export default classes;
}

declare module '*.scss' {
    const content: string;
    export default content;
}

declare module '*.module.scss' {
    const classes: { [key: string]: string };
    export default classes;
}

declare module '*.sass' {
    const content: string;
    export default content;
}

declare module '*.module.sass' {
    const classes: { [key: string]: string };
    export default classes;
}

declare module '*.less' {
    const content: string;
    export default content;
}

declare module '*.module.less' {
    const classes: { [key: string]: string };
    export default classes;
}

declare module '*.styl' {
    const content: string;
    export default content;
}

declare module '*.module.styl' {
    const classes: { [key: string]: string };
    export default classes;
}

declare module '*.stylus' {
    const content: string;
    export default content;
}

declare module '*.module.stylus' {
    const classes: { [key: string]: string };
    export default classes;
}

declare module '*.svg' {
    import * as React from 'react';
    export const ReactComponent: React.FunctionComponent<React.SVGProps<SVGSVGElement> & { title?: string }>;
    const src: string;
    export default src;
}

declare module '*.png' {
    const src: string;
    export default src;
}

declare module '*.jpg' {
    const src: string;
    export default src;
}

declare module '*.jpeg' {
    const src: string;
    export default src;
}

declare module '*.gif' {
    const src: string;
    export default src;
}

declare module '*.webp' {
    const src: string;
    export default src;
}

declare module '*.ico' {
    const src: string;
    export default src;
}

declare module '*.bmp' {
    const src: string;
    export default src;
}

declare module '*.tiff' {
    const src: string;
    export default src;
}

declare module '*.woff' {
    const src: string;
    export default src;
}

declare module '*.woff2' {
    const src: string;
    export default src;
}

declare module '*.eot' {
    const src: string;
    export default src;
}

declare module '*.ttf' {
    const src: string;
    export default src;
}

declare module '*.otf' {
    const src: string;
    export default src;
}

declare module '*.mp3' {
    const src: string;
    export default src;
}

declare module '*.wav' {
    const src: string;
    export default src;
}

declare module '*.ogg' {
    const src: string;
    export default src;
}

declare module '*.flac' {
    const src: string;
    export default src;
}

declare module '*.mp4' {
    const src: string;
    export default src;
}

declare module '*.webm' {
    const src: string;
    export default src;
}

declare module '*.avi' {
    const src: string;
    export default src;
}

declare module '*.mov' {
    const src: string;
    export default src;
}

declare module '*.wmv' {
    const src: string;
    export default src;
}

declare module '*.flv' {
    const src: string;
    export default src;
}

declare module '*.mkv' {
    const src: string;
    export default src;
}

declare module '*.json' {
    const value: any;
    export default value;
}

declare module '*.txt' {
    const content: string;
    export default content;
}

declare module '*.md' {
    const content: string;
    export default content;
}

declare module '*.yaml' {
    const content: any;
    export default content;
}

declare module '*.yml' {
    const content: any;
    export default content;
}

declare module '*.toml' {
    const content: any;
    export default content;
}

declare module '*.xml' {
    const content: string;
    export default content;
}

declare module '*.csv' {
    const content: string;
    export default content;
}

declare module '*.tsv' {
    const content: string;
    export default content;
}

declare module '*.wasm' {
    const initWasm: (options?: WebAssembly.Imports) => Promise<WebAssembly.Instance>;
    export default initWasm;
}

// 环境变量类型声明
interface ImportMetaEnv {
    readonly VITE_APP_TITLE: string;
    readonly VITE_API_URL: string;
    readonly VITE_ENV: string;
    readonly GEMINI_API_KEY: string;
    readonly API_KEY: string;
}

interface ImportMeta {
    readonly env: ImportMetaEnv;
}