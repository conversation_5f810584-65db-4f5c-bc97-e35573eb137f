/* NovaRay 启动器应用样式 */
:root {
    --launcher-bg: rgba(255, 255, 255, 0.95);
    --launcher-border: rgba(255, 255, 255, 0.2);
    --launcher-shadow: rgba(0, 0, 0, 0.1);
    --text-primary: #333;
    --text-secondary: #666;
}

.dark {
    --launcher-bg: rgba(30, 30, 30, 0.95);
    --launcher-border: rgba(255, 255, 255, 0.1);
    --launcher-shadow: rgba(0, 0, 0, 0.3);
    --text-primary: #fff;
    --text-secondary: #999;
}

/* 重置默认样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html,
body {
    height: 100%;
    overflow: hidden;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
        'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
        sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

#root {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 启动器应用容器 */
.launcher-app {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.launcher-app.hidden {
    opacity: 0;
    visibility: hidden;
}

.launcher-app.visible {
    opacity: 1;
    visibility: visible;
}

/* 启动器容器 */
.launcher-container {
    width: 100%;
    max-width: 800px;
    min-height: 800px;
    padding: 0;
    background: transparent;
    position: relative;
}

/* 防止文本选择 */
.launcher-container {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* 输入框允许文本选择 */
.launcher-container input {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
}

/* 滚动条全局样式 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
}

.dark ::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
}

.dark ::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* 焦点样式 */
*:focus {
    outline: none;
}

*:focus-visible {
    outline: 2px solid #007AFF;
    outline-offset: 2px;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .launcher-app {
        --launcher-bg: rgba(255, 255, 255, 1);
        --launcher-border: rgba(0, 0, 0, 0.5);
        --launcher-shadow: rgba(0, 0, 0, 0.5);
    }

    .dark .launcher-app {
        --launcher-bg: rgba(0, 0, 0, 1);
        --launcher-border: rgba(255, 255, 255, 0.5);
        --launcher-shadow: rgba(255, 255, 255, 0.5);
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {

    .launcher-app,
    .launcher-container,
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 响应式断点 */
@media (max-width: 800px) {
    .launcher-container {
        margin: 0 8px;
    }
}

@media (max-width: 480px) {
    .launcher-container {
        margin: 0 4px;
    }
}

/* 调试模式（开发时可启用） */
.debug-mode {
    border: 2px dashed #ff0000;
}

.debug-mode .launcher-container {
    border: 1px solid #00ff00;
}

/* 性能优化：GPU加速 */
.launcher-app {
    transform: translateZ(0);
    will-change: transform, opacity;
}

.launcher-container {
    transform: translateZ(0);
    will-change: transform;
}

/* 打印样式隐藏 */
@media print {
    .launcher-app {
        display: none !important;
    }
}

/* 辅助功能增强 */
@media (prefers-reduced-transparency: reduce) {
    .launcher-app {
        --launcher-bg: rgba(255, 255, 255, 1);
    }

    .dark .launcher-app {
        --launcher-bg: rgba(30, 30, 30, 1);
    }
}

/* 高 DPI 屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2),
(min-resolution: 192dpi) {
    .launcher-app {
        -webkit-font-smoothing: subpixel-antialiased;
    }
}