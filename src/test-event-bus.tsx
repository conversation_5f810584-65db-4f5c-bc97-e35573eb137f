/**
 * 事件总线测试页面
 * 独立的测试环境，专门用于测试事件总线功能
 */

import React from 'react';
import ReactDOM from 'react-dom/client';
import { EventBusDemo } from './components/EventBusDemo';

function TestEventBusApp() {
    return (
        <div className="test-app">
            <header className="test-header">
                <h1>🚀 NovaRay 事件总线测试</h1>
                <p>测试前端与后端的事件通信系统</p>
            </header>
            <main className="test-main">
                <EventBusDemo />
            </main>
            <footer className="test-footer">
                <p>按 F12 打开开发者工具查看事件日志</p>
            </footer>

            <style dangerouslySetInnerHTML={{
                __html: `
          .test-app {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON>I', <PERSON><PERSON>, sans-serif;
          }
          
          .test-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 20px;
            text-align: center;
            color: white;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
          }
          
          .test-header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 700;
          }
          
          .test-header p {
            margin: 10px 0 0;
            font-size: 1.2rem;
            opacity: 0.9;
          }
          
          .test-main {
            background: white;
            margin: 20px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
          }
          
          .test-footer {
            text-align: center;
            padding: 20px;
            color: white;
            opacity: 0.8;
          }
          
          .test-footer p {
            margin: 0;
            font-size: 0.9rem;
          }
        `
            }} />
        </div>
    );
}

function initializeTestApp() {
    const rootElement = document.getElementById('root');
    if (rootElement) {
        ReactDOM.createRoot(rootElement).render(
            <React.StrictMode>
                <TestEventBusApp />
            </React.StrictMode>
        );
    } else {
        console.error('Failed to find the root element.');
    }
}

if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeTestApp);
} else {
    initializeTestApp();
}