//! # 布局引擎类型定义
//! 
//! 定义与Rust后端布局引擎交互的TypeScript类型

export interface WindowBounds {
    x: number;
    y: number;
    width: number;
    height: number;
}

export interface ScreenInfo {
    width: number;
    height: number;
    scale_factor: number;
    workarea: WindowBounds;
}

export interface LayoutConstraints {
    min_window_width: number;
    min_window_height: number;
    max_window_width?: number;
    max_window_height?: number;
    window_gap: number;
    screen_margin: number;
}

export type EasingFunction =
    | 'Linear'
    | 'EaseInOut'
    | 'EaseOut'
    | 'EaseIn'
    | 'Bounce'
    | 'Elastic';

export interface LayoutAnimation {
    duration_ms: number;
    easing: EasingFunction;
    enabled: boolean;
}

export type SplitDirection = 'Horizontal' | 'Vertical' | 'Auto';

export interface LayoutTemplateConfig {
    primary_ratio: number;
    secondary_ratio: number;
    direction: SplitDirection;
    max_windows?: number;
    auto_balance: boolean;
}

export interface LayoutTemplate {
    id: string;
    name: string;
    description: string;
    layout_type: string;
    config: LayoutTemplateConfig;
    created_at: string;
    usage_count: number;
}

export interface LayoutTemplateRequest {
    id: string;
    name: string;
    description: string;
    layout_type: string;
    config: LayoutTemplateConfig;
}

export interface LayoutStats {
    calculation_time_ms: number;
    windows_processed: number;
    layout_efficiency: number;
    balance_score: number;
}

export interface LayoutResult {
    success: boolean;
    message: string;
    layout: Record<string, WindowBounds>;
    stats?: LayoutStats;
}

export interface TemplateListResponse {
    templates: LayoutTemplate[];
    total_count: number;
}

export interface PerformanceStatsResponse {
    stats: Record<string, LayoutStats>;
}

export interface AnimationFrameResponse {
    frame: Record<string, WindowBounds>;
    progress: number;
}

export interface ScreenInfoRequest {
    width: number;
    height: number;
    scale_factor: number;
    workarea: WindowBounds;
}

export interface LayoutConstraintsRequest {
    min_window_width: number;
    min_window_height: number;
    max_window_width?: number;
    max_window_height?: number;
    window_gap: number;
    screen_margin: number;
}

export interface LayoutAnimationRequest {
    duration_ms: number;
    easing: EasingFunction;
    enabled: boolean;
}

// 预设布局模板ID
export enum PresetTemplates {
    MainSide = 'main-side',
    ThreeColumn = 'three-column',
    GoldenRatio = 'golden-ratio',
    SingleWindow = 'single-window',
    VerticalSplit = 'vertical-split',
    AutoGrid = 'auto-grid'
}

// 布局引擎错误类型
export class LayoutEngineError extends Error {
    constructor(message: string) {
        super(message);
        this.name = 'LayoutEngineError';
    }
}

// 布局动画状态
export interface LayoutAnimationState {
    isAnimating: boolean;
    progress: number;
    startTime: number;
    duration: number;
    fromLayout: Record<string, WindowBounds>;
    toLayout: Record<string, WindowBounds>;
}

// 布局引擎配置
export interface LayoutEngineConfig {
    constraints: LayoutConstraints;
    animation: LayoutAnimation;
    screenInfo: ScreenInfo;
}

// 布局优化建议
export interface LayoutOptimizationSuggestion {
    type: 'efficiency' | 'balance' | 'spacing' | 'template';
    description: string;
    action: string;
    expectedImprovement: number;
}

// 布局分析结果
export interface LayoutAnalysis {
    efficiency: number;
    balance_score: number;
    suggestions: LayoutOptimizationSuggestion[];
    recommended_template?: string;
}