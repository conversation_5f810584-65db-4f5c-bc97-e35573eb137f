import React, { useEffect, useRef } from 'react';
import LauncherInterface from './components/LauncherInterface';
import { useLauncher } from './hooks/useLauncher';
import './App.css';

/**
 * NovaRay 启动器主应用
 * 实现紧凑的启动器风格界面，固定800像素宽度，动态高度
 */
const App: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const { launcherState, setLauncherSize } = useLauncher();

  // 处理高度变化
  const handleHeightChange = (height: number) => {
    // 通过Tauri命令调整窗口大小
    setLauncherSize(height);
  };

  // 监听主题变化
  useEffect(() => {
    const updateTheme = () => {
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      document.documentElement.classList.toggle('dark', prefersDark);
    };

    // 初始设置
    updateTheme();

    // 监听系统主题变化
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    mediaQuery.addEventListener('change', updateTheme);

    return () => {
      mediaQuery.removeEventListener('change', updateTheme);
    };
  }, []);

  // 处理全局键盘事件
  useEffect(() => {
    const handleGlobalKeyDown = (event: KeyboardEvent) => {
      // 阻止默认的窗口快捷键
      if (event.metaKey || event.ctrlKey) {
        switch (event.key) {
          case 'w':
          case 'q':
            event.preventDefault();
            break;
        }
      }
    };

    window.addEventListener('keydown', handleGlobalKeyDown);
    return () => {
      window.removeEventListener('keydown', handleGlobalKeyDown);
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className="launcher-app visible"
    >
      <div className="launcher-container">
        <LauncherInterface onHeightChange={handleHeightChange} />
      </div>
    </div>
  );
};

export default App;