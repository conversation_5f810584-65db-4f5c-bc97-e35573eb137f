import { Plugin, ListPlugin, SearchResultItem, PluginAction } from './types';
import { getSetting } from './settingsManager';
import { installedPlugins } from '../plugins';
import { handleStoreSpecialAction } from '../plugins/store';


let allPlugins: Plugin[] = [];

/**
 * Initializes the plugin registry.
 */
export const initializeRegistry = (): void => {
  allPlugins = installedPlugins;
  console.log('Plugin registry initialized.');
};

// --- TAURI INTEGRATION NOTE ---
// In a real app, these would be backend calls e.g., invoke('install_plugin', { id })
// The installation logic is now owned by the store plugin itself for simulation purposes.
const installPlugin = (pluginId: string): boolean => {
    // This is now a mock function. The real logic is inside the store plugin for simulation.
    // In a real app, this would trigger a backend event.
    console.log(`Simulating install of ${pluginId}. Refresh needed to see changes.`);
    return true;
}
const uninstallPlugin = (pluginId: string): boolean => {
    console.log(`Simulating uninstall of ${pluginId}. Refresh needed to see changes.`);
    return true;
}


export const getPlugins = (): Plugin[] => allPlugins;
export const getPluginsWithSettings = (): Plugin[] => allPlugins.filter(p => p.settings && p.settings.length > 0);

export const findPluginByKeyword = (keyword: string): Plugin | undefined => {
  const lowerKeyword = keyword.toLowerCase();
  return allPlugins.find(p => p.keyword.toLowerCase() === lowerKeyword);
};

export const search = (query: string): SearchResultItem[] => {
  const trimmedQuery = query.trim();
  if (!trimmedQuery) return [];

  // Meta-command for listing plugins
  if (trimmedQuery.toLowerCase() === '/plugins') {
      return allPlugins
        .filter(p => p.id !== 'system-tools') // Don't show system tools plugin
        .map(plugin => ({ type: 'plugin', plugin }));
  }

  const lowerQuery = trimmedQuery.toLowerCase();
  
  const actionResults: SearchResultItem[] = [];
  const matchedPluginIds = new Set<string>();

  // 1. Check for action matches
  for (const plugin of allPlugins) {
    if (plugin.actions) {
      for (const action of plugin.actions) {
        const regex = new RegExp(action.pattern, 'i');
        if (regex.test(query)) {
          actionResults.push({ type: 'action', action, plugin });
          matchedPluginIds.add(plugin.id);
        }
      }
    }
  }

  // 2. Check for plugin keyword/alias matches (if not already matched by an action)
  const pluginResults: SearchResultItem[] = [];
  for (const plugin of allPlugins) {
    if (matchedPluginIds.has(plugin.id)) continue;

    const keywords = [plugin.keyword, ...(plugin.aliases || [])].filter(Boolean);
    let matchFound = false;
    for (const keyword of keywords) {
        if (keyword && lowerQuery.startsWith(keyword.toLowerCase())) {
            matchFound = true;
            break;
        }
    }

    if (!matchFound && plugin.keyword) { // Only do general search for plugins with keywords
        matchFound = plugin.name.toLowerCase().includes(lowerQuery);
    }
    
    if (matchFound) {
        pluginResults.push({ type: 'plugin', plugin });
    }
  }

  return [...actionResults, ...pluginResults];
};


export const runListPluginAction = async (pluginId: string, input: string, action?: PluginAction): Promise<string> => {
  console.log(`Dispatching to plugin for id: ${pluginId}, input: ${input}, action: ${action?.id}`);
  
  const plugin = allPlugins.find(p => p.id === pluginId);

  if (!plugin) {
    return `Error: Unknown plugin ID "${pluginId}"`;
  }

  if (plugin.mode === 'list') {
    // We cast here because we know 'list' mode plugins will be of type ListPlugin
    const listPlugin = plugin as ListPlugin;
    try {
        const result = await listPlugin.run({
            input,
            // Provide a default action if none is passed, for agent-style queries
            action: action || { id: 'default-query', name: 'Default Query', pattern: '.*' },
            getSetting: (key: string) => getSetting(plugin.id, key),
        });
        return result;
    } catch(e) {
        const errorMessage = e instanceof Error ? e.message : String(e);
        console.error(`Error running plugin ${pluginId}:`, errorMessage);
        return `Error: Execution failed for ${plugin.name}: ${errorMessage}`;
    }
  }

  return `Error: Plugin "${plugin.name}" is not an executable list plugin or has no 'run' method.`;
};


// --- TAURI INTEGRATION NOTE ---
// This special action handler would not exist on the frontend.
// The frontend would only call `invoke('install_plugin')` etc. and then
// the backend would emit an event to the frontend to refresh its plugin list.
// For simulation, we delegate to the store plugin's handler.
export const handleSpecialAction = (action: { type: string, payload: any }): boolean => {
    switch(action.type) {
        case 'install-plugin':
        case 'uninstall-plugin':
            // The store plugin now owns the simulation logic for this
            return handleStoreSpecialAction(action);
        default:
            return false;
    }
}