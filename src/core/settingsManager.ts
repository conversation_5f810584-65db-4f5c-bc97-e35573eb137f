// --- <PERSON>AUR<PERSON> INTEGRATION NOTE ---
// This module currently uses localStorage for simplicity in a web environment.
// In a Tauri app, this would be replaced with calls to the Rust backend
// to read/write settings from a persistent file (e.g., settings.json or a database).
// Example: `invoke('set_setting', { pluginId, key, value })`

const SETTINGS_PREFIX = 'novaray:settings:';

/**
 * Gets a setting value for a specific plugin.
 * @param pluginId The unique ID of the plugin.
 * @param key The key of the setting.
 * @returns The stored value, or null if not found.
 */
export function getSetting(pluginId: string, key: string): any | null {
  try {
    const storedValue = localStorage.getItem(`${SETTINGS_PREFIX}${pluginId}:${key}`);
    if (storedValue === null) {
      return null;
    }
    return JSON.parse(storedValue);
  } catch (error) {
    console.error(`Error getting setting ${key} for plugin ${pluginId}:`, error);
    return null;
  }
}

/**
 * Sets a setting value for a specific plugin.
 * @param pluginId The unique ID of the plugin.
 * @param key The key of the setting.
 * @param value The value to store.
 */
export function setSetting(pluginId: string, key: string, value: any): void {
  try {
    const valueToStore = JSON.stringify(value);
    localStorage.setItem(`${SETTINGS_PREFIX}${pluginId}:${key}`, valueToStore);
  } catch (error) {
    console.error(`Error setting ${key} for plugin ${pluginId}:`, error);
  }
}
