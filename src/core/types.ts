// --- TAURI INTEGRATION NOTE ---
// These types define the structure of the `plugin.json` manifest file and the
// data contracts between the Rust backend and the frontend.

export interface PluginAction {
  id: string; // e.g., 'action-add-note'
  name: string; // e.g., 'Add a new note'
  pattern: string; // A regex string, e.g., "^(note|addnote)\\s+(.+)"
  description?: string;
  isInteractive?: boolean;
}

// Base interface for all plugins
export interface PluginBase {
  id: string;
  name:string;
  icon: string; // Can be a Lucide icon name OR an image URL
  description?: string;
  keyword: string;
  placeholderSuffix?: string;
  settings?: PluginSetting[]; // Optional settings declaration
  aliases?: string[]; // For the plugin itself, e.g., ['calc', 'math'] for Calculator
  actions?: PluginAction[];
}

export type ListPluginRunnerContext = {
  input: string;
  action: PluginAction;
  getSetting: (key: string) => any;
};

export type ListPluginRunner = (context: ListPluginRunnerContext) => Promise<string>;

// Represents a plugin that operates in "list" mode.
// Its action returns a list of items to be rendered by the main app.
export interface ListPlugin extends PluginBase {
  mode: 'list';
  run: ListPluginRunner;
}

// Represents a plugin that operates in "view" mode.
// It provides its own HTML/CSS/JS user interface.
export interface ViewPlugin extends PluginBase {
  mode: 'view';
}

// Union type for any kind of plugin
export type Plugin = ListPlugin | ViewPlugin;

/**
 * A search result can be either a plugin or a direct action.
 */
export interface PluginSearchResult {
  type: 'plugin';
  plugin: Plugin;
}

export interface ActionSearchResult {
  type: 'action';
  action: PluginAction;
  plugin: Plugin;
}

export type SearchResultItem = PluginSearchResult | ActionSearchResult;


/**
 * Extends the base Plugin interface with metadata for the plugin store.
 */
export type RemotePlugin = Plugin & {
  author: string;
  version: string;
  readme: string; // Markdown content
  screenshots?: string[]; // Array of image URLs for preview
};


// --- Settings Data Structures ---

/**
 * Defines the structure of a single setting item declared by a plugin.
 */
export interface PluginSetting {
  id: string; // e.g., 'apiKey', 'temperature'
  title: string; // e.g., 'API Key', 'Model Temperature'
  description?: string; // e.g., 'Your personal API key for this service.'
  type: 'string' | 'password' | 'boolean' | 'select';
  defaultValue?: any;
  options?: { label: string; value: string }[]; // For type 'select'
}


// --- List Mode Data Structures ---

/**
 * Defines an action to be taken when a ListItem is selected.
 */
export interface OnSelectAction {
  type: string; // e.g., 'copy', 'open-url', 'show-list'
  payload: any;
}

/**
 * Defines the preview pane content for a ListItem.
 */
export interface ListItemPreview {
  type: 'text' | 'markdown';
  content: string;
  author?: string;
  version?: string;
  screenshots?: string[];
}

/**
 * Represents a single item in a list returned by a list-mode plugin.
 */
export interface ListItem {
  id: string;
  icon: string;
  title: string;
  description?: string;
  preview?: ListItemPreview;
  onSelectAction: OnSelectAction;
  isInstalled?: boolean;
}

/**
 * The structure of a result returned by a list-mode plugin action.
 */
export interface ListModeResult {
  items: ListItem[];
}


// --- Plugin View Communication ---

/**
 * Defines the message structure for communication between the main app
 * and a plugin running in a Custom View (iframe).
 */
export interface PluginMessage {
  type: string;
  payload?: any;
}