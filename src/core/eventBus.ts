/**
 * 前端事件总线适配器
 * 连接前端 React 应用与后端 Tauri 事件系统
 */

import { invoke } from '@tauri-apps/api/core';
import { listen, UnlistenFn, Event } from '@tauri-apps/api/event';
import { useEffect, DependencyList } from 'react';

/**
 * 事件类型定义
 */
export interface EventMessage {
    id: string;
    timestamp: number;
    event_type: EventType;
    source: string;
    target?: string;
    metadata: Record<string, string>;
}

export type EventType =
    | { Plugin: PluginEvent }
    | { Window: WindowEvent }
    | { System: SystemEvent }
    | { UI: UIEvent }
    | { Custom: CustomEvent };

export interface PluginEvent {
    ExecutionComplete?: {
        plugin_id: string;
        action_id: string;
        result: string;
        success: boolean;
    };
    Installed?: {
        plugin_id: string;
        version: string;
    };
    Uninstalled?: {
        plugin_id: string;
    };
    ToggleEnabled?: {
        plugin_id: string;
        enabled: boolean;
    };
}

export interface WindowEvent {
    Created?: {
        window_id: string;
        window_type: string;
    };
    Closed?: {
        window_id: string;
    };
    VisibilityChanged?: {
        window_id: string;
        visible: boolean;
    };
    Focused?: {
        window_id: string;
    };
    Blurred?: {
        window_id: string;
    };
}

export interface SystemEvent {
    GlobalShortcut?: {
        shortcut: string;
    };
    ClipboardChanged?: {
        content: string;
    };
    FileChanged?: {
        path: string;
        event_type: string;
    };
    AppStarted?: null;
    AppExit?: null;
}

export interface UIEvent {
    InputChanged?: {
        value: string;
    };
    SearchResultsChanged?: {
        results: string[];
    };
    ThemeChanged?: {
        theme: string;
    };
    SettingsChanged?: {
        setting_key: string;
        new_value: string;
    };
}

export interface CustomEvent {
    event_name: string;
    payload: any;
    source: string;
    target?: string;
}

/**
 * 事件监听器类型
 */
export type EventListener<T = any> = (event: T) => void | Promise<void>;

/**
 * 前端事件总线类
 */
export class EventBus {
    private listeners: Map<string, Set<EventListener>> = new Map();
    private unlistenFunctions: Map<string, UnlistenFn> = new Map();

    /**
     * 发送自定义事件到后端
     */
    async emitEvent(
        eventName: string,
        payload: any,
        target?: string
    ): Promise<boolean> {
        try {
            return await invoke('emit_event', {
                eventName,
                payload,
                target,
            });
        } catch (error) {
            console.error('发送事件失败:', error);
            return false;
        }
    }

    /**
     * 发送系统事件到后端
     */
    async emitSystemEvent(
        eventType: string,
        payload: any
    ): Promise<boolean> {
        try {
            return await invoke('emit_system_event', {
                eventType,
                payload,
            });
        } catch (error) {
            console.error('发送系统事件失败:', error);
            return false;
        }
    }

    /**
     * 发送UI事件到后端
     */
    async emitUIEvent(
        eventType: string,
        payload: any
    ): Promise<boolean> {
        try {
            return await invoke('emit_ui_event', {
                eventType,
                payload,
            });
        } catch (error) {
            console.error('发送UI事件失败:', error);
            return false;
        }
    }

    /**
     * 发送插件事件到后端
     */
    async emitPluginEvent(
        eventType: string,
        payload: any
    ): Promise<boolean> {
        try {
            return await invoke('emit_plugin_event', {
                eventType,
                payload,
            });
        } catch (error) {
            console.error('发送插件事件失败:', error);
            return false;
        }
    }

    /**
     * 订阅事件
     */
    async subscribe<T>(
        eventPattern: string,
        listener: EventListener<T>
    ): Promise<string> {
        // 添加到本地监听器映射
        if (!this.listeners.has(eventPattern)) {
            this.listeners.set(eventPattern, new Set());
        }
        this.listeners.get(eventPattern)!.add(listener);

        // 如果是第一次订阅这个模式，创建 Tauri 监听器
        if (this.listeners.get(eventPattern)!.size === 1) {
            try {
                const unlisten = await listen(eventPattern, (event: Event<any>) => {
                    this.handleEvent(eventPattern, event.payload);
                });
                this.unlistenFunctions.set(eventPattern, unlisten);
            } catch (error) {
                console.error('订阅事件失败:', error);
            }
        }

        return `${eventPattern}_${Date.now()}`;
    }

    /**
     * 取消订阅事件
     */
    async unsubscribe(
        eventPattern: string,
        listener?: EventListener
    ): Promise<void> {
        const listeners = this.listeners.get(eventPattern);
        if (!listeners) return;

        if (listener) {
            listeners.delete(listener);
        } else {
            listeners.clear();
        }

        // 如果没有监听器了，取消 Tauri 监听
        if (listeners.size === 0) {
            const unlisten = this.unlistenFunctions.get(eventPattern);
            if (unlisten) {
                unlisten();
                this.unlistenFunctions.delete(eventPattern);
            }
            this.listeners.delete(eventPattern);
        }
    }

    /**
     * 处理接收到的事件
     */
    private handleEvent(eventPattern: string, eventData: any): void {
        const listeners = this.listeners.get(eventPattern);
        if (!listeners) return;

        listeners.forEach((listener) => {
            try {
                listener(eventData);
            } catch (error) {
                console.error('事件监听器执行失败:', error);
            }
        });
    }

    /**
     * 获取事件历史记录
     */
    async getEventHistory(limit?: number): Promise<EventMessage[]> {
        try {
            return await invoke('get_event_history', { limit });
        } catch (error) {
            console.error('获取事件历史失败:', error);
            return [];
        }
    }

    /**
     * 清空事件历史记录
     */
    async clearEventHistory(): Promise<boolean> {
        try {
            return await invoke('clear_event_history');
        } catch (error) {
            console.error('清空事件历史失败:', error);
            return false;
        }
    }

    /**
     * 发送输入变化事件
     */
    async onInputChanged(value: string): Promise<void> {
        await this.emitUIEvent('input_changed', { value });
    }

    /**
     * 发送搜索结果变化事件
     */
    async onSearchResultsChanged(results: string[]): Promise<void> {
        await this.emitUIEvent('search_results_changed', { results });
    }

    /**
     * 发送主题变化事件
     */
    async onThemeChanged(theme: string): Promise<void> {
        await this.emitUIEvent('theme_changed', { theme });
    }

    /**
     * 发送设置变化事件
     */
    async onSettingsChanged(settingKey: string, newValue: string): Promise<void> {
        await this.emitUIEvent('settings_changed', {
            setting_key: settingKey,
            new_value: newValue,
        });
    }

    /**
     * 发送插件执行完成事件
     */
    async onPluginExecutionComplete(
        pluginId: string,
        actionId: string,
        result: string,
        success: boolean
    ): Promise<void> {
        await this.emitPluginEvent('execution_complete', {
            plugin_id: pluginId,
            action_id: actionId,
            result,
            success,
        });
    }

    /**
     * 清理所有监听器
     */
    async cleanup(): Promise<void> {
        for (const unlisten of this.unlistenFunctions.values()) {
            unlisten();
        }
        this.unlistenFunctions.clear();
        this.listeners.clear();
    }
}

/**
 * 全局事件总线实例
 */
export const eventBus = new EventBus();

/**
 * React Hook 用于事件订阅
 */
export function useEventSubscription<T>(
    eventPattern: string,
    listener: EventListener<T>,
    deps: DependencyList = []
): void {
    useEffect(() => {
        let subscriptionId: string | null = null;

        const subscribe = async () => {
            subscriptionId = await eventBus.subscribe(eventPattern, listener);
        };

        subscribe();

        return () => {
            if (subscriptionId) {
                eventBus.unsubscribe(eventPattern, listener);
            }
        };
    }, deps);
}

/**
 * React Hook 用于发送事件
 */
export function useEventEmitter() {
    return {
        emitEvent: eventBus.emitEvent.bind(eventBus),
        emitSystemEvent: eventBus.emitSystemEvent.bind(eventBus),
        emitUIEvent: eventBus.emitUIEvent.bind(eventBus),
        emitPluginEvent: eventBus.emitPluginEvent.bind(eventBus),
        onInputChanged: eventBus.onInputChanged.bind(eventBus),
        onSearchResultsChanged: eventBus.onSearchResultsChanged.bind(eventBus),
        onThemeChanged: eventBus.onThemeChanged.bind(eventBus),
        onSettingsChanged: eventBus.onSettingsChanged.bind(eventBus),
        onPluginExecutionComplete: eventBus.onPluginExecutionComplete.bind(eventBus),
    };
}