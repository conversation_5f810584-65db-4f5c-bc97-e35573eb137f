//! # Event Bus Hook
//! 
//! 提供事件总线功能的React Hook

import { useCallback } from 'react';
import { eventBus } from '../core/eventBus';

export interface UseEventBusReturn {
    emit: (event: string, data?: any) => Promise<void>;
    subscribe: (event: string, callback: (data: any) => void) => void;
    unsubscribe: (event: string, callback: (data: any) => void) => void;
}

export const useEventBus = (): UseEventBusReturn => {
    const emit = useCallback(async (event: string, data?: any) => {
        try {
            await eventBus.emitEvent(event, data);
        } catch (err) {
            console.error(`发送事件 ${event} 失败:`, err);
        }
    }, []);

    const subscribe = useCallback((event: string, callback: (data: any) => void) => {
        eventBus.subscribe(event, callback);
    }, []);

    const unsubscribe = useCallback((event: string, callback: (data: any) => void) => {
        eventBus.unsubscribe(event, callback);
    }, []);

    return {
        emit,
        subscribe,
        unsubscribe
    };
};