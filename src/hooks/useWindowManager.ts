import { useState, useEffect, useCallback } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { eventBus } from '../core/eventBus';

export interface WindowState {
    window_id: string;
    title: string;
    size: [number, number];
    position: [number, number];
    is_visible: boolean;
    is_focused: boolean;
    is_minimized: boolean;
    is_maximized: boolean;
    is_always_on_top: boolean;
    z_order: number;
    created_at: string;
    last_updated: string;
}

export interface WindowInfo {
    id: string;
    title: string;
    url: string;
    state: WindowState;
}

export interface WindowStats {
    total_windows: number;
    active_windows: number;
    plugin_windows: number;
    focused_window: string | null;
    memory_usage: string;
    created_today: number;
}

export interface UseWindowManagerReturn {
    windows: WindowInfo[];
    stats: WindowStats | null;
    loading: boolean;
    error: string | null;

    // 基础窗口操作
    createWindow: (url: string, title: string, width?: number, height?: number) => Promise<boolean>;
    closeWindow: (windowId: string) => Promise<boolean>;
    focusWindow: (windowId: string) => Promise<boolean>;
    showWindow: (windowId: string) => Promise<boolean>;
    hideWindow: (windowId: string) => Promise<boolean>;
    toggleWindow: (windowId: string) => Promise<boolean>;

    // 窗口状态管理
    minimizeWindow: (windowId: string) => Promise<boolean>;
    maximizeWindow: (windowId: string) => Promise<boolean>;
    unmaximizeWindow: (windowId: string) => Promise<boolean>;
    resizeWindow: (windowId: string, width: number, height: number) => Promise<boolean>;
    moveWindow: (windowId: string, x: number, y: number) => Promise<boolean>;
    setAlwaysOnTop: (windowId: string, alwaysOnTop: boolean) => Promise<boolean>;

    // 批量操作
    closeAllPluginWindows: () => Promise<boolean>;
    syncAllWindowStates: () => Promise<boolean>;

    // 数据刷新
    refreshWindows: () => Promise<void>;
    refreshStats: () => Promise<void>;
}

export const useWindowManager = (): UseWindowManagerReturn => {
    const [windows, setWindows] = useState<WindowInfo[]>([]);
    const [stats, setStats] = useState<WindowStats | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    // 获取所有窗口
    const refreshWindows = useCallback(async () => {
        try {
            const windowList = await invoke<WindowInfo[]>('get_all_windows');
            setWindows(windowList);
            setError(null);
        } catch (err) {
            setError(err instanceof Error ? err.message : String(err));
            console.error('获取窗口列表失败:', err);
        }
    }, []);

    // 获取窗口统计
    const refreshStats = useCallback(async () => {
        try {
            const windowStats = await invoke<WindowStats>('get_window_stats');
            setStats(windowStats);
            setError(null);
        } catch (err) {
            setError(err instanceof Error ? err.message : String(err));
            console.error('获取窗口统计失败:', err);
        }
    }, []);

    // 创建窗口
    const createWindow = useCallback(async (url: string, title: string, width = 800, height = 600): Promise<boolean> => {
        try {
            const result = await invoke<string>('create_window', {
                url,
                title,
                width,
                height
            });

            const success = !!result; // 将字符串转换为boolean
            if (success) {
                await refreshWindows();
                await refreshStats();

                // 发送窗口创建事件
                await eventBus.emitEvent('window-created', {
                    url,
                    title,
                    size: [width, height],
                    windowId: result,
                    timestamp: Date.now()
                });
            }

            return success;
        } catch (err) {
            setError(err instanceof Error ? err.message : String(err));
            console.error('创建窗口失败:', err);
            return false;
        }
    }, [refreshWindows, refreshStats]);

    // 关闭窗口
    const closeWindow = useCallback(async (windowId: string): Promise<boolean> => {
        try {
            const result = await invoke<boolean>('close_plugin_window', { windowId });

            if (result) {
                await refreshWindows();
                await refreshStats();

                // 发送窗口关闭事件
                await eventBus.emitEvent('window-closed', {
                    windowId,
                    timestamp: Date.now()
                });
            }

            return result;
        } catch (err) {
            setError(err instanceof Error ? err.message : String(err));
            console.error('关闭窗口失败:', err);
            return false;
        }
    }, [refreshWindows, refreshStats]);

    // 聚焦窗口
    const focusWindow = useCallback(async (windowId: string): Promise<boolean> => {
        try {
            const result = await invoke<boolean>('focus_main_window', { windowId });

            if (result) {
                await refreshWindows();

                // 发送窗口聚焦事件
                await eventBus.emitEvent('window-focused', {
                    windowId,
                    timestamp: Date.now()
                });
            }

            return result;
        } catch (err) {
            setError(err instanceof Error ? err.message : String(err));
            console.error('聚焦窗口失败:', err);
            return false;
        }
    }, [refreshWindows]);

    // 显示窗口
    const showWindow = useCallback(async (windowId: string): Promise<boolean> => {
        try {
            const result = await invoke<boolean>('show_window', { windowId });

            if (result) {
                await refreshWindows();

                // 发送窗口显示事件
                await eventBus.emitEvent('window-shown', {
                    windowId,
                    timestamp: Date.now()
                });
            }

            return result;
        } catch (err) {
            setError(err instanceof Error ? err.message : String(err));
            console.error('显示窗口失败:', err);
            return false;
        }
    }, [refreshWindows]);

    // 隐藏窗口
    const hideWindow = useCallback(async (windowId: string): Promise<boolean> => {
        try {
            const result = await invoke<boolean>('hide_window', { windowId });

            if (result) {
                await refreshWindows();

                // 发送窗口隐藏事件
                await eventBus.emitEvent('window-hidden', {
                    windowId,
                    timestamp: Date.now()
                });
            }

            return result;
        } catch (err) {
            setError(err instanceof Error ? err.message : String(err));
            console.error('隐藏窗口失败:', err);
            return false;
        }
    }, [refreshWindows]);

    // 切换窗口显示状态
    const toggleWindow = useCallback(async (windowId: string): Promise<boolean> => {
        try {
            const result = await invoke<boolean>('toggle_window', { windowId });

            if (result) {
                await refreshWindows();

                // 发送窗口切换事件
                await eventBus.emitEvent('window-toggled', {
                    windowId,
                    timestamp: Date.now()
                });
            }

            return result;
        } catch (err) {
            setError(err instanceof Error ? err.message : String(err));
            console.error('切换窗口状态失败:', err);
            return false;
        }
    }, [refreshWindows]);

    // 最小化窗口
    const minimizeWindow = useCallback(async (windowId: string): Promise<boolean> => {
        try {
            const result = await invoke<boolean>('minimize_window', { windowId });

            if (result) {
                await refreshWindows();

                // 发送窗口最小化事件
                await eventBus.emitEvent('window-minimized', {
                    windowId,
                    timestamp: Date.now()
                });
            }

            return result;
        } catch (err) {
            setError(err instanceof Error ? err.message : String(err));
            console.error('最小化窗口失败:', err);
            return false;
        }
    }, [refreshWindows]);

    // 最大化窗口
    const maximizeWindow = useCallback(async (windowId: string): Promise<boolean> => {
        try {
            const result = await invoke<boolean>('maximize_window', { windowId });

            if (result) {
                await refreshWindows();

                // 发送窗口最大化事件
                await eventBus.emitEvent('window-maximized', {
                    windowId,
                    timestamp: Date.now()
                });
            }

            return result;
        } catch (err) {
            setError(err instanceof Error ? err.message : String(err));
            console.error('最大化窗口失败:', err);
            return false;
        }
    }, [refreshWindows]);

    // 取消最大化窗口
    const unmaximizeWindow = useCallback(async (windowId: string): Promise<boolean> => {
        try {
            const result = await invoke<boolean>('unmaximize_window', { windowId });

            if (result) {
                await refreshWindows();

                // 发送窗口取消最大化事件
                await eventBus.emitEvent('window-unmaximized', {
                    windowId,
                    timestamp: Date.now()
                });
            }

            return result;
        } catch (err) {
            setError(err instanceof Error ? err.message : String(err));
            console.error('取消最大化窗口失败:', err);
            return false;
        }
    }, [refreshWindows]);

    // 调整窗口大小
    const resizeWindow = useCallback(async (windowId: string, width: number, height: number): Promise<boolean> => {
        try {
            const result = await invoke<boolean>('resize_window', {
                windowId,
                width,
                height
            });

            if (result) {
                await refreshWindows();

                // 发送窗口调整大小事件
                await eventBus.emitEvent('window-resized', {
                    windowId,
                    size: [width, height],
                    timestamp: Date.now()
                });
            }

            return result;
        } catch (err) {
            setError(err instanceof Error ? err.message : String(err));
            console.error('调整窗口大小失败:', err);
            return false;
        }
    }, [refreshWindows]);

    // 移动窗口
    const moveWindow = useCallback(async (windowId: string, x: number, y: number): Promise<boolean> => {
        try {
            const result = await invoke<boolean>('move_window', {
                windowId,
                x,
                y
            });

            if (result) {
                await refreshWindows();

                // 发送窗口移动事件
                await eventBus.emitEvent('window-moved', {
                    windowId,
                    position: [x, y],
                    timestamp: Date.now()
                });
            }

            return result;
        } catch (err) {
            setError(err instanceof Error ? err.message : String(err));
            console.error('移动窗口失败:', err);
            return false;
        }
    }, [refreshWindows]);

    // 设置窗口置顶
    const setAlwaysOnTop = useCallback(async (windowId: string, alwaysOnTop: boolean): Promise<boolean> => {
        try {
            const result = await invoke<boolean>('set_always_on_top', {
                windowId,
                alwaysOnTop
            });

            if (result) {
                await refreshWindows();

                // 发送窗口置顶事件
                await eventBus.emitEvent('window-always-on-top-changed', {
                    windowId,
                    alwaysOnTop,
                    timestamp: Date.now()
                });
            }

            return result;
        } catch (err) {
            setError(err instanceof Error ? err.message : String(err));
            console.error('设置窗口置顶失败:', err);
            return false;
        }
    }, [refreshWindows]);

    // 关闭所有插件窗口
    const closeAllPluginWindows = useCallback(async (): Promise<boolean> => {
        try {
            const result = await invoke<boolean>('close_all_plugin_windows');

            if (result) {
                await refreshWindows();
                await refreshStats();

                // 发送批量关闭事件
                await eventBus.emitEvent('all-plugin-windows-closed', {
                    timestamp: Date.now()
                });
            }

            return result;
        } catch (err) {
            setError(err instanceof Error ? err.message : String(err));
            console.error('关闭所有插件窗口失败:', err);
            return false;
        }
    }, [refreshWindows, refreshStats]);

    // 同步所有窗口状态
    const syncAllWindowStates = useCallback(async (): Promise<boolean> => {
        try {
            const result = await invoke<boolean>('sync_all_window_states');

            if (result) {
                await refreshWindows();
                await refreshStats();

                // 发送同步完成事件
                await eventBus.emitEvent('window-states-synced', {
                    timestamp: Date.now()
                });
            }

            return result;
        } catch (err) {
            setError(err instanceof Error ? err.message : String(err));
            console.error('同步窗口状态失败:', err);
            return false;
        }
    }, [refreshWindows, refreshStats]);

    // 初始化和监听事件
    useEffect(() => {
        const initializeData = async () => {
            setLoading(true);
            try {
                await Promise.all([refreshWindows(), refreshStats()]);
            } finally {
                setLoading(false);
            }
        };

        initializeData();

        // 监听窗口事件
        const handleWindowEvent = (event: any) => {
            console.log('窗口事件:', event);

            // 根据事件类型刷新数据
            if (event.event_type === 'window_created' ||
                event.event_type === 'window_closed' ||
                event.event_type === 'window_focused' ||
                event.event_type === 'window_state_changed') {
                refreshWindows();
                refreshStats();
            }
        };

        // 订阅窗口事件
        eventBus.subscribe('window-event', handleWindowEvent);

        return () => {
            eventBus.unsubscribe('window-event', handleWindowEvent);
        };
    }, [refreshWindows, refreshStats]);

    return {
        windows,
        stats,
        loading,
        error,
        createWindow,
        closeWindow,
        focusWindow,
        showWindow,
        hideWindow,
        toggleWindow,
        minimizeWindow,
        maximizeWindow,
        unmaximizeWindow,
        resizeWindow,
        moveWindow,
        setAlwaysOnTop,
        closeAllPluginWindows,
        syncAllWindowStates,
        refreshWindows,
        refreshStats
    };
};