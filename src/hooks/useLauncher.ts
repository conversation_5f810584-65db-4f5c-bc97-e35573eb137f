import { useState, useEffect, useCallback } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { listen } from '@tauri-apps/api/event';

export interface LauncherState {
    isVisible: boolean;
    position: [number, number] | null;
    size: [number, number];
}

export interface PluginWindow {
    pluginId: string;
    isVisible: boolean;
}

/**
 * 启动器管理 Hook
 * 提供启动器窗口的显示/隐藏控制和状态管理
 */
export function useLauncher() {
    const [launcherState, setLauncherState] = useState<LauncherState>({
        isVisible: true, // 开发模式下默认显示
        position: null,
        size: [800, 80],
    });
    const [pluginWindows, setPluginWindows] = useState<PluginWindow[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    // 获取启动器状态
    const getLauncherState = useCallback(async () => {
        try {
            setError(null);
            const state = await invoke<{
                is_visible: boolean;
                position: [number, number] | null;
                size: [number, number];
            }>('get_launcher_state');

            setLauncherState({
                isVisible: state.is_visible,
                position: state.position,
                size: state.size,
            });
        } catch (err) {
            setError(`获取启动器状态失败: ${err}`);
        }
    }, []);

    // 切换启动器可见性
    const toggleLauncher = useCallback(async () => {
        try {
            setIsLoading(true);
            setError(null);
            const state = await invoke<{
                is_visible: boolean;
                position: [number, number] | null;
                size: [number, number];
            }>('toggle_launcher');

            setLauncherState({
                isVisible: state.is_visible,
                position: state.position,
                size: state.size,
            });
        } catch (err) {
            setError(`切换启动器失败: ${err}`);
        } finally {
            setIsLoading(false);
        }
    }, []);

    // 显示启动器
    const showLauncher = useCallback(async () => {
        try {
            setIsLoading(true);
            setError(null);
            const state = await invoke<{
                is_visible: boolean;
                position: [number, number] | null;
                size: [number, number];
            }>('show_launcher');

            setLauncherState({
                isVisible: state.is_visible,
                position: state.position,
                size: state.size,
            });
        } catch (err) {
            setError(`显示启动器失败: ${err}`);
        } finally {
            setIsLoading(false);
        }
    }, []);

    // 隐藏启动器
    const hideLauncher = useCallback(async () => {
        try {
            setIsLoading(true);
            setError(null);
            const state = await invoke<{
                is_visible: boolean;
                position: [number, number] | null;
                size: [number, number];
            }>('hide_launcher');

            setLauncherState({
                isVisible: state.is_visible,
                position: state.position,
                size: state.size,
            });
        } catch (err) {
            setError(`隐藏启动器失败: ${err}`);
        } finally {
            setIsLoading(false);
        }
    }, []);

    // 设置启动器大小（动态调整）
    const setLauncherSize = useCallback(async (height: number) => {
        try {
            setError(null);
            const state = await invoke<{
                is_visible: boolean;
                position: [number, number] | null;
                size: [number, number];
            }>('set_launcher_size', { height });

            setLauncherState({
                isVisible: state.is_visible,
                position: state.position,
                size: state.size,
            });
        } catch (err) {
            setError(`设置启动器大小失败: ${err}`);
        }
    }, []);

    // 处理启动器失焦
    const handleBlur = useCallback(async () => {
        try {
            await invoke('handle_launcher_blur');
        } catch (err) {
            console.warn('处理启动器失焦失败:', err);
        }
    }, []);

    // 创建插件窗口
    const createPluginWindow = useCallback(async (
        pluginId: string,
        title: string,
        url: string,
        width?: number,
        height?: number
    ) => {
        try {
            setError(null);
            const windowLabel = await invoke<string>('create_plugin_window', {
                pluginId,
                title,
                url,
                width,
                height,
            });

            // 更新插件窗口列表
            await getPluginWindows();
            return windowLabel;
        } catch (err) {
            setError(`创建插件窗口失败: ${err}`);
            throw err;
        }
    }, []);

    // 关闭插件窗口
    const closePluginWindow = useCallback(async (pluginId: string) => {
        try {
            setError(null);
            await invoke('close_plugin_window', { pluginId });

            // 更新插件窗口列表
            await getPluginWindows();
        } catch (err) {
            setError(`关闭插件窗口失败: ${err}`);
        }
    }, []);

    // 获取插件窗口列表
    const getPluginWindows = useCallback(async () => {
        try {
            const windows = await invoke<Array<[string, boolean]>>('get_plugin_windows');
            setPluginWindows(windows.map(([pluginId, isVisible]) => ({
                pluginId,
                isVisible,
            })));
        } catch (err) {
            console.warn('获取插件窗口列表失败:', err);
        }
    }, []);

    // 监听启动器事件
    useEffect(() => {
        const setupListeners = async () => {
            // 监听启动器显示事件
            const unlistenShown = await listen('launcher:shown', () => {
                setLauncherState(prev => ({ ...prev, isVisible: true }));
            });

            // 监听启动器隐藏事件
            const unlistenHidden = await listen('launcher:hidden', () => {
                setLauncherState(prev => ({ ...prev, isVisible: false }));
            });

            return () => {
                unlistenShown();
                unlistenHidden();
            };
        };

        let cleanup: (() => void) | undefined;
        setupListeners().then(cleanupFn => {
            cleanup = cleanupFn;
        });

        // 初始化状态
        getLauncherState();
        getPluginWindows();

        return () => {
            cleanup?.();
        };
    }, [getLauncherState, getPluginWindows]);

    return {
        // 状态
        launcherState,
        pluginWindows,
        isLoading,
        error,

        // 操作方法
        toggleLauncher,
        showLauncher,
        hideLauncher,
        setLauncherSize,
        handleBlur,
        createPluginWindow,
        closePluginWindow,
        getPluginWindows,
        getLauncherState,
    };
}