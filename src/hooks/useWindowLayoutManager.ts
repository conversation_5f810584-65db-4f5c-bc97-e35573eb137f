import { useState, useEffect, useCallback } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { eventBus } from '../core/eventBus';

export type LayoutType = 'Free' | 'Tiled' | 'Stacked' | 'Grid' | 'Tabbed';

export interface WindowLayoutInfo {
    window_id: string;
    layout_type: LayoutType;
    bounds: {
        x: number;
        y: number;
        width: number;
        height: number;
    };
    z_order: number;
    is_focused: boolean;
    grid_position?: {
        row: number;
        col: number;
    };
    tab_group?: string;
}

export interface LayoutStats {
    total_layouts: number;
    active_layout: LayoutType;
    windows_in_layout: number;
    focused_window: string | null;
    grid_size?: {
        rows: number;
        cols: number;
    };
    tab_groups?: string[];
}

export interface UseWindowLayoutManagerReturn {
    layouts: WindowLayoutInfo[];
    stats: LayoutStats | null;
    loading: boolean;
    error: string | null;

    // 布局操作
    setLayout: (layoutType: LayoutType) => Promise<boolean>;
    addWindowToLayout: (windowId: string, layoutType: LayoutType) => Promise<boolean>;
    removeWindowFromLayout: (windowId: string) => Promise<boolean>;
    arrangeWindows: (layoutType: LayoutType) => Promise<boolean>;

    // 窗口焦点管理
    focusNextWindow: () => Promise<boolean>;
    focusPreviousWindow: () => Promise<boolean>;
    focusWindowInDirection: (direction: 'up' | 'down' | 'left' | 'right') => Promise<boolean>;

    // 网格布局操作
    setGridSize: (rows: number, cols: number) => Promise<boolean>;
    moveWindowToGridPosition: (windowId: string, row: number, col: number) => Promise<boolean>;
    getGridLayout: () => Promise<WindowLayoutInfo[]>;

    // 标签组操作
    createTabGroup: (groupName: string) => Promise<boolean>;
    addWindowToTabGroup: (windowId: string, groupName: string) => Promise<boolean>;
    removeWindowFromTabGroup: (windowId: string) => Promise<boolean>;
    switchToTabGroup: (groupName: string) => Promise<boolean>;

    // 布局保存和恢复
    saveLayout: (layoutName: string) => Promise<boolean>;
    loadLayout: (layoutName: string) => Promise<boolean>;
    deleteLayout: (layoutName: string) => Promise<boolean>;
    listSavedLayouts: () => Promise<string[]>;

    // 数据刷新
    refreshLayouts: () => Promise<void>;
    refreshStats: () => Promise<void>;
}

export const useWindowLayoutManager = (): UseWindowLayoutManagerReturn => {
    const [layouts, setLayouts] = useState<WindowLayoutInfo[]>([]);
    const [stats, setStats] = useState<LayoutStats | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    // 获取所有布局信息
    const refreshLayouts = useCallback(async () => {
        try {
            const layoutList = await invoke<WindowLayoutInfo[]>('get_window_layouts');
            setLayouts(layoutList);
            setError(null);
        } catch (err) {
            setError(err instanceof Error ? err.message : String(err));
            console.error('获取布局信息失败:', err);
        }
    }, []);

    // 获取布局统计
    const refreshStats = useCallback(async () => {
        try {
            const layoutStats = await invoke<LayoutStats>('get_layout_stats');
            setStats(layoutStats);
            setError(null);
        } catch (err) {
            setError(err instanceof Error ? err.message : String(err));
            console.error('获取布局统计失败:', err);
        }
    }, []);

    // 设置布局类型
    const setLayout = useCallback(async (layoutType: LayoutType): Promise<boolean> => {
        try {
            const result = await invoke<boolean>('set_layout', { layoutType });

            if (result) {
                await refreshLayouts();
                await refreshStats();

                // 发送布局设置事件
                await eventBus.emitEvent('layout-set', {
                    layoutType,
                    timestamp: Date.now()
                });
            }

            return result;
        } catch (err) {
            setError(err instanceof Error ? err.message : String(err));
            console.error('设置布局失败:', err);
            return false;
        }
    }, [refreshLayouts, refreshStats]);

    // 添加窗口到布局
    const addWindowToLayout = useCallback(async (windowId: string, layoutType: LayoutType): Promise<boolean> => {
        try {
            const result = await invoke<boolean>('add_window_to_layout', {
                windowId,
                layoutType
            });

            if (result) {
                await refreshLayouts();
                await refreshStats();

                // 发送窗口添加到布局事件
                await eventBus.emitEvent('window-added-to-layout', {
                    windowId,
                    layoutType,
                    timestamp: Date.now()
                });
            }

            return result;
        } catch (err) {
            setError(err instanceof Error ? err.message : String(err));
            console.error('添加窗口到布局失败:', err);
            return false;
        }
    }, [refreshLayouts, refreshStats]);

    // 从布局中移除窗口
    const removeWindowFromLayout = useCallback(async (windowId: string): Promise<boolean> => {
        try {
            const result = await invoke<boolean>('remove_window_from_layout', { windowId });

            if (result) {
                await refreshLayouts();
                await refreshStats();

                // 发送窗口从布局移除事件
                await eventBus.emitEvent('window-removed-from-layout', {
                    windowId,
                    timestamp: Date.now()
                });
            }

            return result;
        } catch (err) {
            setError(err instanceof Error ? err.message : String(err));
            console.error('从布局中移除窗口失败:', err);
            return false;
        }
    }, [refreshLayouts, refreshStats]);

    // 重新排列窗口
    const arrangeWindows = useCallback(async (layoutType: LayoutType): Promise<boolean> => {
        try {
            const result = await invoke<boolean>('arrange_windows', { layoutType });

            if (result) {
                await refreshLayouts();

                // 发送窗口重新排列事件
                await eventBus.emitEvent('windows-arranged', {
                    layoutType,
                    timestamp: Date.now()
                });
            }

            return result;
        } catch (err) {
            setError(err instanceof Error ? err.message : String(err));
            console.error('重新排列窗口失败:', err);
            return false;
        }
    }, [refreshLayouts]);

    // 聚焦下一个窗口
    const focusNextWindow = useCallback(async (): Promise<boolean> => {
        try {
            const result = await invoke<boolean>('focus_next_window');

            if (result) {
                await refreshLayouts();

                // 发送焦点切换事件
                await eventBus.emitEvent('focus-next-window', {
                    timestamp: Date.now()
                });
            }

            return result;
        } catch (err) {
            setError(err instanceof Error ? err.message : String(err));
            console.error('聚焦下一个窗口失败:', err);
            return false;
        }
    }, [refreshLayouts]);

    // 聚焦上一个窗口
    const focusPreviousWindow = useCallback(async (): Promise<boolean> => {
        try {
            const result = await invoke<boolean>('focus_previous_window');

            if (result) {
                await refreshLayouts();

                // 发送焦点切换事件
                await eventBus.emitEvent('focus-previous-window', {
                    timestamp: Date.now()
                });
            }

            return result;
        } catch (err) {
            setError(err instanceof Error ? err.message : String(err));
            console.error('聚焦上一个窗口失败:', err);
            return false;
        }
    }, [refreshLayouts]);

    // 按方向聚焦窗口
    const focusWindowInDirection = useCallback(async (direction: 'up' | 'down' | 'left' | 'right'): Promise<boolean> => {
        try {
            const result = await invoke<boolean>('focus_window_in_direction', { direction });

            if (result) {
                await refreshLayouts();

                // 发送方向焦点切换事件
                await eventBus.emitEvent('focus-window-in-direction', {
                    direction,
                    timestamp: Date.now()
                });
            }

            return result;
        } catch (err) {
            setError(err instanceof Error ? err.message : String(err));
            console.error('按方向聚焦窗口失败:', err);
            return false;
        }
    }, [refreshLayouts]);

    // 设置网格大小
    const setGridSize = useCallback(async (rows: number, cols: number): Promise<boolean> => {
        try {
            const result = await invoke<boolean>('set_grid_size', { rows, cols });

            if (result) {
                await refreshLayouts();
                await refreshStats();

                // 发送网格大小设置事件
                await eventBus.emitEvent('grid-size-set', {
                    rows,
                    cols,
                    timestamp: Date.now()
                });
            }

            return result;
        } catch (err) {
            setError(err instanceof Error ? err.message : String(err));
            console.error('设置网格大小失败:', err);
            return false;
        }
    }, [refreshLayouts, refreshStats]);

    // 移动窗口到网格位置
    const moveWindowToGridPosition = useCallback(async (windowId: string, row: number, col: number): Promise<boolean> => {
        try {
            const result = await invoke<boolean>('move_window_to_grid_position', {
                windowId,
                row,
                col
            });

            if (result) {
                await refreshLayouts();

                // 发送窗口移动到网格位置事件
                await eventBus.emitEvent('window-moved-to-grid', {
                    windowId,
                    row,
                    col,
                    timestamp: Date.now()
                });
            }

            return result;
        } catch (err) {
            setError(err instanceof Error ? err.message : String(err));
            console.error('移动窗口到网格位置失败:', err);
            return false;
        }
    }, [refreshLayouts]);

    // 获取网格布局
    const getGridLayout = useCallback(async (): Promise<WindowLayoutInfo[]> => {
        try {
            const result = await invoke<WindowLayoutInfo[]>('get_grid_layout');
            return result;
        } catch (err) {
            setError(err instanceof Error ? err.message : String(err));
            console.error('获取网格布局失败:', err);
            return [];
        }
    }, []);

    // 创建标签组
    const createTabGroup = useCallback(async (groupName: string): Promise<boolean> => {
        try {
            const result = await invoke<boolean>('create_tab_group', { groupName });

            if (result) {
                await refreshStats();

                // 发送标签组创建事件
                await eventBus.emitEvent('tab-group-created', {
                    groupName,
                    timestamp: Date.now()
                });
            }

            return result;
        } catch (err) {
            setError(err instanceof Error ? err.message : String(err));
            console.error('创建标签组失败:', err);
            return false;
        }
    }, [refreshStats]);

    // 添加窗口到标签组
    const addWindowToTabGroup = useCallback(async (windowId: string, groupName: string): Promise<boolean> => {
        try {
            const result = await invoke<boolean>('add_window_to_tab_group', {
                windowId,
                groupName
            });

            if (result) {
                await refreshLayouts();

                // 发送窗口添加到标签组事件
                await eventBus.emitEvent('window-added-to-tab-group', {
                    windowId,
                    groupName,
                    timestamp: Date.now()
                });
            }

            return result;
        } catch (err) {
            setError(err instanceof Error ? err.message : String(err));
            console.error('添加窗口到标签组失败:', err);
            return false;
        }
    }, [refreshLayouts]);

    // 从标签组移除窗口
    const removeWindowFromTabGroup = useCallback(async (windowId: string): Promise<boolean> => {
        try {
            const result = await invoke<boolean>('remove_window_from_tab_group', { windowId });

            if (result) {
                await refreshLayouts();

                // 发送窗口从标签组移除事件
                await eventBus.emitEvent('window-removed-from-tab-group', {
                    windowId,
                    timestamp: Date.now()
                });
            }

            return result;
        } catch (err) {
            setError(err instanceof Error ? err.message : String(err));
            console.error('从标签组移除窗口失败:', err);
            return false;
        }
    }, [refreshLayouts]);

    // 切换到标签组
    const switchToTabGroup = useCallback(async (groupName: string): Promise<boolean> => {
        try {
            const result = await invoke<boolean>('switch_to_tab_group', { groupName });

            if (result) {
                await refreshLayouts();

                // 发送标签组切换事件
                await eventBus.emitEvent('tab-group-switched', {
                    groupName,
                    timestamp: Date.now()
                });
            }

            return result;
        } catch (err) {
            setError(err instanceof Error ? err.message : String(err));
            console.error('切换到标签组失败:', err);
            return false;
        }
    }, [refreshLayouts]);

    // 保存布局
    const saveLayout = useCallback(async (layoutName: string): Promise<boolean> => {
        try {
            const result = await invoke<boolean>('save_layout', { layoutName });

            if (result) {
                // 发送布局保存事件
                await eventBus.emitEvent('layout-saved', {
                    layoutName,
                    timestamp: Date.now()
                });
            }

            return result;
        } catch (err) {
            setError(err instanceof Error ? err.message : String(err));
            console.error('保存布局失败:', err);
            return false;
        }
    }, []);

    // 加载布局
    const loadLayout = useCallback(async (layoutName: string): Promise<boolean> => {
        try {
            const result = await invoke<boolean>('load_layout', { layoutName });

            if (result) {
                await refreshLayouts();
                await refreshStats();

                // 发送布局加载事件
                await eventBus.emitEvent('layout-loaded', {
                    layoutName,
                    timestamp: Date.now()
                });
            }

            return result;
        } catch (err) {
            setError(err instanceof Error ? err.message : String(err));
            console.error('加载布局失败:', err);
            return false;
        }
    }, [refreshLayouts, refreshStats]);

    // 删除布局
    const deleteLayout = useCallback(async (layoutName: string): Promise<boolean> => {
        try {
            const result = await invoke<boolean>('delete_layout', { layoutName });

            if (result) {
                // 发送布局删除事件
                await eventBus.emitEvent('layout-deleted', {
                    layoutName,
                    timestamp: Date.now()
                });
            }

            return result;
        } catch (err) {
            setError(err instanceof Error ? err.message : String(err));
            console.error('删除布局失败:', err);
            return false;
        }
    }, []);

    // 列出已保存的布局
    const listSavedLayouts = useCallback(async (): Promise<string[]> => {
        try {
            const result = await invoke<string[]>('list_saved_layouts');
            return result;
        } catch (err) {
            setError(err instanceof Error ? err.message : String(err));
            console.error('列出已保存布局失败:', err);
            return [];
        }
    }, []);

    // 初始化和监听事件
    useEffect(() => {
        const initializeData = async () => {
            setLoading(true);
            try {
                await Promise.all([refreshLayouts(), refreshStats()]);
            } finally {
                setLoading(false);
            }
        };

        initializeData();

        // 监听布局事件
        const handleLayoutEvent = (event: any) => {
            console.log('布局事件:', event);

            // 根据事件类型刷新数据
            if (event.event_type === 'layout_changed' ||
                event.event_type === 'window_layout_updated' ||
                event.event_type === 'focus_changed') {
                refreshLayouts();
                refreshStats();
            }
        };

        // 订阅布局事件
        eventBus.subscribe('layout-event', handleLayoutEvent);

        return () => {
            eventBus.unsubscribe('layout-event', handleLayoutEvent);
        };
    }, [refreshLayouts, refreshStats]);

    return {
        layouts,
        stats,
        loading,
        error,
        setLayout,
        addWindowToLayout,
        removeWindowFromLayout,
        arrangeWindows,
        focusNextWindow,
        focusPreviousWindow,
        focusWindowInDirection,
        setGridSize,
        moveWindowToGridPosition,
        getGridLayout,
        createTabGroup,
        addWindowToTabGroup,
        removeWindowFromTabGroup,
        switchToTabGroup,
        saveLayout,
        loadLayout,
        deleteLayout,
        listSavedLayouts,
        refreshLayouts,
        refreshStats
    };
};