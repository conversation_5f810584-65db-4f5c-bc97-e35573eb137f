import { useState, useEffect, useCallback } from 'react';
import { invoke } from '@tauri-apps/api/core';

// 性能指标类型
export interface PerformanceMetric {
    timestamp: string;
    metric_type: string;
    value: number;
    unit: string;
    metadata?: any;
}

// 系统快照类型
export interface SystemSnapshot {
    timestamp: string;
    cpu_usage: number;
    memory_used: number;
    memory_total: number;
    memory_usage_percent: number;
    process_memory: number;
    process_cpu: number;
    uptime: {
        secs: number;
        nanos: number;
    };
}

// 应用性能统计类型
export interface AppPerformanceStats {
    startup_time?: {
        secs: number;
        nanos: number;
    };
    total_commands_executed: number;
    average_command_response_time: number;
    total_events_processed: number;
    average_event_latency: number;
    plugins_loaded: number;
    total_plugin_load_time: {
        secs: number;
        nanos: number;
    };
}

export const usePerformanceMonitor = () => {
    const [isMonitoring, setIsMonitoring] = useState(false);
    const [systemSnapshot, setSystemSnapshot] = useState<SystemSnapshot | null>(null);
    const [appStats, setAppStats] = useState<AppPerformanceStats | null>(null);
    const [performanceHistory, setPerformanceHistory] = useState<PerformanceMetric[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    // 启动性能监控
    const startMonitoring = useCallback(async () => {
        try {
            setIsLoading(true);
            setError(null);
            await invoke('start_performance_monitoring');
            setIsMonitoring(true);
        } catch (err) {
            setError(err as string);
            console.error('启动性能监控失败:', err);
        } finally {
            setIsLoading(false);
        }
    }, []);

    // 停止性能监控
    const stopMonitoring = useCallback(async () => {
        try {
            setIsLoading(true);
            setError(null);
            await invoke('stop_performance_monitoring');
            setIsMonitoring(false);
        } catch (err) {
            setError(err as string);
            console.error('停止性能监控失败:', err);
        } finally {
            setIsLoading(false);
        }
    }, []);

    // 获取系统快照
    const getSystemSnapshot = useCallback(async () => {
        try {
            setError(null);
            const snapshot = await invoke<SystemSnapshot>('get_system_snapshot');
            setSystemSnapshot(snapshot);
            return snapshot;
        } catch (err) {
            setError(err as string);
            console.error('获取系统快照失败:', err);
            return null;
        }
    }, []);

    // 获取应用性能统计
    const getAppStats = useCallback(async () => {
        try {
            setError(null);
            const stats = await invoke<AppPerformanceStats>('get_app_performance_stats');
            setAppStats(stats);
            return stats;
        } catch (err) {
            setError(err as string);
            console.error('获取应用统计失败:', err);
            return null;
        }
    }, []);

    // 获取性能历史数据
    const getPerformanceHistory = useCallback(async () => {
        try {
            setError(null);
            const history = await invoke<PerformanceMetric[]>('get_performance_history');
            setPerformanceHistory(history);
            return history;
        } catch (err) {
            setError(err as string);
            console.error('获取性能历史失败:', err);
            return [];
        }
    }, []);

    // 根据类型获取指标
    const getMetricsByType = useCallback(async (metricType: string) => {
        try {
            setError(null);
            const metrics = await invoke<PerformanceMetric[]>('get_metrics_by_type', {
                metricType,
            });
            return metrics;
        } catch (err) {
            setError(err as string);
            console.error(`获取${metricType}指标失败:`, err);
            return [];
        }
    }, []);

    // 清除历史数据
    const clearHistory = useCallback(async () => {
        try {
            setError(null);
            await invoke('clear_performance_history');
            setPerformanceHistory([]);
        } catch (err) {
            setError(err as string);
            console.error('清除历史数据失败:', err);
        }
    }, []);

    // 导出性能数据
    const exportData = useCallback(async (format: 'json' | 'csv' = 'json') => {
        try {
            setError(null);
            const data = await invoke<string>('export_performance_data', { format });

            // 创建下载链接
            const blob = new Blob([data], {
                type: format === 'json' ? 'application/json' : 'text/csv'
            });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `performance_data_${new Date().toISOString().split('T')[0]}.${format}`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            return data;
        } catch (err) {
            setError(err as string);
            console.error('导出性能数据失败:', err);
            return null;
        }
    }, []);

    // 运行性能基准测试
    const runBenchmark = useCallback(async () => {
        try {
            setIsLoading(true);
            setError(null);
            const result = await invoke('run_performance_benchmark');
            return result;
        } catch (err) {
            setError(err as string);
            console.error('运行基准测试失败:', err);
            return null;
        } finally {
            setIsLoading(false);
        }
    }, []);

    // 获取监控状态
    const getMonitoringStatus = useCallback(async () => {
        try {
            const status = await invoke<boolean>('get_monitoring_status');
            setIsMonitoring(status);
            return status;
        } catch (err) {
            console.error('获取监控状态失败:', err);
            return false;
        }
    }, []);

    // 刷新所有数据
    const refreshAllData = useCallback(async () => {
        const promises = [
            getSystemSnapshot(),
            getAppStats(),
            getPerformanceHistory(),
        ];

        try {
            await Promise.allSettled(promises);
        } catch (err) {
            console.error('刷新数据失败:', err);
        }
    }, [getSystemSnapshot, getAppStats, getPerformanceHistory]);

    // 定期更新数据
    useEffect(() => {
        if (isMonitoring) {
            const interval = setInterval(() => {
                refreshAllData();
            }, 5000); // 每5秒更新一次

            return () => clearInterval(interval);
        }
    }, [isMonitoring, refreshAllData]);

    // 初始化时获取监控状态
    useEffect(() => {
        getMonitoringStatus();
    }, [getMonitoringStatus]);

    // 格式化持续时间
    const formatDuration = useCallback((duration: { secs: number; nanos: number }) => {
        const totalMs = duration.secs * 1000 + duration.nanos / 1000000;
        if (totalMs < 1000) {
            return `${totalMs.toFixed(1)}ms`;
        } else if (totalMs < 60000) {
            return `${(totalMs / 1000).toFixed(1)}s`;
        } else {
            return `${(totalMs / 60000).toFixed(1)}min`;
        }
    }, []);

    // 格式化内存大小
    const formatMemorySize = useCallback((bytes: number) => {
        const units = ['B', 'KB', 'MB', 'GB'];
        let size = bytes;
        let unitIndex = 0;

        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }

        return `${size.toFixed(1)} ${units[unitIndex]}`;
    }, []);

    // 获取CPU使用率颜色
    const getCpuUsageColor = useCallback((usage: number) => {
        if (usage < 30) return '#10b981'; // green
        if (usage < 70) return '#f59e0b'; // yellow
        return '#ef4444'; // red
    }, []);

    // 获取内存使用率颜色
    const getMemoryUsageColor = useCallback((usage: number) => {
        if (usage < 50) return '#10b981'; // green
        if (usage < 80) return '#f59e0b'; // yellow
        return '#ef4444'; // red
    }, []);

    return {
        // 状态
        isMonitoring,
        systemSnapshot,
        appStats,
        performanceHistory,
        isLoading,
        error,

        // 操作
        startMonitoring,
        stopMonitoring,
        getSystemSnapshot,
        getAppStats,
        getPerformanceHistory,
        getMetricsByType,
        clearHistory,
        exportData,
        runBenchmark,
        refreshAllData,

        // 工具函数
        formatDuration,
        formatMemorySize,
        getCpuUsageColor,
        getMemoryUsageColor,
    };
};