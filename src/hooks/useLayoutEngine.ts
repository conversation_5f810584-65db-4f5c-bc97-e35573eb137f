//! # 布局引擎React Hook
//! 
//! 提供与Rust后端布局引擎交互的React Hook

import { useState, useCallback, useEffect, useRef } from 'react';
import { invoke } from '@tauri-apps/api/core';
import {
    WindowBounds,
    ScreenInfo,
    LayoutConstraints,
    LayoutAnimation,
    LayoutTemplate,
    LayoutTemplateRequest,
    LayoutResult,
    TemplateListResponse,
    PerformanceStatsResponse,
    AnimationFrameResponse,
    ScreenInfoRequest,
    LayoutConstraintsRequest,
    LayoutAnimationRequest,
    LayoutEngineError,
    LayoutAnimationState,
    LayoutEngineConfig,
    PresetTemplates,
    EasingFunction
} from '../types/layoutEngine';

export interface UseLayoutEngineReturn {
    // 屏幕信息
    screenInfo: ScreenInfo | null;
    updateScreenInfo: (screenInfo: ScreenInfoRequest) => Promise<void>;

    // 布局约束
    constraints: LayoutConstraints | null;
    setConstraints: (constraints: LayoutConstraintsRequest) => Promise<void>;
    getConstraints: () => Promise<void>;

    // 布局动画
    animation: LayoutAnimation | null;
    setAnimation: (animation: LayoutAnimationRequest) => Promise<void>;
    getAnimation: () => Promise<void>;

    // 布局模板
    templates: LayoutTemplate[];
    getTemplates: () => Promise<void>;
    getPopularTemplates: (limit: number) => Promise<void>;
    createTemplate: (template: LayoutTemplateRequest) => Promise<void>;

    // 布局操作
    applyAutoTileLayout: (windowIds: string[]) => Promise<LayoutResult>;
    applyTemplate: (templateId: string, windowIds: string[]) => Promise<LayoutResult>;
    previewLayout: (templateId: string, windowIds: string[]) => Promise<LayoutResult>;
    optimizeLayout: (currentLayout: Record<string, WindowBounds>) => Promise<LayoutResult>;
    recommendLayout: (windowIds: string[]) => Promise<string>;

    // 动画相关
    animationState: LayoutAnimationState | null;
    startLayoutAnimation: (fromLayout: Record<string, WindowBounds>, toLayout: Record<string, WindowBounds>) => void;
    stopAnimation: () => void;

    // 性能统计
    performanceStats: Record<string, any>;
    getPerformanceStats: () => Promise<void>;
    cleanupPerformanceCache: () => Promise<void>;

    // 状态
    isLoading: boolean;
    error: string | null;
    clearError: () => void;
}

export function useLayoutEngine(): UseLayoutEngineReturn {
    // 状态管理
    const [screenInfo, setScreenInfo] = useState<ScreenInfo | null>(null);
    const [constraints, setConstraintsState] = useState<LayoutConstraints | null>(null);
    const [animation, setAnimationState] = useState<LayoutAnimation | null>(null);
    const [templates, setTemplates] = useState<LayoutTemplate[]>([]);
    const [animationState, setAnimationStateValue] = useState<LayoutAnimationState | null>(null);
    const [performanceStats, setPerformanceStats] = useState<Record<string, any>>({});
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    // 动画相关ref
    const animationRef = useRef<number | null>(null);
    const startTimeRef = useRef<number>(0);

    // 错误处理
    const handleError = useCallback((err: any) => {
        const message = err instanceof Error ? err.message : String(err);
        setError(message);
        console.error('布局引擎错误:', err);
    }, []);

    const clearError = useCallback(() => {
        setError(null);
    }, []);

    // 屏幕信息相关
    const updateScreenInfo = useCallback(async (screenInfoRequest: ScreenInfoRequest) => {
        try {
            setIsLoading(true);
            clearError();

            await invoke('update_screen_info', { screenInfo: screenInfoRequest });
            setScreenInfo(screenInfoRequest);
        } catch (err) {
            handleError(err);
        } finally {
            setIsLoading(false);
        }
    }, [handleError, clearError]);

    const getScreenInfo = useCallback(async () => {
        try {
            const info = await invoke<ScreenInfo>('get_screen_info');
            setScreenInfo(info);
        } catch (err) {
            handleError(err);
        }
    }, [handleError]);

    // 约束相关
    const setConstraints = useCallback(async (constraintsRequest: LayoutConstraintsRequest) => {
        try {
            setIsLoading(true);
            clearError();

            await invoke('set_layout_constraints', { constraintsRequest });
            setConstraintsState(constraintsRequest);
        } catch (err) {
            handleError(err);
        } finally {
            setIsLoading(false);
        }
    }, [handleError, clearError]);

    const getConstraints = useCallback(async () => {
        try {
            const constraints = await invoke<LayoutConstraints>('get_layout_constraints');
            setConstraintsState(constraints);
        } catch (err) {
            handleError(err);
        }
    }, [handleError]);

    // 动画相关
    const setAnimation = useCallback(async (animationRequest: LayoutAnimationRequest) => {
        try {
            setIsLoading(true);
            clearError();

            await invoke('set_layout_animation', { animationRequest });
            setAnimationState(animationRequest);
        } catch (err) {
            handleError(err);
        } finally {
            setIsLoading(false);
        }
    }, [handleError, clearError]);

    const getAnimation = useCallback(async () => {
        try {
            const animationConfig = await invoke<LayoutAnimationRequest>('get_layout_animation');
            setAnimationState(animationConfig);
        } catch (err) {
            handleError(err);
        }
    }, [handleError]);

    // 模板相关
    const getTemplates = useCallback(async () => {
        try {
            const response = await invoke<TemplateListResponse>('get_layout_templates');
            setTemplates(response.templates);
        } catch (err) {
            handleError(err);
        }
    }, [handleError]);

    const getPopularTemplates = useCallback(async (limit: number) => {
        try {
            const response = await invoke<TemplateListResponse>('get_popular_templates', { limit });
            setTemplates(response.templates);
        } catch (err) {
            handleError(err);
        }
    }, [handleError]);

    const createTemplate = useCallback(async (template: LayoutTemplateRequest) => {
        try {
            setIsLoading(true);
            clearError();

            await invoke('create_layout_template', { templateRequest: template });
            // 刷新模板列表
            await getTemplates();
        } catch (err) {
            handleError(err);
        } finally {
            setIsLoading(false);
        }
    }, [handleError, clearError, getTemplates]);

    // 布局操作
    const applyAutoTileLayout = useCallback(async (windowIds: string[]): Promise<LayoutResult> => {
        try {
            setIsLoading(true);
            clearError();

            const result = await invoke<LayoutResult>('apply_auto_tile_layout', { windowIds });
            return result;
        } catch (err) {
            handleError(err);
            throw new LayoutEngineError(err instanceof Error ? err.message : String(err));
        } finally {
            setIsLoading(false);
        }
    }, [handleError, clearError]);

    const applyTemplate = useCallback(async (templateId: string, windowIds: string[]): Promise<LayoutResult> => {
        try {
            setIsLoading(true);
            clearError();

            const result = await invoke<LayoutResult>('apply_layout_template', { templateId, windowIds });
            return result;
        } catch (err) {
            handleError(err);
            throw new LayoutEngineError(err instanceof Error ? err.message : String(err));
        } finally {
            setIsLoading(false);
        }
    }, [handleError, clearError]);

    const previewLayout = useCallback(async (templateId: string, windowIds: string[]): Promise<LayoutResult> => {
        try {
            const result = await invoke<LayoutResult>('preview_layout', { templateId, windowIds });
            return result;
        } catch (err) {
            handleError(err);
            throw new LayoutEngineError(err instanceof Error ? err.message : String(err));
        }
    }, [handleError]);

    const optimizeLayout = useCallback(async (currentLayout: Record<string, WindowBounds>): Promise<LayoutResult> => {
        try {
            setIsLoading(true);
            clearError();

            const result = await invoke<LayoutResult>('optimize_current_layout', { currentLayout });
            return result;
        } catch (err) {
            handleError(err);
            throw new LayoutEngineError(err instanceof Error ? err.message : String(err));
        } finally {
            setIsLoading(false);
        }
    }, [handleError, clearError]);

    const recommendLayout = useCallback(async (windowIds: string[]): Promise<string> => {
        try {
            const recommendation = await invoke<string>('recommend_layout', { windowIds });
            return recommendation;
        } catch (err) {
            handleError(err);
            throw new LayoutEngineError(err instanceof Error ? err.message : String(err));
        }
    }, [handleError]);

    // 动画相关功能
    const calculateAnimationFrame = useCallback(async (
        fromLayout: Record<string, WindowBounds>,
        toLayout: Record<string, WindowBounds>,
        progress: number
    ): Promise<AnimationFrameResponse> => {
        try {
            const frame = await invoke<AnimationFrameResponse>('calculate_animation_frame', {
                fromLayout,
                toLayout,
                progress
            });
            return frame;
        } catch (err) {
            handleError(err);
            throw new LayoutEngineError(err instanceof Error ? err.message : String(err));
        }
    }, [handleError]);

    const startLayoutAnimation = useCallback((
        fromLayout: Record<string, WindowBounds>,
        toLayout: Record<string, WindowBounds>
    ) => {
        if (!animation?.enabled) return;

        const startTime = performance.now();
        startTimeRef.current = startTime;

        setAnimationStateValue({
            isAnimating: true,
            progress: 0,
            startTime,
            duration: animation.duration_ms,
            fromLayout,
            toLayout
        });

        const animate = async (currentTime: number) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / animation.duration_ms, 1);

            try {
                const frameResponse = await calculateAnimationFrame(fromLayout, toLayout, progress);

                setAnimationStateValue(prev => prev ? {
                    ...prev,
                    progress,
                    frame: frameResponse.frame
                } : null);

                if (progress < 1) {
                    animationRef.current = requestAnimationFrame(animate);
                } else {
                    setAnimationStateValue(null);
                }
            } catch (err) {
                console.error('动画帧计算失败:', err);
                setAnimationStateValue(null);
            }
        };

        animationRef.current = requestAnimationFrame(animate);
    }, [animation, calculateAnimationFrame]);

    const stopAnimation = useCallback(() => {
        if (animationRef.current) {
            cancelAnimationFrame(animationRef.current);
            animationRef.current = null;
        }
        setAnimationStateValue(null);
    }, []);

    // 性能统计
    const getPerformanceStats = useCallback(async () => {
        try {
            const stats = await invoke<PerformanceStatsResponse>('get_layout_performance_stats');
            setPerformanceStats(stats.stats);
        } catch (err) {
            handleError(err);
        }
    }, [handleError]);

    const cleanupPerformanceCache = useCallback(async () => {
        try {
            await invoke('cleanup_performance_cache');
            setPerformanceStats({});
        } catch (err) {
            handleError(err);
        }
    }, [handleError]);

    // 初始化时获取当前配置
    useEffect(() => {
        const initialize = async () => {
            try {
                await Promise.all([
                    getScreenInfo(),
                    getConstraints(),
                    getAnimation(),
                    getTemplates()
                ]);
            } catch (err) {
                console.error('布局引擎初始化失败:', err);
            }
        };

        initialize();
    }, [getScreenInfo, getConstraints, getAnimation, getTemplates]);

    // 组件卸载时清理动画
    useEffect(() => {
        return () => {
            stopAnimation();
        };
    }, [stopAnimation]);

    return {
        // 屏幕信息
        screenInfo,
        updateScreenInfo,

        // 布局约束
        constraints,
        setConstraints,
        getConstraints,

        // 布局动画
        animation,
        setAnimation,
        getAnimation,

        // 布局模板
        templates,
        getTemplates,
        getPopularTemplates,
        createTemplate,

        // 布局操作
        applyAutoTileLayout,
        applyTemplate,
        previewLayout,
        optimizeLayout,
        recommendLayout,

        // 动画相关
        animationState,
        startLayoutAnimation,
        stopAnimation,

        // 性能统计
        performanceStats,
        getPerformanceStats,
        cleanupPerformanceCache,

        // 状态
        isLoading,
        error,
        clearError
    };
}

// 导出预设模板常量
export { PresetTemplates };

// 导出默认配置
export const defaultLayoutConstraints: LayoutConstraints = {
    min_window_width: 300,
    min_window_height: 200,
    window_gap: 8,
    screen_margin: 16
};

export const defaultLayoutAnimation: LayoutAnimation = {
    duration_ms: 300,
    easing: 'EaseInOut',
    enabled: true
};

// 辅助函数
export const createLayoutTemplate = (
    id: string,
    name: string,
    description: string,
    config: Partial<LayoutTemplateRequest['config']> = {}
): LayoutTemplateRequest => ({
    id,
    name,
    description,
    layout_type: 'Tiled',
    config: {
        primary_ratio: 0.6,
        secondary_ratio: 0.4,
        direction: 'Auto',
        auto_balance: true,
        ...config
    }
});