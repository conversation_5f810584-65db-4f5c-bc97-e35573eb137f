{"name": "launcher", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build"}, "dependencies": {"@google/genai": "^1.6.0", "@tauri-apps/api": "^2.6.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "lucide-react": "^0.523.0", "react": "^19.1.0", "react-dom": "^19.1.0", "sass": "^1.89.2"}, "devDependencies": {"@types/node": "^22.14.0", "typescript": "~5.7.2", "vite": "^6.2.0"}}